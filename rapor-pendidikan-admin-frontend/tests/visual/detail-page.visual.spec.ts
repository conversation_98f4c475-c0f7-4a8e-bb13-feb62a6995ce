import { connectXray } from '@/helpers/global.helper'
import { deleteTableRowsButFirst } from '@/helpers/table.helper'
import { STAGING_BASE_URL } from '@/utils/env.util'
import { setViewportAsDesktop } from '@/utils/setup'
import { expect, test } from '@playwright/test'

const URL_LIST_PAGE_PREFIX = `${STAGING_BASE_URL}/pengaturan-akses`

test.beforeEach(async ({ page }) => {
  setViewportAsDesktop(page)
})

test('@RPNG-1787 @visual @staging should match the detail satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1787' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-satuan-pendidikan-users"]')

  const detailLink = page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-detail-button"]')

  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-satuan-pendidikan-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-satuan-pendidikan-user"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for email and name section
  locators.push(page.locator('[data-testid="user-email-text"]'))
  locators.push(page.locator('[data-testid="user-name-text"]'))

  // add locators for 5 columns
  locators.push(page.locator('[data-testid="user-detail-tbody-0-column-npsn"]'))
  locators.push(page.locator('[data-testid="user-detail-tbody-0-column-name"]'))
  locators.push(page.locator('[data-testid="user-detail-tbody-0-column-is-satu-atap"]'))
  locators.push(page.locator('[data-testid="user-detail-tbody-0-column-reason"]'))
  locators.push(page.locator('[data-testid="user-detail-tbody-0-column-status"]'))

  // add locators for activate/nonactivate button
  locators.push(page.locator('[data-testid="user-detail-tbody-0-change-status-button"]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})

test('@RPNG-3537 @visual @staging should match "Lihat Riwayat Akses" Satpen page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3537' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-satuan-pendidikan-users"]')

  const detailLink = page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-detail-button"]')

  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-satuan-pendidikan-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-satuan-pendidikan-user"]')

  // add locators for activate button
  const detailriwayat = page.locator('[data-testid="action-log-user-trigger-button"]')
  await detailriwayat.click()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-1788 @visual @staging should match the detail dinas kabupaten kota user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1788' })
  const LIST_PAGE_DINAS_KABKOT_URL = `${URL_LIST_PAGE_PREFIX}/dinas-kabupaten-kota`
  await page.goto(LIST_PAGE_DINAS_KABKOT_URL)
  await page.waitForURL(LIST_PAGE_DINAS_KABKOT_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')

  const detailLink = page.locator('[data-testid="user-list-daerah-row-0-detail-button"]')
  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-daerah-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-daerah-user"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for email and name section
  locators.push(page.locator('[data-testid="user-email-text"]'))
  locators.push(page.locator('[data-testid="user-name-text"]'))

  // add locators for 4 columns
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-name"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-letter-number"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-reason"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-status"]'))

  // add locators for activate/nonactivate button
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-change-status-button"]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})

test('@RPNG-3538 @visual @staging should match "Lihat Riwayat Akses" Kabkota page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3538' })
  const LIST_PAGE_DINAS_KABKOT_URL = `${URL_LIST_PAGE_PREFIX}/dinas-kabupaten-kota`
  await page.goto(LIST_PAGE_DINAS_KABKOT_URL)
  await page.waitForURL(LIST_PAGE_DINAS_KABKOT_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')

  const detailLink = page.locator('[data-testid="user-list-daerah-row-0-detail-button"]')
  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-daerah-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-daerah-user"]')

  const detailriwayat = page.locator('[data-testid="action-log-user-trigger-button"]')
  await detailriwayat.click()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-1789 @visual @staging should match the detail dinas provinsi user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1789' })
  const LIST_PAGE_DINAS_PROV_URL = `${URL_LIST_PAGE_PREFIX}/dinas-provinsi`
  await page.goto(LIST_PAGE_DINAS_PROV_URL)
  await page.waitForURL(LIST_PAGE_DINAS_PROV_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')

  const detailLink = page.locator('[data-testid="user-list-daerah-row-0-detail-button"]')
  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-daerah-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-daerah-user"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for email and name section
  locators.push(page.locator('[data-testid="user-email-text"]'))
  locators.push(page.locator('[data-testid="user-name-text"]'))

  // add locators for 5 columns
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-name"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-letter-number"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-is-eligible-all-city"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-reason"]'))
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-column-status"]'))

  // add locators for activate/nonactivate button
  locators.push(page.locator('[data-testid="user-detail-daerah-tbody-0-change-status-button"]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})

test('@RPNG-3539 @visual @staging should match "Lihat Riwayat Akses" Provinsi page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3539' })
  const LIST_PAGE_DINAS_PROV_URL = `${URL_LIST_PAGE_PREFIX}/dinas-provinsi`
  await page.goto(LIST_PAGE_DINAS_PROV_URL)
  await page.waitForURL(LIST_PAGE_DINAS_PROV_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')

  const detailLink = page.locator('[data-testid="user-list-daerah-row-0-detail-button"]')
  const href = await detailLink.getAttribute('href')
  await detailLink.click()
  const link = `${STAGING_BASE_URL}${href}`
  await page.waitForURL(link)
  // wait until table detail user displayed
  await page.waitForSelector('[data-testid="table-detail-daerah-user"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-detail-daerah-user"]')

  const detailriwayat = page.locator('[data-testid="action-log-user-trigger-button"]')
  await detailriwayat.click()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})
