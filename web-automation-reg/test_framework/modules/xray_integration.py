#!/usr/bin/env python3
"""
Xray Integration Module for Web Automation Reg
Handles test result updates to Jira Xray without changing existing test flows
"""

import os
import json
import requests
import base64
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class XrayTestResult:
    """Data structure for Xray test result"""
    test_key: str
    status: str  # PASS, FAIL, EXECUTING, TODO
    environment: str
    execution_time: float
    error_message: str = ""
    steps: List[Dict] = None
    executed_on: str = None

class XrayIntegration:
    """Handles integration with Jira Xray for test execution updates"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Xray integration
        
        Args:
            config: Configuration dictionary with Xray settings
        """
        self.config = config or self._load_config()
        self.base_url = self.config.get('base_url', 'https://wartek.atlassian.net')
        self.username = self.config.get('username', os.getenv('XRAY_USERNAME'))
        self.password = self.config.get('password', os.getenv('XRAY_PASSWORD'))
        self.project_key = self.config.get('project_key', os.getenv('XRAY_PROJECT_KEY', 'RP'))
        self.enabled = self.config.get('enabled', os.getenv('XRAY_ENABLED', 'true').lower() == 'true')
        
        # Authentication
        self.auth_header = None
        if self.username and self.password:
            credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            self.auth_header = f"Basic {credentials}"
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables and config files"""
        config = {
            'enabled': os.getenv('XRAY_ENABLED', 'true').lower() == 'true',
            'base_url': os.getenv('XRAY_BASE_URL', 'https://wartek.atlassian.net'),
            'username': os.getenv('XRAY_USERNAME'),
            'password': os.getenv('XRAY_PASSWORD'),
            'project_key': os.getenv('XRAY_PROJECT_KEY', 'RP'),
            'test_execution_key': os.getenv('XRAY_TEST_EXECUTION_KEY'),
            'test_plan_key': os.getenv('XRAY_TEST_PLAN_KEY'),
        }
        
        # Try to load from config file if exists
        config_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'xray_config.yaml')
        if os.path.exists(config_file):
            try:
                import yaml
                with open(config_file, 'r') as f:
                    file_config = yaml.safe_load(f)
                    config.update(file_config.get('xray', {}))
            except Exception as e:
                logger.warning(f"Could not load config file {config_file}: {e}")
        
        return config
    
    def is_enabled(self) -> bool:
        """Check if Xray integration is enabled"""
        return self.enabled and self.auth_header is not None
    
    def extract_test_key_from_tags(self, tags: List[str]) -> Optional[str]:
        """
        Extract test key from Gherkin tags
        
        Args:
            tags: List of tags from scenario (e.g., ['@TEST_RP-932', '@staging'])
            
        Returns:
            Test key (e.g., 'RP-932') or None if not found
        """
        for tag in tags:
            if tag.startswith('@TEST_'):
                return tag[6:]  # Remove '@TEST_' prefix
        return None
    
    def extract_environment_from_tags(self, tags: List[str]) -> str:
        """
        Extract environment from Gherkin tags
        
        Args:
            tags: List of tags from scenario
            
        Returns:
            Environment name ('staging' or 'production')
        """
        for tag in tags:
            if tag in ['@staging', '@production']:
                return tag[1:]  # Remove '@' prefix
        return 'staging'  # Default to staging
    
    def convert_status_to_xray(self, status: str) -> str:
        """
        Convert test execution status to Xray format
        
        Args:
            status: Test status ('passed', 'failed', 'skipped', 'error')
            
        Returns:
            Xray status ('PASS', 'FAIL', 'TODO')
        """
        status_mapping = {
            'passed': 'PASS',
            'failed': 'FAIL',
            'skipped': 'TODO',
            'error': 'FAIL'
        }
        return status_mapping.get(status.lower(), 'FAIL')
    
    def update_test_execution(self, test_results: List[XrayTestResult]) -> bool:
        """
        Update test execution results in Xray
        
        Args:
            test_results: List of test results to update
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_enabled():
            logger.info("Xray integration is disabled or not configured")
            return False
        
        if not test_results:
            logger.info("No test results to update")
            return True
        
        try:
            # Group results by environment
            results_by_env = {}
            for result in test_results:
                env = result.environment
                if env not in results_by_env:
                    results_by_env[env] = []
                results_by_env[env].append(result)
            
            # Update each environment separately
            success = True
            for env, env_results in results_by_env.items():
                if not self._update_environment_results(env, env_results):
                    success = False
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating test execution in Xray: {e}")
            return False
    
    def _update_environment_results(self, environment: str, test_results: List[XrayTestResult]) -> bool:
        """
        Update test results for a specific environment
        
        Args:
            environment: Environment name ('staging' or 'production')
            test_results: List of test results for this environment
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Prepare the test execution data
            execution_data = {
                "info": {
                    "summary": f"Automated Test Execution - {environment.title()} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "description": f"Automated test execution results for {environment} environment",
                    "version": "1.0",
                    "user": self.username,
                    "revision": os.getenv('CI_COMMIT_SHA', 'local'),
                    "startDate": datetime.now().isoformat(),
                    "finishDate": datetime.now().isoformat(),
                    "testPlanKey": self.config.get('test_plan_key'),
                    "testEnvironments": [environment.upper()]
                },
                "tests": []
            }
            
            # Add test results
            for result in test_results:
                test_data = {
                    "testKey": result.test_key,
                    "start": result.executed_on or datetime.now().isoformat(),
                    "finish": result.executed_on or datetime.now().isoformat(),
                    "comment": f"Executed in {environment} environment",
                    "status": result.status
                }
                
                # Add error details if test failed
                if result.status == 'FAIL' and result.error_message:
                    test_data["comment"] += f"\n\nError: {result.error_message}"
                
                # Add step details if available
                if result.steps:
                    test_data["steps"] = []
                    for i, step in enumerate(result.steps, 1):
                        step_status = 'PASS' if step.get('status') == 'passed' else 'FAIL'
                        test_data["steps"].append({
                            "status": step_status,
                            "comment": step.get('text', f'Step {i}'),
                            "actualResult": step.get('result', '')
                        })
                
                execution_data["tests"].append(test_data)
            
            # Send to Xray
            return self._send_execution_to_xray(execution_data)
            
        except Exception as e:
            logger.error(f"Error updating {environment} results: {e}")
            return False
    
    def _send_execution_to_xray(self, execution_data: Dict[str, Any]) -> bool:
        """
        Send execution data to Xray API
        
        Args:
            execution_data: Test execution data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/rest/raven/1.0/import/execution"
            headers = {
                'Authorization': self.auth_header,
                'Content-Type': 'application/json'
            }
            
            logger.info(f"Sending test execution to Xray: {len(execution_data['tests'])} tests")
            
            response = requests.post(url, json=execution_data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Successfully updated Xray test execution: {result.get('key', 'Unknown')}")
                return True
            else:
                logger.error(f"Failed to update Xray: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending execution to Xray: {e}")
            return False
    
    def update_single_test(self, test_key: str, status: str, environment: str, 
                          execution_time: float = 0, error_message: str = "") -> bool:
        """
        Update a single test result in Xray
        
        Args:
            test_key: Test key (e.g., 'RP-932')
            status: Test status ('passed', 'failed', 'skipped', 'error')
            environment: Environment ('staging' or 'production')
            execution_time: Test execution time in seconds
            error_message: Error message if test failed
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_enabled():
            return False
        
        xray_result = XrayTestResult(
            test_key=test_key,
            status=self.convert_status_to_xray(status),
            environment=environment,
            execution_time=execution_time,
            error_message=error_message,
            executed_on=datetime.now().isoformat()
        )
        
        return self.update_test_execution([xray_result])
