import { connectXray, screenshot } from '@/helpers/global.helper'
import { expectInLoginPage, goToBuktiKaryaPage } from '@/helpers/test.helper'
import { expect, test } from '@playwright/test'

test.describe('Anonymous <PERSON><PERSON><PERSON>', () => {
  // https://wartek.atlassian.net/browse/SHIP-2898
  test('@SHIP-EXAMPLE2898 @production @anonym Redirect to Login page after tap "tambah karya" on Eksplorasi page', async ({
    page,
  }, testInfo) => {
    connectXray(testInfo, { key: 'SHIP-2898' })

    await test.step('Given guru is on home page with anonymous user', async () => {
      await goToBuktiKaryaPage(page)
    })

    await test.step('And guru follow coach mark', async () => {
      await page.getByRole('button', { name: '<PERSON>n<PERSON><PERSON>' }).click()
      await page.getByRole('button', { name: '<PERSON>n<PERSON><PERSON>' }).click()
      await page.getByRole('button', { name: '<PERSON><PERSON><PERSON>' }).click()
    })

    await test.step('And guru go to Bukti Ka<PERSON> menu with "Eksplorasi" tab', async () => {
      await page.getByRole('tab', { name: 'Karya Saya' }).click()
    })

    await test.step('When guru click Tambah Karya on eksplorasi page', async () => {
      await page.getByRole('button', { name: 'Tambah Karya' }).waitFor()
      await page.getByRole('button', { name: 'Tambah Karya' }).click()
    })

    await test.step('Then user redirect to login page', async () => {
      await expectInLoginPage(page)
      await expect(
        page.getByText('Masuk dan akses beragam referensi untuk membantu pendidik yang merdeka.')
      ).toBeVisible()
    })

    await screenshot(page, testInfo)
  })
})
