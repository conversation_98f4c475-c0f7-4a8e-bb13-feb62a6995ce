#!/bin/bash

# GitLab CI Validation Script for Web Automation Reg
# Validates the GitLab CI configuration and setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[GITLAB-CI]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check GitLab CI YAML syntax
check_gitlab_ci_syntax() {
    print_status "Checking GitLab CI YAML syntax..."
    
    if [ ! -f ".gitlab-ci.yml" ]; then
        print_error "GitLab CI file not found: .gitlab-ci.yml"
        return 1
    fi
    
    # Check if gitlab-ci-local is available for validation
    if command -v gitlab-ci-local >/dev/null 2>&1; then
        print_status "Validating with gitlab-ci-local..."
        if gitlab-ci-local --list; then
            print_success "GitLab CI syntax is valid"
        else
            print_error "GitLab CI syntax validation failed"
            return 1
        fi
    else
        # Basic YAML syntax check with Python
        if command -v python3 >/dev/null 2>&1; then
            print_status "Validating YAML syntax with Python..."
            python3 -c "
import yaml
import sys

try:
    with open('.gitlab-ci.yml', 'r') as f:
        yaml.safe_load(f)
    print('YAML syntax is valid')
except yaml.YAMLError as e:
    print(f'YAML syntax error: {e}')
    sys.exit(1)
except Exception as e:
    print(f'Error reading file: {e}')
    sys.exit(1)
"
            if [ $? -eq 0 ]; then
                print_success "YAML syntax is valid"
            else
                print_error "YAML syntax validation failed"
                return 1
            fi
        else
            print_warning "Cannot validate YAML syntax (no validation tools available)"
        fi
    fi
    
    return 0
}

# Function to check required files for GitLab CI
check_required_files() {
    print_status "Checking required files for GitLab CI..."
    
    local errors=0
    
    # Check main files
    local required_files=(
        ".gitlab-ci.yml"
        "run_scaled.sh"
        "requirements.txt"
        "test_framework/parallel_executor.py"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "Required file found: $file"
        else
            print_error "Required file missing: $file"
            errors=$((errors + 1))
        fi
    done
    
    # Check directories
    local required_dirs=(
        "test_framework"
        "test_framework/features"
        "test_framework/modules"
        "scripts"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_success "Required directory found: $dir"
        else
            print_error "Required directory missing: $dir"
            errors=$((errors + 1))
        fi
    done
    
    # Check if run_scaled.sh is executable
    if [ -f "run_scaled.sh" ]; then
        if [ -x "run_scaled.sh" ]; then
            print_success "run_scaled.sh is executable"
        else
            print_warning "run_scaled.sh is not executable (will be fixed in CI)"
        fi
    fi
    
    if [ $errors -eq 0 ]; then
        print_success "All required files and directories found"
        return 0
    else
        print_error "$errors required files/directories missing"
        return 1
    fi
}

# Function to check feature files
check_feature_files() {
    print_status "Checking feature files..."
    
    if [ ! -d "test_framework/features" ]; then
        print_error "Features directory not found"
        return 1
    fi
    
    local feature_count=$(find test_framework/features -name "*.feature" 2>/dev/null | wc -l)
    
    if [ "$feature_count" -eq 0 ]; then
        print_error "No feature files found in test_framework/features"
        return 1
    fi
    
    print_success "Found $feature_count feature files"
    
    # Check for Xray tags
    local tagged_scenarios=0
    for feature_file in test_framework/features/*.feature; do
        if [ -f "$feature_file" ]; then
            local tags=$(grep -c "@TEST_" "$feature_file" 2>/dev/null || echo "0")
            tagged_scenarios=$((tagged_scenarios + tags))
        fi
    done
    
    if [ "$tagged_scenarios" -gt 0 ]; then
        print_success "Found $tagged_scenarios scenarios with Xray tags"
    else
        print_warning "No scenarios with Xray tags (@TEST_) found"
    fi
    
    return 0
}

# Function to check Python dependencies
check_python_dependencies() {
    print_status "Checking Python dependencies..."
    
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt not found"
        return 1
    fi
    
    print_success "requirements.txt found"
    
    # Show key dependencies
    local key_deps=("playwright" "requests" "yaml" "lxml")
    for dep in "${key_deps[@]}"; do
        if grep -q "$dep" requirements.txt; then
            print_success "Key dependency found in requirements.txt: $dep"
        else
            print_warning "Key dependency not found in requirements.txt: $dep"
        fi
    done
    
    return 0
}

# Function to check GitLab CI configuration
check_gitlab_ci_config() {
    print_status "Checking GitLab CI configuration..."
    
    # Check stages
    if grep -q "stages:" .gitlab-ci.yml; then
        print_success "Stages defined in GitLab CI"
    else
        print_error "No stages defined in GitLab CI"
        return 1
    fi
    
    # Check jobs
    local jobs=("e2e-test-main" "e2e-test-mr" "e2e-test-feature" "generate-report")
    for job in "${jobs[@]}"; do
        if grep -q "$job:" .gitlab-ci.yml; then
            print_success "Job defined: $job"
        else
            print_warning "Job not found: $job"
        fi
    done
    
    # Check artifacts configuration
    if grep -q "artifacts:" .gitlab-ci.yml; then
        print_success "Artifacts configuration found"
    else
        print_warning "No artifacts configuration found"
    fi
    
    # Check JUnit reports
    if grep -q "junit:" .gitlab-ci.yml; then
        print_success "JUnit reports configuration found"
    else
        print_warning "No JUnit reports configuration found"
    fi
    
    return 0
}

# Function to simulate GitLab CI environment
simulate_gitlab_ci() {
    print_status "Simulating GitLab CI environment variables..."
    
    # Set GitLab CI environment variables
    export CI=true
    export GITLAB_CI=true
    export CI_PIPELINE_ID="test-pipeline-123"
    export CI_JOB_ID="test-job-456"
    export CI_COMMIT_SHA="abc123def456"
    export CI_COMMIT_REF_NAME="develop"
    export CI_PROJECT_DIR="$(pwd)"
    
    print_success "GitLab CI environment variables set"
    
    # Show environment
    echo "  CI: $CI"
    echo "  GITLAB_CI: $GITLAB_CI"
    echo "  CI_PIPELINE_ID: $CI_PIPELINE_ID"
    echo "  CI_JOB_ID: $CI_JOB_ID"
    echo "  CI_COMMIT_REF_NAME: $CI_COMMIT_REF_NAME"
    
    return 0
}

# Function to generate validation summary
generate_validation_summary() {
    print_status "GitLab CI Validation Summary:"
    echo "============================="
    
    local total_checks=5
    local passed_checks=0
    
    # Run all checks and count results
    if check_gitlab_ci_syntax >/dev/null 2>&1; then
        echo "✅ GitLab CI YAML Syntax: PASSED"
        passed_checks=$((passed_checks + 1))
    else
        echo "❌ GitLab CI YAML Syntax: FAILED"
    fi
    
    if check_required_files >/dev/null 2>&1; then
        echo "✅ Required Files: PASSED"
        passed_checks=$((passed_checks + 1))
    else
        echo "❌ Required Files: FAILED"
    fi
    
    if check_feature_files >/dev/null 2>&1; then
        echo "✅ Feature Files: PASSED"
        passed_checks=$((passed_checks + 1))
    else
        echo "❌ Feature Files: FAILED"
    fi
    
    if check_python_dependencies >/dev/null 2>&1; then
        echo "✅ Python Dependencies: PASSED"
        passed_checks=$((passed_checks + 1))
    else
        echo "❌ Python Dependencies: FAILED"
    fi
    
    if check_gitlab_ci_config >/dev/null 2>&1; then
        echo "✅ GitLab CI Configuration: PASSED"
        passed_checks=$((passed_checks + 1))
    else
        echo "❌ GitLab CI Configuration: FAILED"
    fi
    
    echo ""
    echo "Overall: $passed_checks/$total_checks checks passed"
    
    if [ $passed_checks -eq $total_checks ]; then
        print_success "🎉 All validations passed! GitLab CI is ready."
        echo ""
        echo "Next steps:"
        echo "1. Commit and push your changes to GitLab"
        echo "2. Create a merge request or push to main/develop"
        echo "3. Monitor the GitLab CI pipeline execution"
        echo "4. Check the generated artifacts and JUnit reports"
        return 0
    else
        print_warning "⚠️ Some validations failed. Please review and fix issues."
        echo ""
        echo "Common fixes:"
        echo "1. Ensure all required files are present"
        echo "2. Fix YAML syntax errors in .gitlab-ci.yml"
        echo "3. Add feature files with @TEST_ tags"
        echo "4. Verify Python dependencies in requirements.txt"
        return 1
    fi
}

# Main execution
main() {
    print_status "🚀 GitLab CI Validation for Web Automation Reg"
    print_status "=============================================="
    
    # Simulate GitLab CI environment
    simulate_gitlab_ci
    echo ""
    
    # Run individual checks
    check_gitlab_ci_syntax
    echo ""
    
    check_required_files
    echo ""
    
    check_feature_files
    echo ""
    
    check_python_dependencies
    echo ""
    
    check_gitlab_ci_config
    echo ""
    
    # Generate summary
    generate_validation_summary
}

# Run main function
main "$@"
