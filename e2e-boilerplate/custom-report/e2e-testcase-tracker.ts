import { CI_PROJECT_NAME, DEV_CUSTOM_REPORTER, ENV_CUSTOM_REPORTER, GROUP_NAME, TRIBE_NAME } from '@/utils/env.util'
import type { FullConfig, FullResult, Reporter, Suite, TestCase, TestResult } from '@playwright/test/reporter'
import { satuEventTracker } from '@wartek-id/toolbox-event-tracker'
import { execSync } from 'child_process'

import crypto from 'crypto'
import { omit, uniq } from 'lodash'
import { nanoid } from 'nanoid'
import invariant from 'tiny-invariant'

/**
 * For development purposes, you can set these variables to make sure the reporter works as expected.
 * It will send the data to the staging environment instead of the production environment.
 # Custom Reporter - Development only
 DEV_CUSTOM_REPORTER=true
 ENV_CUSTOM_REPORTER=staging
 TRIBE_NAME=platform
 GROUP_NAME=core-webex
 CI_PROJECT_NAME=e2e-boilerplate
 CI_COMMIT_BRANCH=main
 */

interface Tag {
  tag_name: string
  total_test_cases: number
}

interface Hash {
  iv: string
  content: string
}

interface Project {
  project_name: string
  tags: Tag[]
  total_test_cases: number
}

interface ExecutionInfo {
  last_updated: string
  execution_duration: string
}

interface RateInfo {
  success_rate: string
  failed_rate: string
  flaky_rate: string
}

interface AllData {
  id?: string
  timestamp?: string
  projects: Project[]
  execution_info: ExecutionInfo
  rate_info: RateInfo
  tribe_name: string
  squad_name: string
  repository_name: string
  totalTests: number
  successTests: string[]
  failedTests: string[]
  startTime: number
}

class GovtechE2EReporter implements Reporter {
  private allData: AllData = {
    projects: [],
    execution_info: {
      last_updated: '',
      execution_duration: '0',
    },
    rate_info: {
      success_rate: '',
      failed_rate: '',
      /** flaky rate currently not supported */
      flaky_rate: '',
    },
    tribe_name: TRIBE_NAME,
    squad_name: GROUP_NAME,
    repository_name: CI_PROJECT_NAME,
    totalTests: 0,
    successTests: [],
    failedTests: [],
    startTime: 0,
  }

  private testRunningTitles: string[] = []

  onBegin(config: FullConfig, suite: Suite) {
    this.allData.projects = []

    this.allData.execution_info = {
      last_updated: '',
      execution_duration: '0',
    }

    this.allData.rate_info = {
      success_rate: '',
      failed_rate: '',
      flaky_rate: '',
    }

    const trackedTestCases = uniq(
      suite
        .allTests()
        .filter((f) => !f.title.includes('@setup'))
        .map((t) => t.title)
    )

    this.allData.id = process.env.CI_PIPELINE_ID ?? nanoid()
    this.allData.timestamp = new Date().toISOString()
    this.allData.tribe_name = TRIBE_NAME || ''
    this.allData.squad_name = GROUP_NAME || ''
    this.allData.repository_name = CI_PROJECT_NAME || ''
    this.allData.totalTests = trackedTestCases.length
    this.allData.successTests = []
    this.allData.failedTests = []
    this.allData.startTime = new Date().getTime()
    this.allData.execution_info.execution_duration = String(this.allData.execution_info.execution_duration)

    try {
      // Get date of the last commit in the repository
      const lastCommitDateStr = execSync('git log -1 --format=%cd').toString()

      // Parse the date string to a Date object
      const lastUpdatedDate = new Date(lastCommitDateStr)

      // Convert the date object to an ISO string
      this.allData.execution_info.last_updated = lastUpdatedDate.toISOString()
    } catch (err) {
      console.error(`Error while getting the last updated date: ${err}`)
      // Fallback to current date time or you can handle the error
      this.allData.execution_info.last_updated = new Date().toISOString()
    }
  }

  // Function to extract the value of a given option (e.g., '--grep' or '--g')
  private getOptionValue(option: string): string | undefined {
    const optionIndex = process.argv.findIndex((arg) => arg === option)
    if (optionIndex !== -1 && optionIndex + 1 < process.argv.length) {
      return process.argv[optionIndex + 1]
    }
    return undefined
  }

  private decrypt(hash: Hash, secretKey: string): string {
    const iv = Buffer.from(hash.iv, 'base64')
    const encryptedText = Buffer.from(hash.content, 'base64')
    const decipher = crypto.createDecipheriv('aes-256-cfb', Buffer.from(secretKey, 'base64'), iv)
    let decrypted = decipher.update(encryptedText)
    decrypted = Buffer.concat([decrypted, decipher.final()])

    return decrypted.toString()
  }

  private async trackEvent(data: Omit<AllData, 'totalTests' | 'successTests' | 'failedTests' | 'startTime'>) {
    invariant(TRIBE_NAME, 'TRIBE_NAME environment variable is not set')
    invariant(GROUP_NAME, 'GROUP_NAME environment variable is not set')
    invariant(CI_PROJECT_NAME, 'CI_PROJECT_NAME environment variable is not set')

    const { key, iv, content } =
      DEV_CUSTOM_REPORTER && ENV_CUSTOM_REPORTER === 'staging'
        ? {
            key: 'nviFLRpILA5HfuWyYU1nlyywiaulrDgtyyzThcnE/xQ=',
            iv: 'ZahqEkhCQd0CUPbWhKk13Q==',
            content: 'wPoMQ1Wa/J0MTgAt4v0/4Zb7ryA=',
          }
        : {
            key: 'P3aqTbf8lyz3D36d6EgPcrqcTr4WGbMo1mm2J0OkeXI=',
            iv: 'MiV6ZD1IQw3kixgtXwzJ1w==',
            content: 'tXBE7+7wsZDoqd7CI66d6yMhZ/GuvscJIciTzCiLQ6TVclDmcQAyiLoHesxfqu7FVn3/w3LSX6sAzYTm',
          }

    const basicAuth = this.decrypt({ iv, content }, key)

    satuEventTracker.createInstance({
      auth: { basicAuth },
      url:
        DEV_CUSTOM_REPORTER && ENV_CUSTOM_REPORTER === 'staging'
          ? 'https://api.data.staging.belajar.id/event-tracker/api/v1/events'
          : 'https://events.belajar.id/event-tracker/api/v1/events',
    })

    await satuEventTracker.track({
      eventName: 'webex_tracker_e2e_v1',
      data,
    })

    console.log('-- E2E Test Cases tracked successfully!')
  }

  private updateProjectAndTagData(test: TestCase) {
    const projectName = test['_projectId']

    // Track tag wise test count here
    const tag = test.title.includes('@visual') ? 'VISUAL' : 'NON-VISUAL'
    const project = this.allData.projects.find((p) => p.project_name === projectName)

    if (project) {
      const tagged = project.tags.find((t) => t.tag_name === tag)
      if (tagged) {
        tagged.total_test_cases++
        project.total_test_cases++
      } else {
        project.tags.push({ tag_name: tag, total_test_cases: 1 })
      }
    } else {
      this.allData.projects.push({
        project_name: projectName,
        tags: [{ tag_name: tag, total_test_cases: 1 }],
        total_test_cases: 1,
      })
    }
  }

  onTestBegin(test: TestCase) {
    // Skip @setup test cases
    if (test.title.includes('@setup')) return

    const projectNameWithTitle = `${test['_projectId']} - ${test.title}`

    // If the title already exists, skip it. This prevents duplicate test case calculation, especially when the test is retried.
    if (this.testRunningTitles.find((t) => t === projectNameWithTitle)) return

    this.testRunningTitles.push(projectNameWithTitle)
    this.updateProjectAndTagData(test)
  }

  onTestEnd(test: TestCase, result: TestResult) {
    if (result.status === 'passed' || result.status === 'skipped') {
      this.allData.successTests.push(test.title)
    } else {
      this.allData.failedTests.push(test.title)
    }
  }

  async onEnd(result: FullResult) {
    /**
     *  If the test duration is less than 1 second, we assume that the test is not running
     *  For example when running `npm run test --list`
     */
    const isRunningTests = result.duration > 1000
    if (!isRunningTests) return

    /** Skip on schedule pipelines
     * This to prevent total test cases count that only counting production test cases
     * Because mostly schedule pipelines are used to run production env (smoke test or trigger from other/app repository)
     * It will make the total test cases count not accurate because the staging test cases are not counted.
     *
     * Why "pipeline": https://docs.gitlab.com/ee/ci/pipelines/downstream_pipelines.html?tab=Multi-project+pipeline#use-rules-to-control-downstream-pipeline-jobs
     */
    if (['schedule', 'pipeline'].includes(process.env.CI_PIPELINE_SOURCE)) {
      console.log('-- Info: triggered from schedule pipeline or trigger pipeline, skipping tracking event')
      return
    }

    /**
     * Skip tracking event if the test have grep filters (it also prevent track event if triggered from Jira)
     * This is to prevent tracking event when running `npm run test --grep`
     * which is used to run specific test cases and not the whole test suite
     * whitelist grep: @production, @staging
     */
    const grepValue = this.getOptionValue('--grep') || this.getOptionValue('-g')

    // grepValue examples: @production, @staging, @staging|@SHIP-123
    const grepValueArray = grepValue?.split('|') || []
    const isFilteringProductionOrSaging = grepValueArray.some(
      (f) => f.includes('@production') || f.includes('@staging')
    )

    if (grepValue && !isFilteringProductionOrSaging) {
      console.log('-- Info: grep filter detected, skipping tracking event')
      return
    }

    const shouldTrackEvent = process.env.CI_COMMIT_BRANCH === 'main' || DEV_CUSTOM_REPORTER
    if (!shouldTrackEvent) {
      console.log('-- Info: not on main branch, skipping tracking event')
      return
    }

    /** Skip if @setup test failed */
    const failedTestSetup = this.allData.failedTests.find((f) => f.includes('@setup'))
    if (failedTestSetup) {
      console.log('-- Info: @setup test failed, skipping tracking event')
      return
    }

    /** Filter this.allData from @setup tests */
    this.allData.failedTests = this.allData.failedTests.filter((f) => !f.includes('@setup'))
    this.allData.successTests = this.allData.successTests.filter((f) => !f.includes('@setup'))

    this.allData.execution_info.execution_duration = String((new Date().getTime() - this.allData.startTime) / 1000)

    // filter test that flaky so it will not be counted as failed test
    const flakesTests = uniq(this.allData.failedTests.filter((f) => this.allData.successTests.includes(f))).length
    // filter test that success but also flaky so it will not be counted as success test
    const successTests = uniq(this.allData.successTests.filter((f) => !this.allData.failedTests.includes(f))).length
    // filter test that failed but also flaky so it will not be counted as failed test
    const failedTests = uniq(this.allData.failedTests.filter((f) => !this.allData.successTests.includes(f))).length

    this.allData.rate_info.success_rate = (successTests / this.allData.totalTests || 0).toFixed(2)
    this.allData.rate_info.failed_rate = (failedTests / this.allData.totalTests || 0).toFixed(2)
    this.allData.rate_info.flaky_rate = (flakesTests / this.allData.totalTests || 0).toFixed(2)

    const trackingPayload = omit(this.allData, ['totalTests', 'successTests', 'failedTests', 'startTime'])

    const projects = trackingPayload.projects.map((p) => ({
      ...p,
      tags: p.tags.map((t) => t.tag_name),
    }))

    console.table(projects)
    console.table(omit(trackingPayload, ['projects']))

    await this.trackEvent(trackingPayload)
  }
}

export default GovtechE2EReporter
