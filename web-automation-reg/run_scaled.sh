#!/bin/bash

# Enhanced Test Runner for Large-Scale Test Execution
# Supports parallel execution, smart test selection, and performance optimization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEFAULT_MAX_TESTS=50
DEFAULT_MAX_WORKERS=4
DEFAULT_CHUNK_SIZE=5
DEFAULT_STRATEGY="hybrid"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Function to show usage
show_usage() {
    echo "Optimized Test Runner for Large-Scale Test Execution"
    echo ""
    echo "Usage: $0 [OPTIONS] [TEST_CASES]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -m, --mode MODE         Execution mode: 'parallel', 'sequential', 'smart' (default: smart)"
    echo "  -w, --workers NUM       Number of parallel workers (default: $DEFAULT_MAX_WORKERS)"
    echo "  -c, --chunk-size NUM    Chunk size for parallel execution (default: $DEFAULT_CHUNK_SIZE)"
    echo "  -s, --strategy STRAT    Smart selection strategy: 'risk_based', 'time_based', 'coverage_based', 'hybrid' (default: $DEFAULT_STRATEGY)"
    echo "  -t, --max-tests NUM     Maximum number of tests to run (default: $DEFAULT_MAX_TESTS)"
    echo "  -a, --all               Run all available test cases"
    echo "  -f, --failed-only       Run only recently failed tests"
    echo "  -r, --report-only       Generate report from previous execution"
    echo "  --feature-file FILE     Run specific .feature file (e.g., tc_188, rumdik_regression_test)"
    echo "                           Can be combined with specific tags: --feature-file tc_188 @TEST_RP-549 @TEST_RP-553"
    echo "  -v, --verbose           Verbose output"
    echo ""
    echo "Examples:"
    echo "  $0 TC1,TC2,TC3                    # Run specific test cases"
    echo "  $0 -a                             # Run all tests with optimized parallel execution"
    echo "  $0 -m parallel -w 8 TC1,TC2,TC3   # Run tests in parallel with 8 workers"
    echo "  $0 -s risk_based -t 30            # Run 30 highest-risk tests"
    echo "  $0 -f                             # Run only recently failed tests"
    echo "  $0 --feature-file tc_188          # Run only tc_188.feature"
    echo "  $0 --feature-file tc_188 -m parallel -w 4  # Run tc_188.feature in parallel with 4 workers"
    echo "  $0 --feature-file tc_188 @TEST_RP-549 @TEST_RP-553  # Run specific scenarios from tc_188.feature"
    echo ""
    echo "Performance:"
    echo "  - Optimized for 80-90% faster execution"
    echo "  - Single comprehensive report generation"
    echo "  - True parallel execution (not sequential)"
    echo "  - Suitable for 150+ test cases"
    echo ""
}

# Function to get all available test cases
get_all_test_cases() {
    local feature_file="$1"
    print_status "Discovering all available test cases..."
    
    # Check if we're in the right directory
    if [ ! -d "test_framework/features" ]; then
        print_error "test_framework/features directory not found. Please run from the project root."
        exit 1
    fi
    
    # If a specific feature file is specified, use only that file
    if [ -n "$feature_file" ]; then
        local feature_path="test_framework/features/${feature_file}.feature"
        if [ ! -f "$feature_path" ]; then
            print_error "Feature file not found: $feature_path"
            echo ""
            echo "Available feature files:"
            ls -1 test_framework/features/*.feature | sed 's|test_framework/features/||' | sed 's|\.feature||'
            exit 1
        fi
        print_status "Using specific feature file: $feature_file.feature"
        # Extract test cases - look for various formats: @TC1, @TEST_RP-549, etc.
        # Extract only the first tag (test case ID) from each line
        test_cases=$(grep -h "^[[:space:]]*@" "$feature_path" | sed 's/^[[:space:]]*@//' | cut -d' ' -f1 | grep -E "^[A-Z]+[A-Z0-9_-]*$" | sort -u)
    else
        # Extract test cases from all Gherkin files - look for various formats
        # Extract only the first tag (test case ID) from each line
        test_cases=$(grep -h "^[[:space:]]*@" test_framework/features/*.feature | sed 's/^[[:space:]]*@//' | cut -d' ' -f1 | grep -E "^[A-Z]+[A-Z0-9_-]*$" | sort -u)
    fi
    
    # Return only the test case IDs, not the status message
    echo "$test_cases"
}

    # Function to run tests in parallel
    run_parallel_tests() {
        local test_cases="$1"
        local max_workers="$2"
        local chunk_size="$3"
        
        print_header "🚀 OPTIMIZED PARALLEL TEST EXECUTION"
        print_status "Test Cases: $test_cases"
        print_status "Max Workers: $max_workers"
        print_status "Chunk Size: $chunk_size"
        
        # Run the optimized parallel executor
        python3 test_framework/parallel_executor.py \
            --test-cases "$test_cases" \
            --mode gherkin \
            --max-workers "$max_workers" \
            --chunk-size "$chunk_size" \
            --feature-file "$FEATURE_FILE"
        
        if [ $? -eq 0 ]; then
            print_success "Optimized parallel execution completed!"
        else
            print_error "Optimized parallel execution failed"
            exit 1
        fi
    }

# Function to run tests sequentially
run_sequential_tests() {
    local test_cases="$1"
    
    print_header "🔄 SEQUENTIAL TEST EXECUTION"
    print_status "Test Cases: $test_cases"
    
    # Convert comma-separated list to space-separated
    test_list=$(echo "$test_cases" | tr ',' ' ')
    
    for test_case in $test_list; do
        print_status "Running $test_case..."
        ./run_gherkin.sh "@$test_case"
        
        if [ $? -eq 0 ]; then
            print_success "$test_case completed successfully"
        else
            print_error "$test_case failed"
        fi
    done
}

# Function to run smart test selection
run_smart_tests() {
    local all_test_cases="$1"
    local max_tests="$2"
    local strategy="$3"
    local mode="$4"
    
    print_header "🧠 SMART TEST SELECTION"
    print_status "Available Tests: $all_test_cases"
    print_status "Max Tests: $max_tests"
    print_status "Strategy: $strategy"
    print_status "Mode: $mode"
    
    # Use smart test selector to choose tests
    print_status "Calling smart test selector with: all_test_cases=$all_test_cases, max_tests=$max_tests, strategy=$strategy"
    print_status "Current directory: $(pwd)"
    print_status "Smart selector path: test_framework/smart_test_selector.py"
    
    selected_tests=$(python3 test_framework/smart_test_selector.py "$all_test_cases" "$max_tests" "$strategy")
    
    print_status "Smart selector exit code: $?"
    print_status "Smart selector output length: ${#selected_tests}"
    print_status "Smart selector output (first 200 chars): ${selected_tests:0:200}"
    
    if [ -z "$selected_tests" ]; then
        print_error "No tests selected by smart selector"
        exit 1
    fi
    
    # Extract just the test case names from the output
    # The smart selector outputs a formatted list, we need to extract just the test case IDs
    test_case_list=""
    
    # First, try to extract from the success line that contains all test cases
    if [[ $selected_tests =~ "Smart selector chose:" ]]; then
        # Extract the part after "Smart selector chose:"
        chosen_part=$(echo "$selected_tests" | grep -o "Smart selector chose:.*" | sed 's/Smart selector chose: //')
        if [ -n "$chosen_part" ]; then
            test_case_list="$chosen_part"
            print_success "Extracted test cases from success line: $test_case_list"
        fi
    fi
    
    # If that didn't work, try the line-by-line parsing
    if [ -z "$test_case_list" ]; then
        while IFS= read -r line; do
            # Look for lines that contain test case IDs (TC1, TC2, etc.)
            if [[ $line =~ TC[0-9]+ ]]; then
                # Extract the test case ID
                tc_id=$(echo "$line" | grep -o 'TC[0-9]*' | head -1)
                if [ -n "$tc_id" ]; then
                    # Check if this test case is already in the list to avoid duplicates
                    if [[ ! "$test_case_list" =~ "$tc_id" ]]; then
                        if [ -z "$test_case_list" ]; then
                            test_case_list="$tc_id"
                        else
                            test_case_list="$test_case_list,$tc_id"
                        fi
                    fi
                fi
            fi
        done <<< "$selected_tests"
    fi
    
    if [ -z "$test_case_list" ]; then
        print_error "Could not extract test cases from smart selector output"
        print_status "Smart selector output: $selected_tests"
        exit 1
    fi
    
    print_success "Smart selector chose: $test_case_list"
    
    # Run selected tests based on mode
    if [ "$mode" = "parallel" ]; then
        run_parallel_tests "$test_case_list" "$MAX_WORKERS" "$CHUNK_SIZE"
    else
        run_sequential_tests "$test_case_list"
    fi
}

# Function to run only failed tests
run_failed_tests() {
    print_header "🔍 FAILED TESTS EXECUTION"
    
    # Get all test cases
    all_test_cases=$(get_all_test_cases)
    
    # Use smart selector to get recently failed tests
    failed_tests=$(python3 test_framework/smart_test_selector.py "$all_test_cases" 20 "risk_based")
    
    if [ -z "$failed_tests" ]; then
        print_warning "No recently failed tests found"
        return
    fi
    
    print_status "Recently failed tests: $failed_tests"
    run_sequential_tests "$failed_tests"
}

# Function to generate comprehensive report
generate_report() {
    print_header "📊 GENERATING COMPREHENSIVE REPORT"
    
    # Find the latest comprehensive report (optimized parallel execution)
    latest_comprehensive_report=$(ls -t test_framework/reports/parallel_comprehensive_*.html 2>/dev/null | head -1)
    if [ -n "$latest_comprehensive_report" ]; then
        print_success "Latest comprehensive report: $latest_comprehensive_report"
        open "$latest_comprehensive_report" 2>/dev/null || print_status "Report available at: $latest_comprehensive_report"
        return
    fi
    
    # Fallback: check for individual reports
    if [ -d "test_framework/reports" ]; then
        latest_report=$(ls -t test_framework/reports/test_report_*.html 2>/dev/null | head -1)
        if [ -n "$latest_report" ]; then
            print_success "Latest individual report: $latest_report"
            open "$latest_report" 2>/dev/null || print_status "Report available at: $latest_report"
        else
            print_warning "No test reports found"
        fi
    else
        print_warning "No reports directory found"
    fi
}

# Function to check system resources
check_system_resources() {
    print_status "Checking system resources..."
    
    # Check available memory
    if command -v free >/dev/null 2>&1; then
        available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7/1024}')
        print_status "Available memory: ${available_memory}GB"
        
        if [ "$available_memory" -lt 4 ]; then
            print_warning "Low memory available. Consider reducing parallel workers."
            MAX_WORKERS=2
        fi
    fi
    
    # Check CPU cores
    if command -v nproc >/dev/null 2>&1; then
        cpu_cores=$(nproc)
        print_status "CPU cores: $cpu_cores"
        
        if [ "$MAX_WORKERS" -gt "$cpu_cores" ]; then
            print_warning "Reducing workers to match CPU cores"
            MAX_WORKERS=$cpu_cores
        fi
    fi
}

# Function to cleanup old reports
cleanup_old_reports() {
    print_status "Cleaning up old reports..."
    
    # Store current directory
    local original_dir=$(pwd)
    
    # Keep only last 10 reports - use macOS compatible syntax
    if [ -d "test_framework/reports" ]; then
        # For macOS, we need to use a different approach since -printf is not available
        cd test_framework/reports
        # Get list of test report files sorted by modification time (newest first)
        report_files=$(ls -t test_report_*.html 2>/dev/null | tail -n +11)
        if [ -n "$report_files" ]; then
            echo "$report_files" | xargs rm -f 2>/dev/null || true
        fi
        cd "$original_dir"
    fi
    
    if [ -d "test_framework/reports/parallel" ]; then
        cd test_framework/reports/parallel
        # Get list of parallel report files sorted by modification time (newest first)
        parallel_files=$(ls -t *_report.html 2>/dev/null | tail -n +21)
        if [ -n "$parallel_files" ]; then
            echo "$parallel_files" | xargs rm -f 2>/dev/null || true
        fi
        cd "$original_dir"
    fi
}

# Main execution
main() {
    # Default values
    MODE="smart"
    MAX_WORKERS=$DEFAULT_MAX_WORKERS
    CHUNK_SIZE=$DEFAULT_CHUNK_SIZE
    STRATEGY=$DEFAULT_STRATEGY
    MAX_TESTS=$DEFAULT_MAX_TESTS
    RUN_ALL=false
    FAILED_ONLY=false
    REPORT_ONLY=false
    VERBOSE=false
    TEST_CASES=""
    FEATURE_FILE=""
    
    # Store original arguments for filtering
    ORIGINAL_ARGS="$*"
    # Extract only tag arguments (those starting with @)
    TAG_ARGS=""
    for arg in "$@"; do
        if [[ "$arg" == @* ]]; then
            if [ -z "$TAG_ARGS" ]; then
                TAG_ARGS="$arg"
            else
                TAG_ARGS="$TAG_ARGS $arg"
            fi
        fi
    done
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -m|--mode)
                MODE="$2"
                shift 2
                ;;
            -w|--workers)
                MAX_WORKERS="$2"
                shift 2
                ;;
            -c|--chunk-size)
                CHUNK_SIZE="$2"
                shift 2
                ;;
            -s|--strategy)
                STRATEGY="$2"
                shift 2
                ;;
            -t|--max-tests)
                MAX_TESTS="$2"
                shift 2
                ;;
            -a|--all)
                RUN_ALL=true
                shift
                ;;
            -f|--failed-only)
                FAILED_ONLY=true
                shift
                ;;
            -r|--report-only)
                REPORT_ONLY=true
                shift
                ;;
            --feature-file)
                FEATURE_FILE="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -*)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                TEST_CASES="$1"
                shift
                ;;
        esac
    done
    
    # Set verbose mode
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    # Check if report-only mode
    if [ "$REPORT_ONLY" = true ]; then
        generate_report
        exit 0
    fi
    
    # Check if failed-only mode
    if [ "$FAILED_ONLY" = true ]; then
        run_failed_tests
        exit 0
    fi
    
    # Get test cases
    if [ "$RUN_ALL" = true ] || [ -n "$FEATURE_FILE" ]; then
        # If feature file is specified, run all tests from that file
        if [ -n "$FEATURE_FILE" ]; then
            print_status "Feature file specified: $FEATURE_FILE - running all tests from this file"
        fi
        TEST_CASES=$(get_all_test_cases "$FEATURE_FILE")
        # Clean up the test cases - remove any INFO prefixes and extra whitespace
        TEST_CASES=$(echo "$TEST_CASES" | grep -E "^[A-Z]+[A-Z0-9_-]*$" | tr '\n' ',' | sed 's/,$//')
        
        # If specific test cases are provided with feature file, filter to only those
        if [ -n "$FEATURE_FILE" ] && [ -n "$TEST_CASES" ]; then
            # Check if there are additional tag arguments (specific tags)
            if [ -n "$TAG_ARGS" ]; then
                print_status "Filtering to specific test cases: $TAG_ARGS"
                # Convert the tag arguments to a comma-separated list
                SPECIFIC_TESTS=$(echo "$TAG_ARGS" | tr ' ' ',')
                # Filter TEST_CASES to only include the specified ones
                FILTERED_TESTS=""
                for test in $(echo "$TEST_CASES" | tr ',' ' '); do
                    for specific_test in $(echo "$SPECIFIC_TESTS" | tr ',' ' '); do
                        # Remove @ prefix for comparison
                        specific_test_clean=$(echo "$specific_test" | sed 's/^@//')
                        if [ "$test" = "$specific_test_clean" ]; then
                            if [ -z "$FILTERED_TESTS" ]; then
                                FILTERED_TESTS="$test"
                            else
                                FILTERED_TESTS="$FILTERED_TESTS,$test"
                            fi
                            break
                        fi
                    done
                done
                TEST_CASES="$FILTERED_TESTS"
                print_status "Filtered test cases: $TEST_CASES"
            fi
        fi
    elif [ -z "$TEST_CASES" ]; then
        print_error "No test cases specified. Use -a for all tests, --feature-file for specific file, or specify test cases."
        show_usage
        exit 1
    fi
    
    # Check system resources
    check_system_resources
    
    # Cleanup old reports
    cleanup_old_reports
    
    # Execute based on mode
    case $MODE in
        "parallel")
            run_parallel_tests "$TEST_CASES" "$MAX_WORKERS" "$CHUNK_SIZE"
            ;;
        "sequential")
            run_sequential_tests "$TEST_CASES"
            ;;
        "smart")
            # For full test runs (-a flag or --feature-file), use optimized parallel execution
            if [ "$RUN_ALL" = true ] || [ -n "$FEATURE_FILE" ]; then
                print_status "Full test run detected - using optimized parallel execution"
                run_parallel_tests "$TEST_CASES" "$MAX_WORKERS" "$CHUNK_SIZE"
            else
                run_smart_tests "$TEST_CASES" "$MAX_TESTS" "$STRATEGY" "parallel"
            fi
            ;;
        *)
            print_error "Unknown mode: $MODE"
            show_usage
            exit 1
            ;;
    esac
    
    # Generate final report
    generate_report
    
    print_success "Test execution completed!"
}

# Run main function
main "$@" 