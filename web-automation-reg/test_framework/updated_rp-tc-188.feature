
@RP-1063
Feature: TC 188

	@TEST_RP-932 @Web @positive_test @staging
	Scenario: Aktifasi Layanan Layanan UKBI pada Ruang Bahasa
		Given the user is on the Ruang Bahasa page
		    When the user sees the Layanan UKBI option
		    And the user clicks on Layanan UKBI
		    Then the user should be redirected to the Layanan UKBI page
		
	#Redirection to Tentang page fails
	@TEST_RP-553 @about-us @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Redirection to Tentang page fails
		Given the user is on the homepage
		  When the user clicks the blue button
		  And the redirection URL is broken or misconfigured
		  Then the user should see an error page (e.g., 404 or 500)
		
		
	#Downloading the Cetak Biru file
	@TEST_RP-552 @about-us @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Downloading the Cetak Biru file
		Given the user is on the Tentang Rumah Pendidikan page
		  When the user clicks on the "Cetak Biru Rumah Pendidikan" download button
		  Then the blueprint file should automatically be downloaded to the user’s device
		
		

	
	#Viewing the blue button entry point on homepage
	@TEST_RP-549 @about-us @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Viewing the blue button entry point on homepage
		Given the user is on the homepage of Rumah Pendidikan
		  When the user scrolls to the "Semangat Rumah Pendidikan" segment
		  Then the user should see a blue button labeled appropriately (e.g., "Pelajari Selengkapnya")
		
		
		
	#Navigating to the next page of articles
	@TEST_RP-547 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Navigating to the next page of articles
		Given the user is on the "Informasi Untuk Anda" listing page
		  When the user clicks on the "Next Page" button
		  Then the user should see the next set of articles listed
		
		
	#Clicking on a news thumbnail redirects to detail page
	@TEST_RP-546 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Clicking on a "Informasi Untuk Anda" thumbnail redirects to detail page
		Given the user is viewing the "Informasi Untuk Anda" listing
		  When the user clicks on a thumbnail or title of a "Informasi Untuk Anda"
		  Then the user should be redirected to the detail page of that Informasi Untuk Anda
		
		
		
	#Viewing the list of "Informasi Untuk Anda"
	@TEST_RP-543 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Viewing the list of "Informasi Untuk Anda"
		Given the user is on the homepage or news section of Rumah Pendidikan
		  When the page loads successfully
		  Then the user should see a list of available "Informasi Untuk Anda" displayed with thumbnails and titles
		

		
	@TEST_RP-375 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views "Ruang Pemerintah" and accesses them without opening a new tab
		Given the user is on the Rumah Pendidikan homepage
		    Then the user should see the following content and list of services in the "Ruang Pemerintah":
		      | Content Description                                                                          |
		      | Layanan pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan     |
		      | Layanan yang Tersedia                                                                        |
		      | Akun Pendidikan                                                                              |
		      | Neraca Pendidikan Daerah                                                                     |
		      | Rapor Pendidikan Daerah                                                                      |
		      | Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah                                     |
		    When the user clicks on any of the listed services
		    Then the system should redirect the user to the corresponding service page
		    And the redirection should happen in the same browser tab


	@TEST_RP-374 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views list of "Ruang Publik" and accesses them without opening a new tab
		Given the user accesses the Rumah Pendidikan homepage
		    Then the user should see the following "Ruang Publik" items:
		      | Service Name                    |
		      | Informasi dan materi pendidikan |
		      | Layanan yang Tersedia           |
		      | Bantuan Pendidikan              |
		      | Informasi Data Pendidikan       |
		      | Kursus Digital                  |
		      | Layanan Informasi dan Pengaduan |
		      | Majalah Pendidikan              |
		      | Pusat Perbukuan                 |
		    When the user clicks on any of the listed services
		    Then the system should redirect the user to the selected service
		    And the redirection should happen in the same browser tab

		
	@TEST_RP-373 @fe @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views "Ruang GTK" and accesses them without opening a new tab
		Given the user is on the Rumah Pendidikan homepage
		    Then the user should see the following content and service list in the "Ruang GTK" section:
		      | Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan Tenaga Kependidikan (GTK)    |
		      | Layanan yang Tersedia                                                                       |
		      | Diklat                                                                                      |
		      | Sertifikasi Pendidik                                                                        |
		      | Pelatihan Mandiri                                                                           |
		      | Komunitas                                                                                   |
		      | Pengelolaan Kinerja                                                                         |
		      | Seleksi Kepala Sekolah                                                                      |
		      | Refleksi Kompetensi                                                                         |
		      | Perangkat Ajar                                                                              |
		      | CP/ATP                                                                                      |
		      | Ide Praktik                                                                                 |
		      | Bukti Karya                                                                                 |
		      | Video Inspirasi                                                                             |
		      | Asesmen (Asesmen Murid dan AKM Kelas)                                                       |
		      | Kelas                                                                                       |
		      | Dokumen Rujukan Pengelolaan Kinerja                                                         |
		      | Dokumen Rujukan Pengelolaan Pembelajaran                                                    |
		      | Dokumen Rujukan Pengelolaan Satuan Pendidikan                                               |
		      | Dokumen Rujukan Peningkatan Kompetensi                                                      |
		    When the user clicks on any of the listed services
		    Then the system should redirect the user to the corresponding service page
		    And the redirection should happen in the same browser tab
		
	@TEST_RP-372 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views "Ruang Orang Tua" and accesses them without opening a new tab
		Given the user is on the Rumah Pendidikan homepage
		    Then the user should see the following text and service options under the Parent Space section:
		      | Content Description                                                 |
		      | Sarana partisipasi orang tua melalui pantauan capaian Murid         |
		      | dan dukungan belajar di rumah                                       |
		      | Layanan yang Tersedia                                               |
		      | Konsultasi Pendidikan                                               |
		      | Layanan Informasi dan Pengaduan                                     |
		      | Panduan Pendampingan                                                |
		    When the user clicks on any of the listed services
		    Then the system should redirect the user to the selected service
		    And the redirection should happen in the same browser tab
		
	@TEST_RP-370 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views homepage structure and interacts with service cards and search results
		Given the user accesses the Rumah Pendidikan homepage
		    Then the user should see the following components in order from top to bottom:
		      | Component                                                                 |
		      | Header with logo, search box, and login button                            |
		      | Hero banner with copytext (static image, unclickable)                     |
		      | 8 room icons with segment titles                                          |
		      | 4 live photos with text "Semangat Rumah Pendidikan Nasional 2025"         |
		      | Carousel of 5 featured services with segment titles                       |
		      | 3 education actors' testimonials about Rumah Pendidikan                   |
		      | FAQ section with button linking to the complaint form                     |
		      | Footer with logo, navigation, and links to Google Play Store & App Store  |
		    And on each room page, the user should see a list of services displayed as cards with icons
		    When the user clicks a service card within a room page
		    Then the user should be redirected to the respective service platform in the same browser tab
		    When the user performs a search with keywords such as "guru" or "murid"
		    Then the system should display matching service cards containing the keyword in the title, description, or tags
		
	@TEST_RP-357 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Show 'No results found' state when there are no matching search results
		Given the user is on the Rumah Pendidikan homepage
		    When the user enters a keyword in the search box
		    And the keyword does not match any service title or description
		    Then the system should display a message stating "Tidak Ada Hasil yang Sesuai"
		
	@TEST_RP-356 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Show list of services matching the keyword entered by the user
		Given the user is on the Rumah Pendidikan homepage
		    When the user enters a keyword in the search box
		    And the keyword matches a title or description of one or more services
		    Then a list of matching services should be displayed to the user
		
	@TEST_RP-355 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Display Relawan Pendidikan Pages
		Given the user is on the Rumah Pendidikan homepage
		    When user click Icon "Ruang Mitra" 
		    And user click Card Relawan Pendidikan
		    Then user direct to Relawan Pendidikan Pages "https://rumah.pendidikan.go.id/relawan-pendidikan.html"
		
	
	
	@TEST_RP-351 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Orang Tua from Icon
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Orang Tua"
		    Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"
		
	@TEST_RP-350 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Publik  from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Publik"
		    Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"
		
	@TEST_RP-349 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Mitra from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Mitra"
		    Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"
		
	@TEST_RP-348 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Pemerintah from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Pemerintah"
		    Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"
		
	@TEST_RP-347 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Bahasa from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Bahasa"
		    Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"
		
	@TEST_RP-346 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Sekolah from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Sekolah"
		    Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"
		
	@TEST_RP-345 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Murid from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang Murid"
		    Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"
		
	@TEST_RP-344 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang GTK from Icon
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    And user click Icon "Ruang GTK"
		    Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"
		
		
	@TEST_RP-342 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Display Tittle and Deskription Rumah Pendidikan
		Given user is on the Rumah Pendidikan homepage
		    When user scrolls to the Midle Static Section
		    Then user should see The Title and Description of Web Rumah Pendidikan
		
	@TEST_RP-340 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Native Apps Android on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click Google Play Store Logo
		    Then user direct to Native Apps Rumah Pendidikan Android
		
	@TEST_RP-339 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Native Apps IOS on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click Apps Store Logo
		    Then user direct to Native Apps Rumah Pendidikan IOS
		
	@TEST_RP-338 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access "Kebijakan Privasi" on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Kebijakan Privasi"
		    Then user direct to Kebijakan Privasi Pages "https://rumah-baru.staging.belajar.id/kebijakan-privasi"
		
	@TEST_RP-337 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access "Syarat & Ketentuan" on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Syarat & Ketentuan"
		    Then user direct to Syarat & Ketentuan Pages "https://rumah-baru.staging.belajar.id/syarat-dan-ketentuan"
		
	@TEST_RP-336 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access "Pusat Bantuan Rumah Pendidikan" on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Pusat Bantuan Rumah Pendidikan"
		    Then user direct to Pusat Bantuan Rumah Pendidikan Pages "https://pengaduan.ult.kemendikdasmen.go.id/hc/en-gb/requests/new"
		
	@TEST_RP-335 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Orang Tua on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Orang Tua"
		    Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"
		
	@TEST_RP-334 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Publik on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Publik"
		    Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"
		
	@TEST_RP-333 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Mitra on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Mitra"
		    Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"
		
	@TEST_RP-332 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Pemerintah on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Pemerintah"
		    Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"
		
	@TEST_RP-331 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Bahasa on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Bahasa"
		    Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"
		
	@TEST_RP-330 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Sekolah on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Sekolah"
		    Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"
		
	@TEST_RP-329 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang Murid on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang Murid"
		    Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"
		
	@TEST_RP-328 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Access Ruang GTK on Footer
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    And user click "Ruang GTK"
		    Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"
		
	@TEST_RP-327 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Display Footer Element on each pages
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the Footer section
		    Then the user should see a Logo and Name of "Kementerian Pendidikan Dasar dan Menengah" on Footer
		    And the user should see Layanan Ruang Segmen on Footer
		    And the user should see Navigasi Segmen on Footer
		    And the user should see Entry Point to Native Apps Segmen on Footer
		
	@TEST_RP-309 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User can view various frequently asked questions and their answers
		Given the user is on the Rumah Pendidikan homepage
		    When the user scrolls to the FAQ section
		    Then the user should see a list of frequently asked questions
		    When the user clicks on a question
		    Then the answer related to the selected question should be displayed
		
	@TEST_RP-307 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User is redirected to the About Rumah Pendidikan page
		Given the user is on the Rumah Pendidikan homepage
		    When the user clicks the "Pelajari Selengkapnya" button
		    Then the user should be redirected to the "Mengenal Rumah Pendidikan" page
		    And the page should display complete information about Rumah Pendidikan
		
		
	@TEST_RP-305 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User can log in and log out using Google
		Given the user is on the homepage
		    When the user clicks the "Masuk" button
		    And selects the "Masuk" option
		    And completes the Google login process
		    Then the user should be logged in
		    And the navbar should display the user's initial name
		
		    When the user clicks the profile icon in the navbar
		    And selects "Logout"
		    Then the user should be logged out
		    And the navbar should display the "Masuk" button again
		
		
	@TEST_RP-302 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Clicking "Masuk" redirects to login page
		Given the user is on the homepage
		    When the user clicks the "Masuk" button
		    Then the user should be redirected to the login page
		
		
	@TEST_RP-301 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User can enter any keyword in the search box
		Given the user is on the homepage
		    When the user types any text into the search box
		    Then the search box should display the entered text
		
	@TEST_RP-300 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Clicking logo redirects to homepage
		Given the user is on any page of "Rumah Pendidikan"
		    When the user clicks on the "Rumah Pendidikan" logo
		    Then the user should be redirected to the homepage
		
	@TEST_RP-299 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Display of header elements on homepage
		Given the user navigates to "https://rumah-baru.staging.belajar.id/"
		    Then the header should display the "Rumah Pendidikan" logo on the left
		    And a search box in the middle
		    And a "Masuk" button on the right
		
	@TEST_RP-296 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Footer Link to Terms and Conditions
		Given the user navigates to "https://rumah-baru.staging.belajar.id/"
		    When the user scrolls to the footer
		    And the user clicks on the "Syarat dan Ketentuan" link
		    Then the user should be redirected to the "Syarat dan Ketentuan" page
		
		
	@TEST_RP-295 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Footer Link to Privacy Policy
		    Given the user navigates to "https://rumah-baru.staging.belajar.id/"
		    When the user scrolls to the footer
		    And the user clicks on the "Kebijakan Privasi" link
		    Then the user should be redirected to the "Kebijakan Privasi" page
		
		
	@TEST_RP-148 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Layanan Informasi dan Pengaduan
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik Ruang Orang Tua
		And Menampilkan halaman Ruang Orang Tua
		And Klik "Layanan Informasi dan Pengaduan" yang tidak tersedia
		Then Tidak diarahkan ke Webview Website "https://ult.kemdikbud.go.id/"
		And Gagal Diarahkan ke halaman Webview Website Layanan Informasi dan Pengaduan
		

		
	@TEST_RP-142 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website BUKU
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik Ruang Publik
		And Menampilkan halaman Ruang Publik
		And Klik "Bantuan Pendidikan" 
		Then Tidak diarahkan ke Webview Website "https://pip.kemdikbud.go.id/"
		And Gagal Diarahkan ke halaman Webview Website BUKU
		

		
	@TEST_RP-138 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview BUKU
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik Ruang Publik
		And Menampilkan halaman Ruang Publik
		And Klik "Pusat Perbukuan"
		Then Tidak diarahkan ke Webview Website "https://buku.kemdikbud.go.id/"
		And Gagal Diarahkan ke halaman Webview Website BUKU
				

	@TEST_RP-124 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Belajar.id
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik Ruang Pemerintah
		And Menampilkan halaman Ruang Pemerintah
		And Klik "Akun Pendidikan"
		Then diarahkan ke Webview Website "https://belajar.id/"
		And Diarahkan ke halaman Webview Website Belajar.id
		
		
	@TEST_RP-113 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Berhasil Diarahkan ke halaman Webview Website UKBI
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Bahasa"
		And Menampilkan halaman "Ruang Bahasa"
		And Klik "Layanan UKBI"
		Then Akan diarahkan ke Webview Website "https://ukbi.kemdikbud.go.id/"
		And Berhasil Diarahkan ke halaman Webview Website UKBI
		
	@TEST_RP-112 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Penerjemahan
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Bahasa"
		And Menampilkan halaman "Ruang Bahasa"
		And Klik "Penerjemahan Daring" yang tidak tersedia
		Then Tidak diarahkan ke Webview Website "https://penerjemahan.kemdikbud.go.id/"
		And Gagal Diarahkan ke halaman Webview Website Penerjemahan

			
	@TEST_RP-102 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Arkas
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Sekolah"
		And Menampilkan halaman "Ruang Sekolah"
		And Klik "Rencana Kegiatan dan Belanja Sekolah" yang tidak tersedia
		Then Tidak diarahkan ke Webview Website "https://arkas.kemdikbud.go.id/"
		And Gagal Diarahkan ke halaman Webview Website Arkas
		
	@TEST_RP-101 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: Berhasil Diarahkan ke halaman Webview Website Arkas
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Sekolah"
		And Menampilkan halaman Ruang Sekolah
		And Klik "Rencana Kegiatan dan Belanja Sekolah"
		Then Akan diarahkan ke Webview Website "https://arkas.kemendikdasmen.go.id/download/arkas4"
		And Berhasil Diarahkan ke halaman Webview Website Arkas
		
	@TEST_RP-100 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Rapor Pendidikan
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Sekolah"
		And Menampilkan halaman "Ruang Sekolah"
		And Klik "Rapor Satuan Pendidikan" yang tidak tersedia
		Then Tidak diarahkan ke Webview Website "https://raporpendidikan.kemdikbud.go.id/app"
		And Gagal Diarahkan ke halaman Webview Website Rapor Pendidikan
		
	@TEST_RP-98 @manual @negative_test @rumah-pendidikan @staging @web
	Scenario: Gagal Diarahkan ke halaman Webview Website Sekolah Kita
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik "Ruang Sekolah"
		And Menampilkan halaman "Ruang Sekolah"
		And Klik "Profil Sekolah" yang tidak tersedia
		Then Tidak diarahkan ke Webview "https://sekolah.data.kemendikdasmen.go.id/"
		And Gagal Diarahkan ke halaman Webview Website Sekolah Kita
		
		
	@TEST_RP-83 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario:  Berhasil Searching
		Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
		When Klik pada Search Bar
		And Ketik "Belajar"
		Then Sistem menampilkan hasil pencarian dengan kata kunci "Belajar"
		And Berhasil searching
		
	@TEST_RP-46 @fe @revamp @web
	Scenario: [Web][FE][Revamp][Manual] Initial deployment to staging for web revamp
		    Given the user has a web browser open
		    When the user navigates to "https://rumah-baru.staging.belajar.id/"
		    Then the website should be accessible