import { IAPLogin } from '@/auth/google-login.helper'
import { USER_IAP } from '@/constants/users.constant'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'
import { CI, ENVIRONMENT, PRODUCTION_ONLY } from '@/utils/env.util'
import { chromium } from 'playwright-extra'

import { log } from '@/utils/playwright-config.util'
import { test as setup } from '@playwright/test'

import stealth from 'puppeteer-extra-plugin-stealth'

chromium.use(stealth())

setup('@setup @staging Setting up IAP for staging environment', async () => {
  if (PRODUCTION_ONLY || (CI && ENVIRONMENT !== 'staging')) {
    return
  }

  // Why 3 minutes? Because Google login approximately takes 2-3 minutes.
  setup.setTimeout(3 * 60 * 1_000)

  if (!CI) {
    const isConfigValid = await checkConfig('staging', 'IAP')
    if (isConfigValid) {
      log('🔑 Cached auth files detected; skipping login iap.', 'staging')
      return
    }
  }

  log(`🔑 Logging you in to handle IAP, using account: ${USER_IAP.email}`, 'staging')

  const browser = await chromium.launch({ headless: false })

  await IAPLogin(browser)

  if (!CI) generateConfigAuth('staging', 'IAP')

  await browser.close()
})
