# Web Automation Reg Environment Configuration
# Copy this file to .env and update the values according to your setup

# Application Configuration
BASE_URL=https://rumah-baru.staging.belajar.id/

# Browser Configuration
BROWSER_TYPE=chromium
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# Test Configuration
TEST_ENVIRONMENT=staging
TEST_PARALLEL_WORKERS=4
TEST_CHUNK_SIZE=5
TEST_TIMEOUT=60

# Xray Integration Configuration
# Enable/disable Xray integration
XRAY_ENABLED=true

# Jira/Xray instance URL
XRAY_BASE_URL=https://wartek.atlassian.net

# Jira authentication (use API token for better security)
XRAY_USERNAME=your_jira_username_or_email
XRAY_PASSWORD=your_jira_password
XRAY_API_TOKEN=your_jira_api_token

# Project configuration
XRAY_PROJECT_KEY=RP
XRAY_TEST_PLAN_KEY=
XRAY_TEST_EXECUTION_KEY=

# Environment settings
XRAY_ENVIRONMENT=staging

# API settings
XRAY_TIMEOUT=30
XRAY_RETRY_ATTEMPTS=3
XRAY_RETRY_DELAY=5

# Jira Integration (for label updates)
JIRA_USERNAME=your_jira_username_or_email
JIRA_PASSWORD=your_jira_password
JIRA_API_TOKEN=your_jira_api_token

# CI/CD Configuration
CI=false
GITLAB_CI=false

# Logging Configuration
LOG_LEVEL=INFO
DEBUG_MODE=false

# Report Configuration
GENERATE_HTML_REPORTS=true
GENERATE_JUNIT_XML=true
SAVE_SCREENSHOTS=true
SAVE_VIDEOS=false

# Performance Configuration
ENABLE_CACHE=true
CACHE_EXPIRY_HOURS=24
PARALLEL_MODE=true

# Feature File Configuration
FEATURES_DIR=./test_framework/features
REPORTS_DIR=./test_framework/reports
OUTPUT_DIR=./output

# Example values for different environments:
# 
# For Staging:
# XRAY_ENVIRONMENT=staging
# BASE_URL=https://rumah-baru.staging.belajar.id/
# 
# For Production:
# XRAY_ENVIRONMENT=production
# BASE_URL=https://rumah.pendidikan.go.id/
#
# Security Notes:
# - Never commit actual credentials to version control
# - Use environment variables or secure secret management in CI/CD
# - Prefer API tokens over passwords for better security
# - Rotate credentials regularly
#
# Xray Integration Notes:
# - XRAY_USERNAME should be your Jira email address
# - XRAY_API_TOKEN is preferred over XRAY_PASSWORD
# - Generate API tokens from: https://id.atlassian.com/manage-profile/security/api-tokens
# - Test plan and execution keys are optional - leave empty for dynamic creation
#
# CI/CD Notes:
# - Set XRAY_ENABLED=false to disable Xray integration in development
# - Use GitLab CI variables for secure credential management
# - The integration will automatically detect CI environment
