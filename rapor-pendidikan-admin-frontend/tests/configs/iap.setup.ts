import { IAPLogin } from '@/auth/google-login.helper'
import { USER_IAP } from '@/constants/users.constant'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'
import { CI, ENVIRONMENT, PRODUCTION_ONLY } from '@/utils/env.util'
import { log } from '@/utils/playwright-config.util'
import { test as setup } from '@playwright/test'
import { chromium } from 'playwright'; // Standard Playwright import

setup('@setup @staging Setting up IAP for staging environment', async ({ browser }) => {
  if (PRODUCTION_ONLY || (CI && ENVIRONMENT !== 'staging')) {
    return
  }

  setup.setTimeout(5 * 60 * 1_000) // 5 minutes timeout
  setup.slow() // Add longer timeout for this specific test

  if (!CI) {
    const isConfigValid = await checkConfig('staging')
    if (isConfigValid) {
      log('🔑 Cached auth files detected; skipping login IAP.', 'staging')
      return
    }
  }

  log(`🔑 Logging you in to handle IAP, using account: ${USER_IAP.email}`, 'staging')

  let context;
  try {
    context = await browser.newContext({
      navigationTimeout: 90000,
      actionTimeout: 90000,
    });
    
    const page = await context.newPage();

    // Add more detailed logging
    log('🌐 Browser context and page created successfully', 'staging');

    // Add event listeners for debugging
    page.on('close', () => log('⚠️ Page was closed unexpectedly.', 'staging'));
    context.on('close', () => log('⚠️ Context was closed unexpectedly.', 'staging'));
    browser.on('disconnected', () => log('⚠️ Browser was disconnected unexpectedly.', 'staging'));

    await page.screenshot({ path: 'before-login.png' })
    log('🔍 Screenshot captured before login', 'staging')

    await IAPLogin(page)
    log('✅ IAP Login completed successfully', 'staging');

    await page.screenshot({ path: 'after-login.png' })
    log('🔍 Screenshot captured after login', 'staging')

    if (!CI) {
      generateConfigAuth('staging')
      log('🔑 Authentication credentials cached for future use.', 'staging')
    }

    // Increase the wait time after login
    await page.waitForTimeout(10000);
  } catch (error) {
    log(`❌ Error during IAP login: ${error.message}`, 'staging')
    if (context) {
      const errorPage = await context.newPage()
      await errorPage.screenshot({ path: 'error-screenshot.png' })
      log('🔍 Error screenshot captured.', 'staging')
    }
    throw error
  } finally {
    if (context) {
      try {
        await context.close()
        log('🔒 Browser context closed successfully', 'staging')
      } catch (e) {
        log(`⚠️ Error while closing context: ${e.message}`, 'staging')
      }
    }
  }
})
