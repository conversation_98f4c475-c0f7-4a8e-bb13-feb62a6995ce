import { connectXray } from '@/helpers/global.helper'
import { expectLoggedInUser, goToPerangkatAjarPage } from '@/helpers/test.helper'
import { Page, expect, test } from '@playwright/test'

async function fillMateri(page: Page, { materi = '' } = {}) {
  await page.getByText('Cari materi spesifik').click()
  await page.getByPlaceholder('Masukkan kata kunci').fill(materi)
}

async function clickCariPerangkatAjar(page: Page) {
  await page.waitForLoadState('networkidle')
  await page.waitForTimeout(2000)

  // click and wait <PERSON><PERSON><PERSON><PERSON> hasil OR Cari Perangkat Ajar
  if (await page.isVisible('button:has-text("Tam<PERSON><PERSON>an hasil")')) {
    await page.click('button:has-text("<PERSON><PERSON><PERSON><PERSON> hasil")')
  } else {
    await page.click('button:has-text("Cari Perangkat Ajar")')
  }
}

async function chooseKelasAndPelajaran(
  page: Page,
  { level = 'Umum', fase = 'Fase B', pelajaran = 'Bahasa Indonesia' } = {}
) {
  await page.getByText('Pilih fase (kelas) • Kelas arrow_drop_down').click({ delay: 200 })
  await page.getByText(level).click()

  await page.getByText(new RegExp(fase, 'i')).click()

  await page.getByRole('button', { name: 'Pilih' }).click()

  await page.waitForTimeout(5000) // expected 5s waiting to make sure the page is loaded
  await page.waitForLoadState('networkidle')

  await page.getByText(/Pilih mata pelajaran/i).click({ delay: 5000 })
  await page.getByText(pelajaran, { exact: true }).click()

  await page.getByRole('button', { name: 'Pilih Mapel' }).click()
}

async function initialGoToPerangkatAjarPage(page: Page) {
  await test.step('Given guru already login Gmail and go to "PERANGKAT_AJAR_URL"', async () => {
    await goToPerangkatAjarPage(page)
    await expectLoggedInUser(page, 'GURU_SATU')
  })

  await test.step('And guru is on Perangkat Ajar home page', async () => {
    expect(page.url()).toEqual(expect.stringContaining('/perangkat-ajar'))
  })
}

test.describe('Perangkat Ajar', () => {
  // https://wartek.atlassian.net/browse/CUR-4802
  test('@CUR-EXAMPLE4802 @production Search mapel with empty result', async ({ page }, testInfo) => {
    connectXray(testInfo, { key: 'CUR-4802' })
    await initialGoToPerangkatAjarPage(page)

    await test.step('When guru search invalid mapel name', async () => {
      await chooseKelasAndPelajaran(page)
      await fillMateri(page, { materi: 'invalid mapel name' })
      await clickCariPerangkatAjar(page)
    })

    await test.step('Then the message "Tidak ada perangkat ajar yang sesuai" will be display', async () => {
      await expect(page.getByText('Tidak ada perangkat ajar yang sesuai')).toBeVisible()
    })
  })

  // https://wartek.atlassian.net/browse/CUR-4983
  test('@CUR-EXAMPLE4983 @production Checking perangkat ajar empty result', async ({ page }, testInfo) => {
    connectXray(testInfo, { key: 'CUR-4983' })

    await initialGoToPerangkatAjarPage(page)

    await test.step('And users open the "Modul & Bahan Ajar" toolkit type at the "Umum" level', async () => {
      await chooseKelasAndPelajaran(page, { pelajaran: 'Tematik' })
    })

    await test.step('When guru selects one of fase "B" and subjects "Tematik"', async () => {
      await clickCariPerangkatAjar(page)
    })

    await test.step('Then the message "Tidak ada perangkat ajar yang sesuai" will be display', async () => {
      await expect(page.getByText('Tidak ada perangkat ajar yang sesuai')).toBeVisible()
    })

    await test.step('And the button "Edit Filter" will be displayed', async () => {
      await expect(page.getByRole('button', { name: 'Edit Filter' })).toBeVisible()
    })
  })

  // https://wartek.atlassian.net/browse/CUR-4984
  test('@CUR-EXAMPLE4984 @production PA/BA - Checking search result by keyword', async ({ page }, testInfo) => {
    connectXray(testInfo, { key: 'CUR-4984' })

    await initialGoToPerangkatAjarPage(page)

    await test.step('When guru selects one of fase "B" and subjects "Bahasa Indonesia" with specific materi "Modul"', async () => {
      await chooseKelasAndPelajaran(page, { fase: 'Fase B', pelajaran: 'Bahasa Indonesia' })

      await fillMateri(page, { materi: 'Modul' })

      await clickCariPerangkatAjar(page)
    })

    await test.step('Then guru successfully see list of toolkits for that subject "Modul" and specific materi "Modul"', async () => {
      await page.waitForLoadState('networkidle')
      await expect(page.getByText(/modul/i).first()).toBeVisible({ timeout: 5000 })
      await expect(page.getByRole('link', { name: /Modul/i }).first()).toBeVisible()
    })
  })

  // https://wartek.atlassian.net/browse/CUR-926
  test('@CUR-EXAMPLE926 @production Buku Guru - Detail page', async ({ page }, testInfo) => {
    connectXray(testInfo, { key: 'CUR-926' })

    await initialGoToPerangkatAjarPage(page)

    await test.step('And users open the "Buku" toolkit type at the "Umum" level', async () => {
      await page.locator('span').filter({ hasText: 'Buku' }).first().click()
    })

    await test.step('And guru selects one of fase "E" and subjects "Bahasa Indonesia"', async () => {
      await chooseKelasAndPelajaran(page, { fase: 'Fase E', pelajaran: 'Bahasa Indonesia' })
      await clickCariPerangkatAjar(page)
    })

    await test.step('And user select "Buku Guru" on filter', async () => {
      await page.getByText(/Filter/).click()
      await page.locator('#sheet-content').getByText('Buku Murid').first().click({ delay: 300 })
      await page.getByRole('button', { name: 'Lihat Perangkat Ajar' }).click({ delay: 300 })
    })

    await test.step('When user selects the top toolkit', async () => {
      await page
        .getByRole('link', { name: /Buku Guru/i })
        .first()
        .click({ delay: 300 })
    })

    await test.step('Then the "Buku Guru, Bahasa Indonesia" toolkit detail page will be displayed', async () => {
      await expect(page.getByText('Buku Guru, Bahasa Indonesia', { exact: true })).toBeVisible()
    })
  })
})
