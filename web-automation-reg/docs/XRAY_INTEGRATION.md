# Xray Integration for Web Automation Reg

This document describes the Xray integration implementation for the Web Automation Reg project, which automatically updates Jira Xray tickets with test execution results.

## Overview

The Xray integration allows automatic synchronization of test results from the Web Automation Reg framework to Jira Xray tickets. This provides real-time visibility into test execution status and maintains traceability between automated tests and requirements.

## Features

- ✅ **Automatic Test Result Updates**: Test results are automatically sent to Xray after execution
- ✅ **JUnit XML Generation**: Compatible JUnit XML reports for Xray import
- ✅ **Environment Support**: Differentiates between staging and production environments
- ✅ **Ticket Labeling**: Automatically labels Jira tickets as "automated"
- ✅ **CI/CD Integration**: Works seamlessly with GitLab CI pipelines
- ✅ **Error Handling**: Robust error handling with retry mechanisms
- ✅ **Configurable**: Flexible configuration via environment variables

## How It Works

### 1. Test Annotation
Each scenario in `.feature` files is tagged with a Jira ticket number:

```gherkin
@TEST_RP-932 @staging @regression_test
Scenario: Aktifasi Layanan Layanan UKBI pada Ruang Bahasa
  Given I am on the homepage
  When I click on the UKBI service
  Then I should see the activation page
```

### 2. Test Execution
When tests run via `./run_scaled.sh -a`, the framework:
- Extracts ticket numbers from `@TEST_RP-932` tags (extracts `RP-932`)
- Identifies environment from `@staging` or `@production` tags
- Tracks test execution results and timing

### 3. Result Reporting
After test completion:
- JUnit XML is generated with Xray-specific properties
- Test results are sent to Xray via REST API
- Individual test executions are updated in real-time
- Jira tickets are labeled as "automated"

## Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Xray Integration
XRAY_ENABLED=true
XRAY_BASE_URL=https://wartek.atlassian.net
XRAY_USERNAME=<EMAIL>
XRAY_API_TOKEN=your_jira_api_token
XRAY_PROJECT_KEY=RP
XRAY_ENVIRONMENT=staging
```

### Required Credentials

1. **Jira API Token**: Generate from [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. **Jira Username**: Your Jira email address
3. **Project Access**: Ensure you have Xray permissions in the target project

## Setup Instructions

### 1. Install Dependencies
Dependencies are already included in `requirements.txt`:
```bash
pip install -r requirements.txt
```

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your Jira credentials
```

### 3. Test Configuration
```bash
python3 scripts/setup_xray.py
```

This script will:
- Test Jira connection
- Verify Xray API access
- Check feature file tags
- Test Xray import functionality

### 4. Update Jira Labels
```bash
python3 scripts/update_jira_labels.py
```

This will label all tickets referenced in feature files as "automated".

## Usage

### Local Execution
```bash
# Run tests with Xray integration
./run_scaled.sh -a

# Check generated reports
ls -la reports/
ls -la junit.xml
```

### CI/CD Execution
The integration works automatically in GitLab CI. The pipeline will:
1. Run tests via `./run_scaled.sh -a`
2. Generate JUnit XML reports
3. Update Xray test executions
4. Label Jira tickets as automated

## File Structure

```
web-automation-reg/
├── config/
│   └── xray_config.yaml          # Xray configuration
├── scripts/
│   ├── setup_xray.py             # Setup and test script
│   └── update_jira_labels.py     # Jira labeling script
├── test_framework/
│   └── modules/
│       └── xray_integration.py   # Core Xray integration
├── docs/
│   └── XRAY_INTEGRATION.md       # This documentation
├── .env.example                  # Environment template
└── .gitlab-ci.yml               # CI configuration
```

## Integration Points

### 1. Test Executor (`test_executor.py`)
- Initializes Xray integration
- Updates individual test results in real-time
- Extracts ticket numbers and environments from scenario tags

### 2. Parallel Executor (`parallel_executor.py`)
- Finalizes Xray integration after all tests complete
- Generates comprehensive JUnit XML report
- Sends batch updates to Xray

### 3. JUnit Reporter (`junit_reporter.py`)
- Enhanced to include Xray-specific properties
- Saves reports to both local and root directories
- Compatible with Xray import format

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```
   ❌ Xray authentication failed
   ```
   - Verify `XRAY_USERNAME` and `XRAY_API_TOKEN`
   - Check API token permissions
   - Ensure account has Xray access

2. **Missing Ticket Tags**
   ```
   ⚠️ No Xray ticket number found in tags
   ```
   - Add `@TEST_RP-XXX` tags to scenarios
   - Verify tag format matches pattern

3. **Network Issues**
   ```
   ❌ Connection error: timeout
   ```
   - Check network connectivity to Jira
   - Verify `XRAY_BASE_URL` is correct
   - Check firewall/proxy settings

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
export XRAY_LOG_REQUESTS=true
./run_scaled.sh -a
```

### Manual Testing

Test Xray integration manually:
```bash
# Test connection
python3 scripts/setup_xray.py

# Test with sample data
python3 -c "
from test_framework.modules.xray_integration import XrayIntegration
xray = XrayIntegration()
print('Enabled:', xray.is_enabled())
print('Config:', xray.get_test_execution_info())
"
```

## Best Practices

1. **Tag Consistency**: Use consistent tagging patterns (`@TEST_RP-XXX`)
2. **Environment Tags**: Always include `@staging` or `@production`
3. **Credential Security**: Never commit credentials to version control
4. **Error Monitoring**: Monitor CI logs for Xray integration issues
5. **Regular Testing**: Run `setup_xray.py` periodically to verify configuration

## API Reference

### XrayIntegration Class

```python
from test_framework.modules.xray_integration import XrayIntegration

# Initialize
xray = XrayIntegration()

# Check if enabled
if xray.is_enabled():
    # Update test execution
    xray.update_test_execution('junit.xml', 'staging')
    
    # Update individual test
    xray.update_individual_test('RP-932', 'PASS', 'staging')
```

### Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `XRAY_ENABLED` | Enable/disable integration | `true` |
| `XRAY_BASE_URL` | Jira instance URL | `https://wartek.atlassian.net` |
| `XRAY_USERNAME` | Jira username/email | Required |
| `XRAY_API_TOKEN` | Jira API token | Required |
| `XRAY_PROJECT_KEY` | Project key | `RP` |
| `XRAY_ENVIRONMENT` | Default environment | `staging` |
| `XRAY_TIMEOUT` | API timeout (seconds) | `30` |
| `XRAY_RETRY_ATTEMPTS` | Retry attempts | `3` |

## Support

For issues or questions:
1. Check this documentation
2. Run `python3 scripts/setup_xray.py` for diagnostics
3. Review CI logs for error details
4. Contact the development team

## Changelog

- **v1.0.0**: Initial Xray integration implementation
  - JUnit XML generation with Xray properties
  - Real-time test result updates
  - Jira ticket labeling
  - CI/CD integration
  - Comprehensive error handling
