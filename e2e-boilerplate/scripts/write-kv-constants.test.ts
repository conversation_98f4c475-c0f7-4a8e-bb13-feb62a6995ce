import { USE_DYNAMIC_CONSTANTS_GSHEET } from '@/utils/env.util'
import fs from 'fs/promises'
import { GoogleAuth } from 'google-auth-library'
import nock from 'nock'
import path from 'path'
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest'
import { getGoogleToken, runKv } from './write-kv-constants'

vi.mock('fs/promises')
vi.mock('google-auth-library', () => ({
  GoogleAuth: vi.fn(() => ({
    getIdTokenClient: vi.fn().mockResolvedValue({
      getRequestHeaders: vi.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
      }),
    }),
  })),
}))

vi.mock('path')
vi.mock('shelljs')

beforeAll(() => {
  vi.mock('@/utils/env.util', () => ({
    USE_DYNAMIC_CONSTANTS_GSHEET: 'e2e-boilerplate',
  }))

  // mock import gsheet-kv.ts
  vi.mock('/mock/path/gsheet-kv.ts', () => ({
    __: {
      GURU_ACCOUNT_PATH: '/akun',
    },
  }))
})

describe('write-kv-constants', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  describe('getGoogleToken', () => {
    it('should return a bearer token', async () => {
      const result = await getGoogleToken()
      expect(result).toBe('Bearer mock-token')
      expect(GoogleAuth).toHaveBeenCalledTimes(1)
    })
  })

  describe('runKv', () => {
    const apiUrl = 'https://webex.staging.belajar.id'

    it('should fetch data and update constants', async () => {
      const mockData = { key1: 'value1', key2: 'value2' }
      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: mockData, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue('export const __ = {} as const;')
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      vi.clearAllMocks()
      await runKv()

      Object.entries(mockData).forEach(([key, value]) => {
        expect(fs.writeFile).toHaveBeenCalledWith(
          '/mock/path/gsheet-kv.ts',
          expect.stringContaining(`${JSON.stringify(key)}: ${JSON.stringify(value)},`),
          'utf-8'
        )
      })
    })

    it('should handle API errors', async () => {
      nock(apiUrl).get('/api/e2e/kv').query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET }).replyWithError('API Error')

      await runKv()
      expect(console.error).toHaveBeenCalledWith('Error fetching google sheet data:', 'API Error')
    })

    it('should retry on 401 error', async () => {
      nock(apiUrl)
        .get('/api/e2e/kv')
        .reply(401, 'Unauthorized')
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: { key: 'value' }, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue('export const __ = {} as const;')
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      await runKv()

      expect(fs.writeFile).toHaveBeenCalledWith('/mock/path/gsheet-kv.ts', expect.anything(), 'utf-8')
    })
  })

  describe('updateConstants', () => {
    it('should update existing constants', async () => {
      const existingContent = 'export const __ = { oldKey: "oldValue" } as const;'
      const newData = { oldKey: 'updatedValue', newKey: 'newValue' }
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: newData, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue(existingContent)
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      vi.clearAllMocks()
      await runKv()

      Object.entries(newData).forEach(([key, value]) => {
        expect(fs.writeFile).toHaveBeenCalledWith(
          '/mock/path/gsheet-kv.ts',
          expect.stringContaining(`${JSON.stringify(key)}: ${JSON.stringify(value)},`),
          'utf-8'
        )
      })
    })

    it("should create new constants file if it doesn't exist", async () => {
      const newData = { newKey: 'newValue' }
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: newData, message: 'Success' })

      vi.mocked(fs.readFile).mockRejectedValue(new Error('File not found'))
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      await runKv()

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining(
          Object.entries(newData)
            .map(([key, value]) => `${JSON.stringify(key)}: ${JSON.stringify(value)},`)
            .join('')
        ),
        'utf-8'
      )
    })

    it('should handle values with $$ placeholders', async () => {
      const existingContent = 'export const __ = { oldKey: "oldValue" } as const;'
      const newData = {
        templateKey: 'Hello $$name, welcome to $$place!',
        normalKey: 'Normal value',
      }
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: newData, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue(existingContent)
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      vi.clearAllMocks()
      await runKv()

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining(
          '"templateKey": ({name, place}: {name: string, place: string}) => `Hello ${name}, welcome to ${place}!`,'
        ),
        'utf-8'
      )
      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining('"normalKey": "Normal value",'),
        'utf-8'
      )
    })

    it('should handle values with multiple $$ placeholders in different positions', async () => {
      const newData = {
        complexTemplate: '$$greeting $$name! Your $$item is ready at $$location.',
      }
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: newData, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue('')
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      await runKv()

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining(
          '"complexTemplate": ({greeting, name, item, location}: {greeting: string, name: string, item: string, location: string}) => `${greeting} ${name}! Your ${item} is ready at ${location}.`,'
        ),
        'utf-8'
      )
    })

    /** SKIP: Currently we overwrite the file instead of merging it with existing constants */
    it.skip('should preserve existing constants not present in new data', async () => {
      vi.mock('/mock/path/gsheet-kv.ts', () => ({
        oldKey: 'oldValue',
        keepMe: 'I should stay',
      }))

      const existingContent = 'export const __ = { oldKey: "oldValue", keepMe: "I should stay" } as const;'
      const newData = { newKey: 'newValue' }
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl)
        .get('/api/e2e/kv')
        .query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET })
        .reply(200, { data: newData, message: 'Success' })

      vi.mocked(fs.readFile).mockResolvedValue(existingContent)
      vi.mocked(fs.writeFile).mockResolvedValue(undefined)
      vi.mocked(path.join).mockReturnValue('/mock/path/gsheet-kv.ts')

      vi.clearAllMocks()
      await runKv()

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining('"keepMe": "I should stay"'),
        'utf-8'
      )

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining('"oldKey": "oldValue"'),
        'utf-8'
      )

      expect(fs.writeFile).toHaveBeenCalledWith(
        '/mock/path/gsheet-kv.ts',
        expect.stringContaining('"newKey": "newValue"'),
        'utf-8'
      )
    })

    it('should handle API errors gracefully', async () => {
      const apiUrl = 'https://webex.staging.belajar.id'

      nock(apiUrl).get('/api/e2e/kv').query({ sheet: USE_DYNAMIC_CONSTANTS_GSHEET }).replyWithError('API is down')

      const consoleSpy = vi.spyOn(console, 'error')
      await runKv()

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching google sheet data:', 'API is down')
    })
  })
})
