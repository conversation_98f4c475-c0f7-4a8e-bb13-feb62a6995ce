import { Page } from '@playwright/test'

export const scrollDownVisitAllContent = async (page: Page) => {
  const maxScrollTop = await page.evaluate(() => document.body.scrollHeight - window.innerHeight)

  let currentScrollTop = 0
  while (currentScrollTop < maxScrollTop) {
    await page.evaluate((scrollTop) => {
      window.scrollTo({
        top: scrollTop,
        behavior: 'smooth',
      })
    }, currentScrollTop + 500)

    // Wait for the scroll animation to finish
    await page.waitForTimeout(200)

    currentScrollTop += 500
  }

  // Scroll to the very bottom of the page
  await page.evaluate(() => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth',
    })
  })

  // Wait until all animations are done
  await page.waitForTimeout(2 * 1000)

  // Wait until all images are loaded
  await page.waitForLoadState('networkidle')
}
