/**
 * This file is used to cache authentication results to improve your development experience.
 * So, when you run the test again, you don't need to log in again.
 *
 * This auth caching process is only done in development mode
 * The process is run when you run the test for the first time
 * or when the expiry time has passed.
 *
 * You can adjust the expiry time of the token according to your needs.
 * The default auth cache time is 7 days (assuming your app token is valid for 7 days or more).
 *
 * If you want to force re-login, you can delete the .auth-dev folder.
 * So, when you run the test again, the login process will be performed again.
 * Also, if you change the user data in the `users.ts` file, the login process will be performed again.
 */

export const CONFIG_AUTH_CACHE = '.auth-dev/config.json'

import { USERS, USER_IAP } from '@/constants/users.constant'
import { AUTH_CACHE_EXPIRY_TIME_DAYS, AUTH_SETUP_ENABLED, ENVIRONMENT, IAP_ENABLED } from '@/utils/env.util'
import { log } from '@/utils/playwright-config.util'

import { STORAGE_STATE_IAP } from '@/auth/google-login.helper'
import dayjs from 'dayjs'
import fs from 'fs-extra'
import { isEqual } from 'lodash'
import { chromium } from 'playwright-extra'
import { getAuthFile } from './global.helper'

export function generateConfigAuth(env: typeof ENVIRONMENT) {
  /**
   * Cache authentication files for a specified number of days so that we don't need to log in again for every test run.
   * You can adjust this value according to your token expiration time interval.
   */
  const DEV_TOKEN_EXPIRY_TIME_DAYS = AUTH_CACHE_EXPIRY_TIME_DAYS

  if (!fs.existsSync('.auth-dev')) {
    fs.mkdirSync('.auth-dev')
  }

  fs.writeJSONSync(CONFIG_AUTH_CACHE, {
    users: USERS,
    tokenExpiry: new Date().getTime() + DEV_TOKEN_EXPIRY_TIME_DAYS * 24 * 60 * 60 * 1000,
  })

  fs.copySync('.auth', '.auth-dev')
  log('[Cache] 🚀 Auth files cached to improve your development experience.', env)
}

export const checkConfig = async (env: typeof ENVIRONMENT) => {
  const isConfigExist = fs.existsSync(CONFIG_AUTH_CACHE)

  // check folder .auth is contain files
  const isUserExist = fs.existsSync('.auth') && fs.readdirSync('.auth').length > 0

  // check if .auth-dev folder contain any files
  const isAuthDevExist =
    fs.existsSync('.auth-dev') && fs.readdirSync('.auth-dev').some((file) => file.includes(`.${env}`))

  if (!isConfigExist || (AUTH_SETUP_ENABLED && !isAuthDevExist)) {
    return false
  }

  const config = fs.readJSONSync(CONFIG_AUTH_CACHE)
  const isUserConfigSame = isEqual(config.users, USERS)
  const isTokenValid = dayjs(config.tokenExpiry).isAfter(dayjs())

  if (!isTokenValid) {
    log('🔑 Token has expired. Re-logging in. Please wait.', env)
    return false
  }

  if (!AUTH_SETUP_ENABLED) {
    if (!IAP_ENABLED) {
      log('🔑 Auth setup is disabled. Skipping auth setup.', env)
      return true
    }

    const isStorageStateIAPExist = fs.existsSync(STORAGE_STATE_IAP)

    if (!isStorageStateIAPExist) {
      log('🔑 IAP cache is not exist. Please wait while we log you in.', env)
      return false
    }

    return true
  }

  // check is in .auth-dev any *.staging files
  if (!isUserExist) {
    log('🔑 No auth files were found in the cache. Please wait while we log you in.', env)
    return false
  }

  // check is current USERS const is same with dev config
  if (isUserConfigSame && isTokenValid) {
    if (!IAP_ENABLED) return true

    const browser = await chromium.launch({ headless: true })
    const page = await browser.newPage()

    await page.context().storageState({ path: getAuthFile(USER_IAP.pathFile, env) })

    await browser.close()

    return true
  }

  if (!isUserConfigSame) log('🔑 User configuration changed. Re-logging in. Please wait.', env)
  if (!isTokenValid) log('🔑 Token has expired. Re-logging in. Please wait.', env)

  fs.removeSync('.auth-dev')
  return false
}
