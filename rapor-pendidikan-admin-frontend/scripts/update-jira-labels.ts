import { bulkUpdateJiraIssues, filterOutAutomatedIssues } from '@wartek-id/e2e-jira-labelling'
import shell from 'shelljs'

/**
 * Update Jira Labels will be called at the same time as the test script ignoring the test results.
 * This script will extract Jira keys from the test script output and update the Jira issues with the label 'automated'.
 */

function extractJiraKeys(input: string): string[] {
  const regex = /@(\w+-\d+)/g
  const matches = input.match(regex)
  return matches ? matches : []
}

async function main() {
  const runningTests = shell.exec(
    'USE_GREP_FILTER=false npx playwright test --list --grep-invert "examples|obsolete|skip"'
  )

  const jiraKeys = extractJiraKeys(runningTests)
    .map((issue) => issue.replace('@', ''))
    .filter((value, index, self) => self.indexOf(value) === index)

  const issuesShouldBeUpdated = await filterOutAutomatedIssues(jiraKeys)

  if (issuesShouldBeUpdated.length === 0) {
    console.log('No issues should be updated.')
    return
  }

  await bulkUpdateJiraIssues(issuesShouldBeUpdated, 'automated')
}

main()
