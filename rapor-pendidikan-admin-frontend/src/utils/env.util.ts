/**
 * @file This file contains the configuration and environment variables for the e2e tests.
 */

import { resolve } from 'app-root-path'
import { config } from 'dotenv'
import { parseEnv } from 'znv'
import { z } from 'zod'

config({ path: resolve('.env') })

export const {
  ENVIRONMENT,
  CI,
  PRODUCTION_BASE_URL,
  STAGING_BASE_URL,
  RUN_EXAMPLES,
  AUTH_CACHE_EXPIRY_TIME_DAYS,
  AUTH_DEBUG,
  AUTH_LOGIN_SEQUENTIAL,
  PRODUCTION_ONLY,
  STAGING_ONLY,
  AUTH_SETUP_ENABLED,
  IAP_ENABLED,
  DEV_CUSTOM_REPORTER,
  TRIBE_NAME,
  GROUP_NAME,
  CI_PROJECT_NAME,
  ENV_CUSTOM_REPORTER,
  USE_GREP_FILTER,
} = parseEnv(process.env, {
  PRODUCTION_ONLY: z.boolean().default(false),
  STAGING_ONLY: z.boolean().default(true),
  AUTH_SETUP_ENABLED: z.boolean().default(true),
  PRODUCTION_BASE_URL: z.string().url(),
  STAGING_BASE_URL: z.string().url(),
  RUN_EXAMPLES: z.boolean().default(true),
  AUTH_CACHE_EXPIRY_TIME_DAYS: z.number().default(7),
  CI: z.boolean().default(false),
  AUTH_DEBUG: z.boolean().default(true),
  AUTH_LOGIN_SEQUENTIAL: z.boolean().default(false),
  ENVIRONMENT: z
    .enum(['staging', 'production', 'STAGING', 'PRODUCTION'])
    .default('staging')
    .transform((v) => v.toLowerCase() as 'staging' | 'production'),
  IAP_ENABLED: z.boolean().default(true),
  DEV_CUSTOM_REPORTER: z.boolean().default(false),
  ENV_CUSTOM_REPORTER: z.enum(['staging', 'production']).default('staging'),
  TRIBE_NAME: z.string().default(''),
  GROUP_NAME: z.string().default(''),
  CI_PROJECT_NAME: z.string().default('testing'),
  /** This can be useful when we need to run all tests in CI, such as when running the e2e update Jira labels script. */
  USE_GREP_FILTER: z.boolean().default(true),
  ...dynamicEnv(),
})

function dynamicEnv() {
  let env = {}

  if (process.env.STAGING_ONLY) {
    env = { ...env, PRODUCTION_BASE_URL: z.string().url().optional() }
  }

  if (process.env.PRODUCTION_ONLY) {
    env = { ...env, STAGING_BASE_URL: z.string().url().optional() }
  }

  return env
}
