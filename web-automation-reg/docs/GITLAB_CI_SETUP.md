# GitLab CI Setup for Web Automation Reg

This document provides comprehensive instructions for setting up and running the Web Automation Reg project on GitLab CI/CD.

## Overview

The Web Automation Reg project is configured to run automated tests on GitLab CI using the existing `./run_scaled.sh -a` command without any changes to the current test execution flow.

## GitLab CI Configuration

### Pipeline Structure

The GitLab CI pipeline consists of two stages:

1. **Test Stage**: Executes the automated tests
2. **Report Stage**: Generates comprehensive test reports

### Jobs

#### Test Jobs
- **e2e-test-main**: Runs on main/develop branches
- **e2e-test-mr**: Runs on merge requests
- **e2e-test-feature**: Runs on feature branches

#### Report Job
- **generate-report**: Analyzes test results and generates reports

### Environment Variables

The following environment variables are automatically set in GitLab CI:

```yaml
# Test Configuration
TEST_FEATURES_DIR: "./test_framework/features"
TEST_OUTPUT_DIR: "./output"
TEST_REPORTS_DIR: "./test_framework/reports"

# Xray Integration
XRAY_ENABLED: "true"
XRAY_BASE_URL: "https://wartek.atlassian.net"
XRAY_PROJECT_KEY: "RP"
XRAY_ENVIRONMENT: "staging"

# CI-Specific Settings
CI: "true"
HEADLESS: "true"
BROWSER_HEADLESS: "true"
GENERATE_JUNIT_XML: "true"
```

## Setup Instructions

### 1. Repository Setup

Ensure your GitLab repository contains:
- `.gitlab-ci.yml` (GitLab CI configuration)
- `run_scaled.sh` (main test execution script)
- `requirements.txt` (Python dependencies)
- `test_framework/` directory with feature files
- `scripts/` directory with utility scripts

### 2. GitLab CI/CD Variables

Set the following variables in GitLab Project Settings > CI/CD > Variables:

#### Required Variables (for Xray integration)
```
XRAY_USERNAME: <EMAIL>
XRAY_API_TOKEN: your_jira_api_token
```

#### Optional Variables
```
XRAY_TEST_PLAN_KEY: RP-XXX (if linking to specific test plan)
XRAY_TEST_EXECUTION_KEY: RP-XXX (if linking to specific test execution)
STAGING_BASE_URL: https://rumah-baru.staging.belajar.id/
PRODUCTION_BASE_URL: https://rumah.pendidikan.go.id/
```

### 3. GitLab Runner Requirements

Ensure your GitLab runners have:
- Docker support
- Sufficient resources (minimum 2GB RAM, 2 CPU cores)
- Network access to test environments

## Pipeline Execution

### Automatic Triggers

The pipeline automatically runs on:
- Pushes to `main` branch (production environment)
- Pushes to `develop` branch (staging environment)
- Merge request creation/updates (staging environment)
- Pushes to feature branches (staging environment)

### Manual Execution

You can manually trigger pipelines from:
- GitLab UI: Project > CI/CD > Pipelines > Run Pipeline
- GitLab API
- Git push to trigger branches

### Environment-Specific Execution

#### Staging Environment
```bash
# Triggered automatically on develop/feature branches
# Uses staging environment settings
```

#### Production Environment
```bash
# Triggered automatically on main branch
# Uses production environment settings
```

## Test Execution Flow

### 1. Environment Setup
- Install Python 3.9 and dependencies
- Install Playwright browsers
- Set up test directories
- Configure environment variables

### 2. Test Execution
```bash
# The existing command runs without modification
./run_scaled.sh -a
```

### 3. Result Processing
- Generate JUnit XML reports
- Update Xray test executions
- Create GitLab CI artifacts
- Generate test summaries

## Artifacts and Reports

### Generated Artifacts
- `junit.xml`: JUnit test results for GitLab integration
- `test_framework/reports/`: Detailed test reports
- `output/`: Test execution outputs
- Screenshots and logs (if enabled)

### GitLab Integration
- **Test Results**: Displayed in GitLab merge request UI
- **JUnit Reports**: Integrated with GitLab's test reporting
- **Artifacts**: Downloadable from pipeline jobs
- **Test Coverage**: Available in pipeline overview

## Monitoring and Debugging

### Pipeline Status
Monitor pipeline status in:
- GitLab Project > CI/CD > Pipelines
- Merge request UI (for MR pipelines)
- Email notifications (if configured)

### Debugging Failed Pipelines

#### 1. Check Job Logs
```bash
# View detailed logs in GitLab UI
Project > CI/CD > Pipelines > [Pipeline] > [Job]
```

#### 2. Download Artifacts
```bash
# Download artifacts for local analysis
Project > CI/CD > Pipelines > [Pipeline] > [Job] > Browse Artifacts
```

#### 3. Common Issues

**Dependency Installation Failures**
```bash
# Check requirements.txt and Python version compatibility
# Verify network connectivity in runner environment
```

**Test Execution Failures**
```bash
# Check feature file syntax
# Verify test environment accessibility
# Review browser/Playwright configuration
```

**Xray Integration Issues**
```bash
# Verify XRAY_USERNAME and XRAY_API_TOKEN variables
# Check network connectivity to Jira instance
# Review Xray permissions
```

## Local Validation

### Validate GitLab CI Configuration
```bash
# Run validation script
chmod +x scripts/validate_gitlab_ci.sh
./scripts/validate_gitlab_ci.sh
```

### Test Local Execution
```bash
# Simulate CI environment locally
export CI=true
export GITLAB_CI=true
export HEADLESS=true

# Run tests
./run_scaled.sh -a
```

## Performance Optimization

### Pipeline Speed
- **Caching**: Dependencies cached between runs
- **Parallel Execution**: Tests run in parallel within jobs
- **Optimized Images**: Using official Playwright Docker images

### Resource Usage
- **Memory**: 2GB minimum, 4GB recommended
- **CPU**: 2 cores minimum, 4 cores recommended
- **Storage**: 10GB for artifacts and cache

## Security Considerations

### Sensitive Data
- Store credentials in GitLab CI/CD variables (masked)
- Never commit API tokens or passwords
- Use protected variables for production environments

### Network Security
- Ensure runners can access test environments
- Configure firewall rules if necessary
- Use VPN or private networks for sensitive environments

## Troubleshooting

### Common Error Messages

#### "run_scaled.sh: Permission denied"
```bash
# Fixed automatically in CI, but ensure file is executable
chmod +x run_scaled.sh
```

#### "No feature files found"
```bash
# Ensure feature files exist in test_framework/features/
ls -la test_framework/features/*.feature
```

#### "Playwright browsers not found"
```bash
# Browsers are installed automatically in CI
# For local testing: playwright install
```

#### "Xray authentication failed"
```bash
# Check XRAY_USERNAME and XRAY_API_TOKEN variables
# Verify Jira account permissions
```

## Best Practices

### 1. Branch Strategy
- Use feature branches for development
- Test on staging before merging to main
- Use merge requests for code review

### 2. Test Organization
- Keep feature files organized and named consistently
- Use meaningful scenario names and descriptions
- Include proper Xray tags (@TEST_RP-XXX)

### 3. Pipeline Efficiency
- Run full test suite on main/develop branches
- Run subset of tests on feature branches
- Use parallel execution for faster feedback

### 4. Monitoring
- Monitor pipeline success rates
- Review test execution times
- Track Xray integration status

## Support and Maintenance

### Regular Tasks
- Update Python dependencies in requirements.txt
- Refresh Playwright browser versions
- Review and update GitLab CI configuration
- Monitor pipeline performance and optimize

### Getting Help
- Check GitLab CI job logs for detailed error information
- Review this documentation for common issues
- Contact the development team for complex problems

## Changelog

- **v1.0**: Initial GitLab CI setup with Xray integration
- **v1.1**: Added environment-specific configurations
- **v1.2**: Optimized caching and performance
- **v1.3**: Enhanced error handling and reporting
