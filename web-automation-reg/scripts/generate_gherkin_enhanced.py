#!/usr/bin/env python3
"""
Enhanced Smart Gherkin Generator
Converts Excel test cases to Gherkin feature files with intelligent coverage enhancement
"""

import os
import sys
import pandas as pd
import re
from typing import Dict, List, Tuple, Set
from datetime import datetime
import argparse

class EnhancedGherkinGenerator:
    def __init__(self, excel_file: str, output_dir: str = "test_framework/features"):
        self.excel_file = excel_file
        self.output_dir = output_dir
        self.test_cases = {}
        self.feature_templates = {}
        self.coverage_enhancements = {}
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Load test case patterns for enhancement
        self._load_enhancement_patterns()
        
    def _load_enhancement_patterns(self):
        """Load patterns for generating additional test scenarios."""
        self.coverage_enhancements = {
            'navigation': {
                'patterns': ['buka', 'navigate', 'go to', 'visit', 'klik'],
                'enhancements': [
                    'navigation_timeout',
                    'negative_navigation',
                    'navigation_with_invalid_url'
                ],
                'negative_enhancements': [
                    'negative_navigation',
                    'navigation_with_invalid_url'
                ]
            },
            'input': {
                'patterns': ['isi', 'ketik', 'type', 'input', 'masukkan'],
                'enhancements': [
                    'very_long_input',
                    'empty_input',
                    'special_characters'
                ],
                'negative_enhancements': [
                    'empty_input',
                    'special_characters'
                ]
            },
            'verification': {
                'patterns': ['pastikan', 'verify', 'assert', 'check'],
                'enhancements': [
                    'partial_text_match',
                    'negative_verification'
                ],
                'negative_enhancements': [
                    'negative_verification'
                ]
            },
            'search': {
                'patterns': ['cari', 'search', 'pencarian'],
                'enhancements': [
                    'search_with_no_results',
                    'search_with_special_characters'
                ],
                'negative_enhancements': [
                    'search_with_no_results',
                    'search_with_special_characters'
                ]
            },
            'scroll': {
                'patterns': ['scroll', 'gulir'],
                'enhancements': [
                    'scroll_to_top',
                    'scroll_to_bottom'
                ],
                'negative_enhancements': []
            }
        }
    
    def read_excel_tests(self) -> Dict[str, Dict]:
        """Read test cases from Excel file."""
        print(f"📖 Reading test cases from: {self.excel_file}")
        
        try:
            df = pd.read_excel(self.excel_file)
            test_cases = {}
            
            for _, row in df.iterrows():
                tc_id = row['Test Case ID']
                feature = row['Feature']
                scenario = row['Scenarios']
                steps = row['Test Steps']
                
                # Parse steps
                step_list = self._parse_steps(steps)
                
                test_cases[tc_id] = {
                    'feature': feature,
                    'scenario': scenario,
                    'steps': step_list,
                    'base_url': self._extract_base_url(steps)
                }
                
            print(f"✅ Loaded {len(test_cases)} test cases")
            return test_cases
            
        except Exception as e:
            print(f"❌ Error reading Excel file: {e}")
            return {}
    
    def _parse_steps(self, steps_text: str) -> List[str]:
        """Parse test steps from text."""
        if pd.isna(steps_text):
            return []
        
        # Split by numbered steps (1., 2., etc.)
        steps = re.split(r'\d+\.\s*', steps_text)
        # Remove empty steps and clean up
        steps = [step.strip() for step in steps if step.strip()]
        return steps
    
    def _extract_base_url(self, steps_text: str) -> str:
        """Extract base URL from test steps."""
        if pd.isna(steps_text):
            return ""
        
        # Look for URL patterns
        url_patterns = [
            r'https?://[^\s]+',
            r'base_url[:\s]*([^\s]+)',
            r'url[:\s]*([^\s]+)'
        ]
        
        for pattern in url_patterns:
            match = re.search(pattern, steps_text, re.IGNORECASE)
            if match:
                return match.group(0) if '://' in match.group(0) else match.group(1)
        
        return ""
    
    def _categorize_test_case(self, steps: List[str]) -> List[str]:
        """Categorize test case based on its steps."""
        categories = []
        steps_text = ' '.join(steps).lower()
        
        for category, config in self.coverage_enhancements.items():
            if any(pattern in steps_text for pattern in config['patterns']):
                categories.append(category)
        
        return categories
    
    def _generate_enhanced_scenarios(self, original_steps: List[str], categories: List[str], exclude_negative: bool = False) -> List[Dict]:
        """Generate enhanced test scenarios based on categories."""
        enhanced_scenarios = []
        
        for category in categories:
            if exclude_negative:
                # Filter out negative enhancements
                enhancements = [enh for enh in self.coverage_enhancements[category]['enhancements'] 
                              if enh not in self.coverage_enhancements[category].get('negative_enhancements', [])]
            else:
                enhancements = self.coverage_enhancements[category]['enhancements']
            
            for enhancement in enhancements:
                enhanced_steps = self._apply_enhancement(original_steps, category, enhancement)
                if enhanced_steps and enhanced_steps != original_steps:
                    enhanced_scenarios.append({
                        'type': enhancement,
                        'category': category,
                        'steps': enhanced_steps,
                        'description': self._get_enhancement_description(enhancement)
                    })
        
        return enhanced_scenarios
    
    def _apply_enhancement(self, original_steps: List[str], category: str, enhancement: str) -> List[str]:
        """Apply specific enhancement to test steps."""
        enhanced_steps = original_steps.copy()
        
        if category == 'input':
            if enhancement == 'empty_input':
                # Replace input steps with empty values
                for i, step in enumerate(enhanced_steps):
                    if any(pattern in step.lower() for pattern in ['isi', 'ketik', 'type']):
                        enhanced_steps[i] = step.replace('dengan kata "', 'dengan kata ""')
                        break
                        
            elif enhancement == 'special_characters':
                # Replace input with special characters
                for i, step in enumerate(enhanced_steps):
                    if any(pattern in step.lower() for pattern in ['isi', 'ketik', 'type']):
                        enhanced_steps[i] = step.replace('dengan kata "', 'dengan kata "!@#$%^&*()"')
                        break
                        
        elif category == 'verification':
            if enhancement == 'negative_verification':
                # Add negative verification steps
                for i, step in enumerate(enhanced_steps):
                    if 'pastikan' in step.lower():
                        enhanced_steps[i] = step.replace('muncul', 'tidak muncul')
                        break
                        
        elif category == 'search':
            if enhancement == 'search_with_no_results':
                # Modify search to use non-existent term
                for i, step in enumerate(enhanced_steps):
                    if 'cari' in step.lower() or 'search' in step.lower():
                        enhanced_steps[i] = step.replace('dengan kata "', 'dengan kata "XYZ123NONEXISTENT"')
                        break
        
        return enhanced_steps
    
    def _get_enhancement_description(self, enhancement: str) -> str:
        """Get human-readable description for enhancement."""
        descriptions = {
            'empty_input': 'Empty Input Validation',
            'special_characters': 'Special Characters Input',
            'very_long_input': 'Very Long Input Test',
            'negative_verification': 'Negative Verification',
            'partial_text_match': 'Partial Text Match',
            'search_with_no_results': 'Search with No Results',
            'search_with_special_characters': 'Search with Special Characters',
            'scroll_to_top': 'Scroll to Top',
            'scroll_to_bottom': 'Scroll to Bottom',
            'negative_navigation': 'Negative Navigation',
            'navigation_with_invalid_url': 'Navigation with Invalid URL',
            'navigation_timeout': 'Navigation Timeout'
        }
        return descriptions.get(enhancement, enhancement.replace('_', ' ').title())
    
    def _group_test_cases_by_feature(self, test_cases: Dict[str, Dict]) -> Dict[str, List[Tuple[str, Dict]]]:
        """Group test cases by feature."""
        feature_groups = {}
        
        for tc_id, tc_data in test_cases.items():
            feature = tc_data['feature']
            if feature not in feature_groups:
                feature_groups[feature] = []
            feature_groups[feature].append((tc_id, tc_data))
        
        return feature_groups
    
    def _generate_feature_content(self, feature_name: str, test_cases: List[Tuple[str, Dict]], enhanced_scenarios: List[Dict] = None) -> str:
        """Generate Gherkin feature file content."""
        content = []
        
        # Feature header
        content.append(f"Feature: {feature_name}")
        content.append(f"  As a user")
        content.append(f"  I want to access {feature_name.lower()} functionality")
        content.append(f"  So that I can perform {feature_name.lower()} operations")
        content.append("")
        
        # Base URL comment
        if test_cases and test_cases[0][1].get('base_url'):
            content.append(f"  # base_url: {test_cases[0][1]['base_url']}")
            content.append("")
        
        # Original test cases
        for tc_id, tc_data in test_cases:
            content.append(f"  @{tc_id}")
            content.append(f"  Scenario: {tc_data['scenario']}")
            for step in tc_data['steps']:
                gherkin_step = self._convert_to_gherkin(step)
                if gherkin_step:  # Only add non-empty steps
                    content.append(f"    {gherkin_step}")
            content.append("")
        
        # Enhanced scenarios
        if enhanced_scenarios:
            for scenario in enhanced_scenarios:
                scenario_id = f"TC{len(test_cases) + enhanced_scenarios.index(scenario) + 1}"
                content.append(f"  @{scenario_id}")
                content.append(f"  Scenario: {scenario['description']}")
                for step in scenario['steps']:
                    gherkin_step = self._convert_to_gherkin(step)
                    if gherkin_step:  # Only add non-empty steps
                        content.append(f"    {gherkin_step}")
                content.append("")
        
        return "\n".join(content)
    
    def _convert_to_gherkin(self, step: str) -> str:
        """Convert natural language step to Gherkin format."""
        if not step or step.strip() == "":
            return ""
            
        step_lower = step.lower()
        
        # Navigation steps
        if any(pattern in step_lower for pattern in ['buka halaman', 'open page', 'navigate']):
            if 'utama' in step_lower or 'main' in step_lower:
                return "Given I am on the main page"
            else:
                return f"Given I am on the {step.split()[-1]} page"
        
        # Input steps
        elif any(pattern in step_lower for pattern in ['isi', 'ketik', 'type']):
            if 'pencarian' in step_lower or 'search' in step_lower:
                value = self._extract_quoted_text(step)
                if value:
                    return f"When I type \"{value}\" in the search field"
                else:
                    return "When I type in the search field"
            else:
                value = self._extract_quoted_text(step)
                field = self._extract_field_name(step)
                if value:
                    return f"When I type \"{value}\" in the {field} field"
                else:
                    return f"When I type in the {field} field"
        
        # Click steps
        elif any(pattern in step_lower for pattern in ['klik', 'click']):
            target = self._extract_quoted_text(step)
            if 'tombol' in step_lower or 'button' in step_lower:
                if target:
                    return f"And I click the \"{target}\" button"
                else:
                    return "And I click the button"
            else:
                if target:
                    return f"And I click \"{target}\""
                else:
                    return "And I click the element"
        
        # Verification steps
        elif any(pattern in step_lower for pattern in ['pastikan', 'verify', 'assert']):
            text = self._extract_quoted_text(step)
            if 'tidak muncul' in step_lower or 'not appear' in step_lower:
                if text:
                    return f"Then I should not see \"{text}\""
                else:
                    return "Then I should not see the element"
            else:
                if text:
                    return f"Then I should see \"{text}\""
                else:
                    return "Then I should see the element"
        
        # Scroll steps
        elif 'scroll' in step_lower or 'gulir' in step_lower:
            target = self._extract_quoted_text(step)
            if target:
                return f"When I scroll to the \"{target}\" section"
            else:
                return "When I scroll down"
        
        # Navigation back
        elif 'berpindah ke halaman sebelumnya' in step_lower or 'go back' in step_lower:
            return "When I navigate back to the previous page"
        
        # Default: return as is if it's a meaningful step
        if len(step.strip()) > 3:
            return step
        return ""
    
    def _extract_quoted_text(self, text: str) -> str:
        """Extract text between quotes."""
        match = re.search(r'"([^"]+)"', text)
        return match.group(1) if match else ""
    
    def _extract_field_name(self, text: str) -> str:
        """Extract field name from step."""
        field_patterns = [
            r'kolom\s+([^\s]+)',
            r'field\s+([^\s]+)',
            r'input\s+([^\s]+)'
        ]
        
        for pattern in field_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "input"
    
    def generate_gherkin_files(self, enhance_coverage: bool = True, max_enhancements: int = 2, exclude_negative: bool = False):
        """Generate Gherkin feature files from Excel test cases."""
        print("🚀 Starting Enhanced Smart Gherkin Generation...")
        
        if exclude_negative:
            print("⚠️  Negative test scenarios will be excluded")
        
        # Read test cases
        self.test_cases = self.read_excel_tests()
        if not self.test_cases:
            print("❌ No test cases found. Exiting.")
            return
        
        # Group by feature
        feature_groups = self._group_test_cases_by_feature(self.test_cases)
        
        total_scenarios = 0
        enhanced_scenarios = 0
        
        for feature_name, test_cases in feature_groups.items():
            print(f"\n📝 Processing feature: {feature_name}")
            
            # Generate enhanced scenarios if requested
            all_enhanced_scenarios = []
            if enhance_coverage:
                for tc_id, tc_data in test_cases:
                    categories = self._categorize_test_case(tc_data['steps'])
                    enhanced = self._generate_enhanced_scenarios(tc_data['steps'], categories, exclude_negative)
                    all_enhanced_scenarios.extend(enhanced[:max_enhancements])
                    enhanced_scenarios += len(enhanced[:max_enhancements])
            
            # Generate feature file content
            content = self._generate_feature_content(feature_name, test_cases, all_enhanced_scenarios)
            
            # Write to file
            filename = self._sanitize_filename(feature_name)
            filepath = os.path.join(self.output_dir, f"{filename}.feature")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            total_scenarios += len(test_cases)
            print(f"✅ Generated: {filepath}")
            print(f"   - Original scenarios: {len(test_cases)}")
            print(f"   - Enhanced scenarios: {len(all_enhanced_scenarios)}")
        
        print(f"\n🎉 Generation Complete!")
        print(f"📊 Summary:")
        print(f"   - Total original scenarios: {total_scenarios}")
        print(f"   - Total enhanced scenarios: {enhanced_scenarios}")
        print(f"   - Total scenarios: {total_scenarios + enhanced_scenarios}")
        print(f"   - Feature files generated: {len(feature_groups)}")
        print(f"   - Output directory: {self.output_dir}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for file system."""
        # Remove special characters and replace spaces with underscores
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.lower()
    
    def generate_coverage_report(self):
        """Generate a coverage report showing what types of tests were created."""
        print("\n📋 Coverage Report")
        print("=" * 50)
        
        coverage_stats = {
            'navigation': 0,
            'input': 0,
            'verification': 0,
            'search': 0,
            'scroll': 0
        }
        
        for tc_id, tc_data in self.test_cases.items():
            categories = self._categorize_test_case(tc_data['steps'])
            for category in categories:
                coverage_stats[category] += 1
        
        for category, count in coverage_stats.items():
            percentage = (count / len(self.test_cases)) * 100 if self.test_cases else 0
            print(f"{category.title():15} {count:3d} scenarios ({percentage:5.1f}%)")
        
        print("=" * 50)

def main():
    parser = argparse.ArgumentParser(description="Enhanced Smart Gherkin Generator")
    parser.add_argument("--excel", default="test_framework/tests.xlsx", 
                       help="Path to Excel test file")
    parser.add_argument("--output", default="test_framework/features",
                       help="Output directory for feature files")
    parser.add_argument("--no-enhance", action="store_true",
                       help="Disable coverage enhancement")
    parser.add_argument("--no-negative", action="store_true",
                       help="Exclude negative test scenarios")
    parser.add_argument("--max-enhancements", type=int, default=2,
                       help="Maximum enhancements per test case")
    parser.add_argument("--report", action="store_true",
                       help="Generate coverage report")
    
    args = parser.parse_args()
    
    # Check if Excel file exists
    if not os.path.exists(args.excel):
        print(f"❌ Excel file not found: {args.excel}")
        return
    
    # Create generator
    generator = EnhancedGherkinGenerator(args.excel, args.output)
    
    # Generate Gherkin files
    generator.generate_gherkin_files(
        enhance_coverage=not args.no_enhance,
        max_enhancements=args.max_enhancements,
        exclude_negative=args.no_negative
    )
    
    # Generate coverage report if requested
    if args.report:
        generator.generate_coverage_report()

if __name__ == "__main__":
    main() 