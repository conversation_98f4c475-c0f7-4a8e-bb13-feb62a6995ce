import { USERS_TESTING } from '@/auth/users'
import { USER_IAP, UserSchema } from '@/constants/users.constant'
import { IAP_ENABLED, PRODUCTION_ONLY } from '@/utils/env.util'
import { z } from 'zod'

const validateUser = (user: z.infer<typeof UserSchema>) => UserSchema.safeParse(user)

export const validateUsersTesting = () => {
  if (IAP_ENABLED && !PRODUCTION_ONLY) {
    const validationResult = validateUser(USER_IAP)

    if (!validationResult.success) {
      throw `🔑 !! Make sure the IAP user credentials are valid, especially for User IAP in 'src/auth/users.ts' and env`
    }
  }

  const invalidUsers = Object.entries(USERS_TESTING)
    .filter(([key]) => key !== 'IAP')
    .reduce((acc, [key, value]) => {
      const validationResult = validateUser(value)

      if (!validationResult.success) {
        acc[key] = !validationResult.success
      }

      return acc
    }, {} as Record<string, boolean>)

  if (Object.keys(invalidUsers).length > 0) {
    const invalidUserMessage = Object.keys(invalidUsers).join(', ')
    throw `🔑 !! Make sure all the users credentials are valid, especially for: ${invalidUserMessage} in 'src/auth/users.ts' and env`
  } else {
    console.log(`🔑 All (${Object.keys(USERS_TESTING).length}) users credentials are filled`)
    return true
  }
}

export const SchemaUserProcessEnv = Object.keys(USERS_TESTING).reduce(
  (acc, user) => acc.setKey(`USER_EMAIL_${user}`, z.string().min(3)),
  z.object({})
)

SchemaUserProcessEnv.parse(process.env)
