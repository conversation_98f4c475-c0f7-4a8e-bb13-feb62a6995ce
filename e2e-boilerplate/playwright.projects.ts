import { STORAGE_STATE_IAP } from '@/auth/google-login.helper'
import { getAuthFile } from '@/helpers/global.helper'
import {
  AUTH_SETUP_ENABLED,
  IAP_ENABLED,
  PRODUCTION_BASE_URL,
  PRODUCTION_ONLY,
  STAGING_BASE_URL,
  STAGING_ONLY,
} from '@/utils/env.util'
import {
  authFile,
  defaultDesktopProjectUse,
  defaultMobileProjectUse,
  environmentSetup,
  generateGrep,
} from '@/utils/playwright-config.util'
import { PlaywrightTestProject } from '@playwright/test'

type Device = 'mobile' | 'desktop'
type Environment = 'production' | 'staging'

let ENVIRONMENTS = ['production', 'staging'] as Environment[]

const DEVICE_EACH_ENV: Device[] = ['mobile']
const TAGS_EACH_DEVICE = [
  { tag: '', grepInvert: new RegExp(`@anonym`, 'i'), grepAnd: undefined },
  { tag: 'anonym', grepInvert: undefined, grepAnd: ['@anonym'] },
] as const

export const playwrightProjects: PlaywrightTestProject[] = [...environmentSetup, ...generateProjects()]

function generateProjects() {
  const projects: PlaywrightTestProject[] = []

  if (PRODUCTION_ONLY) ENVIRONMENTS = ['production']
  if (STAGING_ONLY) ENVIRONMENTS = ['staging']

  for (const environment of ENVIRONMENTS) {
    for (const device of DEVICE_EACH_ENV) {
      for (const { tag, grepInvert, grepAnd } of TAGS_EACH_DEVICE) {
        const name = `${environment}.${device}${tag ? `.${tag}` : ''}`
        const deviceUse = device === 'mobile' ? defaultMobileProjectUse : defaultDesktopProjectUse

        const shouldUseIAP = IAP_ENABLED && environment === 'staging'
        const storageStateIAP = shouldUseIAP ? getAuthFile(STORAGE_STATE_IAP) : undefined

        let storageState = AUTH_SETUP_ENABLED ? getAuthFile(authFile, environment) : storageStateIAP

        if (tag === 'anonym') {
          if (environment === 'staging') {
            storageState = IAP_ENABLED ? STORAGE_STATE_IAP : undefined
          } else if (environment === 'production') {
            storageState = undefined
          }
        }

        const dependencies = AUTH_SETUP_ENABLED ? [`${environment}.setup`] : []
        if (IAP_ENABLED && !PRODUCTION_ONLY) dependencies.push('iap.setup')

        projects.push({
          name,
          grep: generateGrep(environment, { AND: grepAnd }),
          grepInvert,
          dependencies,
          use: {
            ...deviceUse,
            storageState,
            baseURL: environment === 'production' ? PRODUCTION_BASE_URL : STAGING_BASE_URL,
          },
        })
      }
    }
  }

  return projects
}
