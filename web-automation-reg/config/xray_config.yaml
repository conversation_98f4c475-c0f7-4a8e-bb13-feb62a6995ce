# Xray Integration Configuration for Web Automation Reg
# This file configures the integration with Jira Xray for test execution reporting

# Xray API Configuration
xray:
  # Enable/disable Xray integration
  enabled: true
  
  # Jira/Xray instance URL
  base_url: "https://wartek.atlassian.net"
  
  # API version for Xray REST API
  api_version: "1.0"
  
  # Project configuration
  project_key: "RP"  # Default project key for Rumah Pendidikan
  
  # Test execution configuration
  # These can be set via environment variables or left empty for dynamic creation
  test_plan_key: ""  # Optional: Link to specific test plan
  test_execution_key: ""  # Optional: Link to specific test execution
  
  # Environment mapping
  environments:
    staging: "staging"
    production: "production"
  
  # Default environment if not specified in test tags
  default_environment: "staging"
  
  # API timeout and retry settings
  timeout: 30  # seconds
  retry_attempts: 3
  retry_delay: 5  # seconds between retries
  
  # Test result mapping
  status_mapping:
    passed: "PASS"
    failed: "FAIL"
    skipped: "TODO"
    error: "FAIL"
  
  # JUnit XML import settings
  import:
    # Whether to create new test execution if none specified
    create_test_execution: true
    
    # Whether to update existing test execution
    update_existing: true
    
    # Include test environment in execution
    include_environment: true
    
    # Include execution time in results
    include_execution_time: true

# Authentication Configuration
# These should be set via environment variables for security:
# XRAY_USERNAME - Jira username or email
# XRAY_PASSWORD - Jira password or API token
# XRAY_API_TOKEN - Jira API token (preferred over password)

# Environment Variables Reference:
# XRAY_ENABLED - Enable/disable Xray integration (true/false)
# XRAY_BASE_URL - Jira instance URL
# XRAY_USERNAME - Jira username
# XRAY_PASSWORD - Jira password
# XRAY_API_TOKEN - Jira API token
# XRAY_PROJECT_KEY - Project key (e.g., RP)
# XRAY_TEST_PLAN_KEY - Test plan key (optional)
# XRAY_TEST_EXECUTION_KEY - Test execution key (optional)
# XRAY_ENVIRONMENT - Default environment (staging/production)
# XRAY_TIMEOUT - API timeout in seconds
# XRAY_RETRY_ATTEMPTS - Number of retry attempts
# XRAY_RETRY_DELAY - Delay between retries in seconds

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log Xray API requests and responses
  log_requests: false
  
  # Log test result updates
  log_updates: true

# Feature File Integration
feature_integration:
  # Tag patterns to extract ticket numbers
  ticket_tag_patterns:
    - "@TEST_([A-Z]+-\\d+)"  # Matches @TEST_RP-932
  
  # Environment tag patterns
  environment_tag_patterns:
    - "@(staging|production)"
  
  # Test type tag patterns
  test_type_tag_patterns:
    - "@(regression_test|smoke_test|ui_test|api_test)"

# CI/CD Integration
ci_integration:
  # Enable integration in CI environment
  enabled_in_ci: true
  
  # CI environment detection
  ci_environment_vars:
    - "CI"
    - "GITLAB_CI"
    - "GITHUB_ACTIONS"
  
  # Artifacts configuration
  artifacts:
    # Save JUnit XML to root directory for CI artifacts
    save_to_root: true
    
    # JUnit XML filename
    junit_filename: "junit.xml"
    
    # Additional report formats
    generate_xray_report: true
