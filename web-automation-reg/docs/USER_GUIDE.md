# 📖 User Guide - Test Automation Framework

## 🎯 Overview

This comprehensive guide covers everything you need to know to use the Test Automation Framework effectively. The framework supports both Excel and Gherkin (BDD) formats for writing and executing automated web tests.

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** with virtual environment
2. **FFmpeg** for video processing (optional)
3. **Web browser** (Chrome/Chromium recommended)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd web-automation-reg

# Create and activate virtual environment
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### Running Tests

The framework provides 4 main execution scripts, each optimized for different use cases:

#### 🚀 run_gherkin.sh (Recommended)
**Purpose**: Execute Gherkin/BDD feature files with modern test framework
**Best for**: Regular testing, development, and most use cases

```bash
# Run all Gherkin tests
./run_gherkin.sh

# Run specific test case
./run_gherkin.sh @TEST_RP-123

# Run multiple test cases
./run_gherkin.sh @TEST_RP-123 @TEST_RP-124 @TEST_RP-125

# Run with environment variables
DEBUG=true ./run_gherkin.sh @TEST_RP-123
RECORDING=false ./run_gherkin.sh @TEST_RP-123
CI=true ./run_gherkin.sh @TEST_RP-123
```

#### ⚡ run_scaled.sh (High Performance)
**Purpose**: Parallel execution for large test suites with advanced options
**Best for**: Large test suites, CI/CD pipelines, performance testing

```bash
# Run all tests in parallel (auto-detect available tests)
./run_scaled.sh -a

# Run specific tests with parallel execution
./run_scaled.sh @TEST_RP-123 @TEST_RP-124 @TEST_RP-125

# Custom parallel configuration
./run_scaled.sh -m parallel -w 4 -c 10 @TEST_RP-123

# Run with specific strategy
./run_scaled.sh -s hybrid @TEST_RP-123
./run_scaled.sh -s sequential @TEST_RP-123

# Advanced options
./run_scaled.sh --max-tests 20 --max-workers 6 --chunk-size 3 -a
```

**Available Options:**
- `-a, --all`: Run all available tests
- `-m, --mode`: Execution mode (parallel, sequential, hybrid)
- `-w, --max-workers`: Number of parallel workers (default: 4)
- `-c, --chunk-size`: Tests per chunk (default: 5)
- `-s, --strategy`: Execution strategy (hybrid, parallel, sequential)
- `--max-tests`: Maximum number of tests to run
- `--feature-file`: Specific feature file to run

#### 📊 run.sh (Legacy Excel Support)
**Purpose**: Execute Excel-based test cases (legacy format)
**Best for**: Existing Excel test suites, backward compatibility

```bash
# Run all Excel tests
./run.sh

# Run specific test case
./run.sh @TC1

# Run multiple test cases
./run.sh @TC1 @TC2 @TC3
```

**Note**: This script runs Excel-based tests from `test_framework/tests.xlsx`. While still functional, Gherkin format is recommended for new tests.

#### 📋 open_report.sh (Report Viewer)
**Purpose**: Quickly open the latest HTML test report
**Best for**: Viewing test results after execution

```bash
# Open the latest HTML report in browser
./open_report.sh
```

**Features:**
- Automatically finds the latest HTML report
- Cross-platform browser opening (macOS, Linux, Windows)
- Error handling if no reports are found

### 📊 Script Comparison Guide

| Script | Format | Performance | Use Case | Status |
|--------|--------|-------------|----------|--------|
| `run_gherkin.sh` | Gherkin/BDD | Fast | **Recommended** for all new tests | ✅ Active |
| `run_scaled.sh` | Gherkin/BDD | **Ultra-Fast** | Large test suites, CI/CD | ✅ Active |
| `run.sh` | Excel | Fast | Legacy Excel tests only | ⚠️ Legacy |
| `open_report.sh` | N/A | Instant | View test reports | ✅ Active |
| `convert_features.sh` | Any → Gherkin | Instant | Convert user features to proper format | ✅ Active |

### 🎯 Which Script Should I Use?

#### For New Projects:
1. **Start with**: `./run_gherkin.sh` for development and testing
2. **Scale up with**: `./run_scaled.sh` for large test suites
3. **View results with**: `./open_report.sh`

#### For Existing Excel Tests:
1. **Continue using**: `./run.sh` for existing Excel test cases
2. **Consider migrating**: Convert Excel tests to Gherkin format
3. **Hybrid approach**: Use both formats during transition

#### For CI/CD Pipelines:
1. **Recommended**: `./run_scaled.sh -a` for maximum performance
2. **Alternative**: `./run_gherkin.sh` for simpler setups

### 🔄 Migration from Excel to Gherkin

If you have existing Excel tests and want to migrate:

```bash
# Continue using Excel tests
./run.sh @TC1

# Create equivalent Gherkin test
# Convert TC1 from Excel to @TEST_RP-123 in Gherkin

# Test the new Gherkin version
./run_gherkin.sh @TEST_RP-123

# Once verified, use Gherkin going forward
```

## 📝 Writing Tests

### Gherkin Format (Recommended)

Create `.feature` files in `test_framework/features/` directory:

```gherkin
@TEST_RP-123 @staging @regression_test
Feature: User Login Functionality

  Scenario: Successful user login
    Given the user is on the login page
    When I enter valid credentials
    And I click the login button
    Then I should see the dashboard
    And the user should be logged in
```

#### Supported Step Patterns

**Navigation Steps:**
- `Given the user is on the [page name]`
- `When I navigate to [page name]`
- `And I go to [URL]`

**Interaction Steps:**
- `When I click "[element text]"`
- `And I type "[text]" in the [field name]`
- `When I select "[option]" from [dropdown name]`

**Assertion Steps:**
- `Then I should see "[text]"`
- `And the page should display "[content]"`
- `Then the [element] should be visible`

**Wait Steps:**
- `And I wait for [number] seconds`
- `When I wait for the page to load`

### Excel Format (Legacy)

Create test cases in `test_framework/tests.xlsx`:

| Test Case ID | Step | Action | Target | Value | Expected |
|--------------|------|--------|--------|-------|----------|
| TC1 | 1 | navigate | homepage | | |
| TC1 | 2 | click | login button | | |
| TC1 | 3 | type | username field | testuser | |
| TC1 | 4 | assert | welcome message | | Welcome, testuser |

## 🏷️ Tagging System

Use tags in Gherkin files for organization and filtering:

- **Test IDs**: `@TEST_RP-XXX` (required for Xray integration)
- **Environments**: `@staging`, `@production`
- **Test Types**: `@regression_test`, `@ui_test`, `@smoke_test`, `@API_test`
- **Custom Tags**: Any additional tags for organization

## 📊 Reports and Results

### HTML Reports
- **Location**: `test_framework/reports/`
- **Features**: Step-by-step execution details, screenshots, timing
- **Quick Access**: Use `./open_report.sh` to open latest report automatically
- **Manual Access**: Open HTML files directly from `test_framework/reports/`

#### Report Types

**🔄 Sequential Execution Reports**
- **Format**: `test_report_YYYYMMDD_HHMMSS.html`
- **Content**: Individual test case execution details
- **Best for**: Single test case analysis and debugging

**⚡ Parallel Execution Reports**
- **Format**: `test_execution_report_YYYYMMDD_HHMMSS.html`
- **Content**: Unified comprehensive report with two main sections:
  - **📊 Summary Report**: Executive overview, performance metrics, features overview
  - **📋 Feature Details**: Detailed test results, step-by-step execution, error analysis
- **Navigation**: Click on feature names in summary to view detailed results
- **Best for**: Multiple test case analysis and comprehensive reporting

#### Opening Reports
```bash
# Automatic - opens latest report in browser
./open_report.sh

# Manual - navigate to reports directory
# For sequential execution:
open test_framework/reports/test_report_YYYYMMDD_HHMMSS.html

# For parallel execution:
open test_framework/reports/test_execution_report_YYYYMMDD_HHMMSS.html
```

### JUnit XML Reports
- **Location**: `./junit.xml` (root directory)
- **Purpose**: CI/CD integration and Xray import
- **Format**: Standard JUnit XML with Xray-specific properties

### Video Recordings (Optional)
- **Location**: `test_framework/test_recordings/`
- **Format**: MP4 video files
- **Content**: Full test execution recording

## 🔧 Configuration

### Basic Configuration
Edit `config/config.yaml`:

```yaml
browser:
  headless: false
  timeout: 30000
  viewport:
    width: 1920
    height: 1080

recording:
  enabled: true
  quality: high
  fps: 30

reporting:
  generate_html: true
  generate_junit: true
  include_screenshots: true
```

### Advanced Configuration
Edit `config/report_config.yaml` for report customization:

```yaml
html_report:
  theme: modern
  include_timing: true
  show_screenshots: true
  
junit_report:
  suite_name: "Web Automation Tests"
  include_properties: true
  xray_integration: true
```

## 🚀 Performance Optimization

### Parallel Execution
```bash
# Run tests in parallel (recommended for large test suites)
./run_scaled.sh -a

# Custom parallel configuration
./run_scaled.sh -m parallel -w 4 -c 10 @TC1 @TC2 @TC3
```

### Execution Modes
- **Pattern Matching**: Fast, reliable (default)
- **Hybrid Mode**: Pattern matching + AI fallback
- **CI Mode**: Ultra-fast with aggressive timeouts

## 🔍 Troubleshooting

### Common Issues

#### 1. Browser Launch Failures
**Problem**: Browser fails to start or crashes
**Solutions**:
```bash
# Reinstall browser
playwright install chromium

# Check system resources
# Reduce parallel workers if memory issues
./run_scaled.sh -w 2
```

#### 2. Element Not Found
**Problem**: Test fails to find page elements
**Solutions**:
- Check element selectors in feature files
- Verify page loading is complete
- Add wait steps before interactions
- Use more specific element descriptions

#### 3. Slow Test Execution
**Problem**: Tests take too long to complete
**Solutions**:
```bash
# Use parallel execution
./run_scaled.sh -a

# Enable CI mode for faster execution
CI=true ./run_gherkin.sh @TC1

# Reduce timeouts in config
```

#### 4. Video Recording Issues
**Problem**: Video recording fails or produces poor quality
**Solutions**:
```bash
# Install FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg

# Disable recording if not needed
RECORDING=false ./run_gherkin.sh @TC1
```

### Debug Mode
```bash
# Enable verbose logging
DEBUG=true ./run_gherkin.sh @TC1

# Generate debug screenshots
SCREENSHOTS=true ./run_gherkin.sh @TC1
```

## 📋 Best Practices

### Test Writing
1. **Use descriptive scenario names**
2. **Keep steps simple and atomic**
3. **Use consistent element descriptions**
4. **Add appropriate tags for organization**
5. **Include data tables for complex inputs**

### Test Organization
1. **Group related scenarios in feature files**
2. **Use meaningful feature file names**
3. **Organize by functionality, not by page**
4. **Keep feature files focused and cohesive**

### Execution Strategy
1. **Use parallel execution for large test suites**
2. **Run smoke tests first in CI/CD**
3. **Use appropriate tags for test selection**
4. **Monitor execution time and optimize**

## 🔗 Integration

### CI/CD Integration
```yaml
# GitLab CI example
test_automation:
  script:
    - ./run_scaled.sh -a
  artifacts:
    paths:
      - junit.xml
      - test_framework/reports/
    reports:
      junit: junit.xml
```

### Xray Integration
The framework automatically generates Xray-compatible JUnit XML reports. Simply import `junit.xml` into your Jira Xray instance.

## 📞 Support

For additional help:
1. Check the Technical Guide for implementation details
2. Review the Development History for known issues and solutions
3. Examine generated reports for detailed error information
4. Use debug mode for troubleshooting specific issues

## 🎯 Next Steps

1. **Start with simple scenarios** to familiarize yourself with the framework
2. **Gradually add more complex test cases** as you become comfortable
3. **Implement parallel execution** for better performance
4. **Integrate with your CI/CD pipeline** for automated testing
5. **Customize reports and configuration** to match your needs

## 🔄 Converting User Features to Proper Gherkin

If you have existing feature descriptions in various formats (text files, markdown, CSV, or informal Gherkin), you can convert them to proper .feature files using the conversion script.

### 📝 Feature Converter Script

The framework includes a powerful feature converter that transforms user-provided features into proper Gherkin format:

#### Using the Shell Script (Recommended)
```bash
# Convert text file to Gherkin features
./scripts/convert_features.sh my_features.txt

# Convert markdown file with custom output directory
./scripts/convert_features.sh features.md --output custom_features

# Convert with custom base URL
./scripts/convert_features.sh test_cases.csv --base-url https://example.com

# Show help and supported formats
./scripts/convert_features.sh --help
```

#### Using Python Script Directly
```bash
# Basic conversion
python3 scripts/convert_features.py my_features.txt

# With custom options
python3 scripts/convert_features.py features.md \
    --output test_framework/features \
    --base-url https://rumah.pendidikan.go.id/#/homepage
```

### 📋 Supported Input Formats

#### 1. **Text Files (.txt)**
```text
Feature: User Login
Scenario: Successful login
1. Buka halaman login
2. Isi username dengan "testuser"
3. Isi password dengan "testpass"
4. Klik tombol login
5. Pastikan user berhasil login
```

#### 2. **Markdown Files (.md)**
```markdown
# Feature: Search Functionality

## Scenario: Search for training programs
- Navigate to homepage
- Type "Pelatihan" in search box
- Click search button
- Verify "Pelatihan" appears in results
```

#### 3. **CSV Files (.csv)**
```csv
Feature,Scenario,Steps,Tags
User Login,Successful login,"1. Open login page|2. Enter username|3. Enter password|4. Click login",@login @smoke
Search,Basic search,"1. Go to homepage|2. Search for 'test'|3. Verify results",@search
```

#### 4. **Existing Gherkin (.feature)**
The script can also clean up and standardize existing Gherkin files.

### 🎯 Conversion Features

The converter automatically:
- **Standardizes step formats** to proper Gherkin syntax
- **Adds proper Given/When/Then keywords** based on step context
- **Generates test IDs** in @TEST_RP-XXX format
- **Adds appropriate tags** for organization
- **Handles both English and Indonesian** step descriptions
- **Creates proper file structure** in test_framework/features/

### 📊 Example Conversion

**Input (text file):**
```text
Feature: Homepage Navigation
Scenario: Access main menu
1. Buka halaman utama
2. Klik menu "Layanan"
3. Pastikan menu terbuka
```

**Output (.feature file):**
```gherkin
@TEST_RP-001 @ui_test
Feature: Homepage Navigation

  Scenario: Access main menu
    Given the user is on the homepage
    When I click "Layanan"
    Then I should see the menu is open
```

### 🚀 Quick Workflow

1. **Create your feature descriptions** in any supported format
2. **Run the converter** to generate proper .feature files
3. **Review and adjust** the generated files if needed
4. **Execute tests** using run_gherkin.sh or run_scaled.sh

```bash
# Complete workflow example
echo "Feature: My Test
Scenario: Test scenario
1. Open homepage
2. Click button
3. Verify result" > my_test.txt

# Convert to proper Gherkin
./scripts/convert_features.sh my_test.txt

# Run the generated test
./run_gherkin.sh @TEST_RP-001
```
