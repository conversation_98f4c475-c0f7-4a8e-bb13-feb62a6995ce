#!/usr/bin/env python3
"""
Xray Integration Module for Web Automation Reg
Handles test result updates to Jira Xray tickets
"""

import os
import json
import requests
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

class XrayIntegration:
    """Handles integration with Jira Xray for test execution updates"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Xray integration
        
        Args:
            config: Configuration dictionary with Xray settings
        """
        self.config = config or self._load_config()
        self.logger = logging.getLogger(__name__)
        
        # Xray API endpoints
        self.base_url = self.config.get('base_url', 'https://wartek.atlassian.net')
        self.api_version = self.config.get('api_version', '1.0')
        self.import_endpoint = f"{self.base_url}/rest/raven/{self.api_version}/import/execution/junit"
        
        # Authentication
        self.auth = self._setup_authentication()
        
        # Session for connection pooling
        self.session = requests.Session()
        if self.auth:
            self.session.auth = self.auth
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        return {
            'base_url': os.getenv('XRAY_BASE_URL', 'https://wartek.atlassian.net'),
            'username': os.getenv('XRAY_USERNAME', ''),
            'password': os.getenv('XRAY_PASSWORD', ''),
            'api_token': os.getenv('XRAY_API_TOKEN', ''),
            'project_key': os.getenv('XRAY_PROJECT_KEY', 'RP'),
            'test_plan_key': os.getenv('XRAY_TEST_PLAN_KEY', ''),
            'test_execution_key': os.getenv('XRAY_TEST_EXECUTION_KEY', ''),
            'enabled': os.getenv('XRAY_ENABLED', 'true').lower() == 'true',
            'environment': os.getenv('XRAY_ENVIRONMENT', 'staging'),
            'timeout': int(os.getenv('XRAY_TIMEOUT', '30')),
            'retry_attempts': int(os.getenv('XRAY_RETRY_ATTEMPTS', '3')),
            'retry_delay': int(os.getenv('XRAY_RETRY_DELAY', '5'))
        }
    
    def _setup_authentication(self) -> Optional[tuple]:
        """Setup authentication for Xray API"""
        username = self.config.get('username')
        password = self.config.get('password')
        api_token = self.config.get('api_token')
        
        if api_token:
            # Use API token authentication
            return (username, api_token)
        elif username and password:
            # Use basic authentication
            return (username, password)
        else:
            self.logger.warning("No Xray authentication credentials provided")
            return None
    
    def is_enabled(self) -> bool:
        """Check if Xray integration is enabled"""
        return self.config.get('enabled', False) and self.auth is not None
    
    def extract_ticket_number(self, tag: str) -> Optional[str]:
        """
        Extract ticket number from Gherkin tag
        
        Args:
            tag: Tag like @TEST_RP-932
            
        Returns:
            Ticket number like RP-932 or None if not found
        """
        if tag.startswith('@TEST_'):
            return tag[6:]  # Remove @TEST_ prefix
        return None
    
    def update_test_execution(self, junit_xml_path: str, environment: str = None) -> bool:
        """
        Update test execution in Xray using JUnit XML
        
        Args:
            junit_xml_path: Path to JUnit XML file
            environment: Test environment (staging/production)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_enabled():
            self.logger.info("Xray integration is disabled")
            return False
        
        if not os.path.exists(junit_xml_path):
            self.logger.error(f"JUnit XML file not found: {junit_xml_path}")
            return False
        
        try:
            with open(junit_xml_path, 'r', encoding='utf-8') as f:
                junit_content = f.read()
            
            return self._send_junit_to_xray(junit_content, environment)
            
        except Exception as e:
            self.logger.error(f"Failed to update test execution: {str(e)}")
            return False
    
    def _send_junit_to_xray(self, junit_content: str, environment: str = None) -> bool:
        """
        Send JUnit XML content to Xray
        
        Args:
            junit_content: JUnit XML content as string
            environment: Test environment
            
        Returns:
            True if successful, False otherwise
        """
        headers = {
            'Content-Type': 'application/xml',
            'Accept': 'application/json'
        }
        
        # Add query parameters for test execution
        params = {}
        if self.config.get('test_execution_key'):
            params['testExecKey'] = self.config['test_execution_key']
        if self.config.get('test_plan_key'):
            params['testPlanKey'] = self.config['test_plan_key']
        if environment:
            params['testEnvironment'] = environment
        
        for attempt in range(self.config.get('retry_attempts', 3)):
            try:
                self.logger.info(f"Sending test results to Xray (attempt {attempt + 1})")
                
                response = self.session.post(
                    self.import_endpoint,
                    data=junit_content.encode('utf-8'),
                    headers=headers,
                    params=params,
                    timeout=self.config.get('timeout', 30)
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.logger.info(f"Successfully updated Xray test execution: {result}")
                    return True
                elif response.status_code == 401:
                    self.logger.error("Xray authentication failed")
                    return False
                else:
                    self.logger.warning(f"Xray API returned status {response.status_code}: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed (attempt {attempt + 1}): {str(e)}")
                
            if attempt < self.config.get('retry_attempts', 3) - 1:
                time.sleep(self.config.get('retry_delay', 5))
        
        self.logger.error("Failed to update Xray after all retry attempts")
        return False
    
    def update_individual_test(self, ticket_number: str, status: str, environment: str = None, 
                             execution_time: float = None, error_message: str = None) -> bool:
        """
        Update individual test result in Xray
        
        Args:
            ticket_number: Jira ticket number (e.g., RP-932)
            status: Test status (PASS, FAIL, EXECUTING, TODO, etc.)
            environment: Test environment
            execution_time: Test execution time in seconds
            error_message: Error message if test failed
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_enabled():
            return False
        
        # This would require additional Xray API endpoints for individual test updates
        # For now, we'll rely on the JUnit XML import method
        self.logger.info(f"Individual test update for {ticket_number}: {status}")
        return True
    
    def get_test_execution_info(self) -> Dict[str, Any]:
        """Get information about the current test execution"""
        return {
            'enabled': self.is_enabled(),
            'base_url': self.config.get('base_url'),
            'project_key': self.config.get('project_key'),
            'test_plan_key': self.config.get('test_plan_key'),
            'test_execution_key': self.config.get('test_execution_key'),
            'environment': self.config.get('environment')
        }
    
    def close(self):
        """Close the session"""
        if hasattr(self, 'session'):
            self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def create_xray_integration(config: Dict[str, Any] = None) -> XrayIntegration:
    """
    Factory function to create XrayIntegration instance
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        XrayIntegration instance
    """
    return XrayIntegration(config)
