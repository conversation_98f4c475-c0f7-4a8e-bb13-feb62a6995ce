import { connectXray } from '@/helpers/global.helper'
import { STAGING_BASE_URL } from '@/utils/env.util'
import { expect, test } from '@playwright/test'

test('@RPNG-1782 @visual @staging @anonymous should match the login page snapshot', async ({ page }, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1782' })
  await page.goto(STAGING_BASE_URL)
  await expect(page.getByText('Dashboard Admin', { exact: false })).toBeVisible()
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})
