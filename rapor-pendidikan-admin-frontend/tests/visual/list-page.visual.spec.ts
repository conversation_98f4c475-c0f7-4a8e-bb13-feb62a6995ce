import { connectXray } from '@/helpers/global.helper'
import { deleteTableRowsButFirst } from '@/helpers/table.helper'
import { STAGING_BASE_URL } from '@/utils/env.util'
import { setViewportAsDesktop } from '@/utils/setup'
import { expect, test } from '@playwright/test'

const URL_LIST_PAGE_PREFIX = `${STAGING_BASE_URL}/pengaturan-akses`

test.beforeEach(async ({ page }) => {
  setViewportAsDesktop(page)
})

test('@RPNG-1784 @visual @staging should match the list satuan pendidikan users page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1784' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-satuan-pendidikan-users"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-list-satuan-pendidikan-users"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for 4 columns in list page satuan pendidikan
  locators.push(page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-column-date"]'))
  locators.push(page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-column-npsn"]'))
  locators.push(page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-column-email"]'))
  locators.push(page.locator('[data-testid="user-list-satuan-pendidikan-table-body-row-0-column-status"]'))

  // add locators for pagination meta data
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[1]/p'))

  // add locators for last page of pagination
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[2]/nav/ul/li[8]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})

test('@RPNG-1785 @visual @staging should match the list dinas kabupaten kota users page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1785' })
  const LIST_PAGE_DINAS_KABKOT_URL = `${URL_LIST_PAGE_PREFIX}/dinas-kabupaten-kota`
  await page.goto(LIST_PAGE_DINAS_KABKOT_URL)
  await page.waitForURL(LIST_PAGE_DINAS_KABKOT_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-list-daerah-users"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for 6 columns in list page dinas kab/kota
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-date"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-area-code"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-province-name"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-city-name"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-email"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-status"]'))

  // add locators for pagination meta data
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[1]/p'))

  // add locators for last page of pagination
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[2]/nav/ul/li[8]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})

test('@RPNG-1786 @visual @staging should match the list dinas provinsi users page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-1786' })
  const LIST_PAGE_DINAS_PROVINSI_URL = `${URL_LIST_PAGE_PREFIX}/dinas-provinsi`
  await page.goto(LIST_PAGE_DINAS_PROVINSI_URL)
  await page.waitForURL(LIST_PAGE_DINAS_PROVINSI_URL)
  // wait until table list user displayed
  await page.waitForSelector('[data-testid="table-list-daerah-users"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-list-daerah-users"]')

  const locators = []
  // add locators for avatar image
  locators.push(page.locator('xpath=//*[@id="headlessui-popover-button-3"]/span/img'))

  // add locators for 5 columns in list page dinas provinsi
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-date"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-area-code"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-province-name"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-email"]'))
  locators.push(page.locator('[data-testid="user-list-daerah-row-0-column-status"]'))

  // add locators for pagination meta data
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[1]/p'))

  // add locators for last page of pagination
  locators.push(page.locator('xpath=//*[@id="__next"]/div[2]/main/div[2]/footer/div/div/div[2]/nav/ul/li[8]'))

  await expect(page).toHaveScreenshot({
    mask: locators,
    maxDiffPixelRatio: 0.2,
  })
})
