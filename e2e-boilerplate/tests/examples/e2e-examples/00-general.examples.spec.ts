import { screenshot, switchUserAccount } from '@/helpers/global.helper'
import { expectLoggedInUser, login } from '@/helpers/test.helper'
import { expect, test } from '@playwright/test'
import { __ } from 'gsheet-kv'

/**
 * Example Test suite for Guru - General Examples
 */
test.describe('General Examples - Guru', () => {
  /**
   * Test case: Should be able to see the homepage
   */
  test('@SHIP-EXAMPLE1 @staging Should be able to see the homepage', async ({ page }, testInfo) => {
    // Go to the homepage
    await page.goto(__.GURU_HOMEPAGE_PATH)

    // Expect the heading to be visible
    await expect(page.getByRole('heading', { name: new RegExp(__.GURU_HOMEPAGE_HEADING, 'i') })).toBeVisible()

    await screenshot(page, testInfo)
  })

  /**
   * Test case: Should switch to GURU_EMPAT and back to GURU_SATU account
   */
  test('@SHIP-EXAMPLE2 @staging Should handle switch users between GURU_EMPAT and GURU_SATU', async ({
    page,
  }, testInfo) => {
    // Switch to user account GURU_EMPAT
    page = await switchUserAccount(page, testInfo, 'GURU_EMPAT')

    // Go to the '/akun' page
    await page.goto(__.GURU_ACCOUNT_PATH, { waitUntil: 'domcontentloaded' })

    // Expect the email to be visible
    await expect(page.getByText(__.GURU_EMPAT_EMAIL)).toBeVisible()

    await expectLoggedInUser(page, 'GURU_EMPAT')
    await screenshot(page, testInfo)

    // Switch to user account GURU_SATU
    page = await switchUserAccount(page, testInfo, 'GURU_SATU')

    // Go to the '/akun' page
    await page.goto(__.GURU_ACCOUNT_PATH)

    // Expect the email to be visible
    await expect(page.getByText(__.GURU_SATU_EMAIL)).toBeVisible()

    // Expect logged-in user to be GURU_SATU
    await expectLoggedInUser(page, 'GURU_SATU')

    await screenshot(page, testInfo)
  })

  /**
   * Test case: Should go to 404 when accessing a protected page and able to login after that
   */
  test('@SHIP-EXAMPLE3 @anonym @production @staging Should go to 404 when accessing protected page and able to login after that', async ({
    page,
  }, testInfo) => {
    // Go to the '/akun' page
    await page.goto(__.GURU_ACCOUNT_PATH)

    // Expect to be redirected to the login page
    await page.waitForURL('**/login**')

    // Login with user account GURU_EMPAT
    page = await login(page, 'GURU_EMPAT')

    // Expect logged-in user to be GURU_EMPAT
    expectLoggedInUser(page, 'GURU_EMPAT')

    // Go to the '/akun' page
    await page.goto(__.GURU_ACCOUNT_PATH)

    // Expect the email to be visible
    await expect(page.getByText(__.GURU_EMPAT_EMAIL)).toBeVisible()

    await screenshot(page, testInfo)
  })
})
