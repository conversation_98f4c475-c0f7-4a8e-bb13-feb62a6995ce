# Base URL Example:
# PRODUCTION_BASE_URL = 'https://guru.kemdikbud.go.id'
# STAGING_BASE_URL = 'https://guru.staging.belajar.id'

PRODUCTION_BASE_URL=your_production_base_url
STAGING_BASE_URL=your_staging_base_url

## Use this pattern if you have multiple users/role to test
## Update constants config in src/auth/users.ts
USER_EMAIL_IAP="email_iap"
USER_PASSWORD_IAP="password_iap"
USER_EMAIL_GURU_SATU="user_email"
USER_PASSWORD_GURU_SATU="user_password"
USER_EMAIL_GURU_EMPAT="user_email"
USER_PASSWORD_GURU_EMPAT="user_password"

# to run examples
RUN_EXAMPLES=true
AUTH_CACHE_EXPIRY_TIME_DAYS=7

# to print out auth debug logs
AUTH_DEBUG=true

# set it false if you want to disable authentication flow in the e2e tests (default: true)
AUTH_SETUP_ENABLED=true

# set it false if your app doesn't have IAP on staging (default: true)
IAP_ENABLED=true

# Use it only when you need to use google sheet for dynamic constants
# https://docs.google.com/spreadsheets/d/1J3jleGcFIfi0sNxtcR-pl3XHSA93_DLM_U8Y-QhYZ3M/edit
# Set the sheet name (repository name) here
USE_DYNAMIC_CONSTANTS_GSHEET=""

# Use it only when you need to use `IAP_ENABLED=true` but your staging login URL is not IAP-protected. Set it to any URL path that is IAP-protected
# IAP_LOGIN_URL=
