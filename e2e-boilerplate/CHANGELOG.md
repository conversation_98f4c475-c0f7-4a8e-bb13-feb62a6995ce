# Changelog

## Version 1.9.0 (2024-09-04)

### Authentication and Setup Improvements

- Refactor: Update `auth-cache.helper.ts` to support user-specific authentication

  - Modify `generateConfigAuth` and `checkConfig` functions to include `userKey`
  - Enhance token validity and user configuration checks
  - Improve handling for IAP-enabled scenarios

- Update: Revise setup processes for IAP, production, and staging environments

  - Implement user-specific authentication loops
  - Add support for parallel authentication when possible
  - Improve logging and error handling

- Enhance: Modularize and improve reusability of authentication processes
- Performance: Optimize authentication speed with parallel processing

  - Implement more efficient handling of cached credentials

## Version 1.8.1 (2024-09-04)

- Fix: Filter test cases that are defined in MR description
- Feat: Add new script to update constants from Google Sheet on CI

## Version 1.8.0 (2024-08-16)

- Feat: Introduced a new feature to dynamically fetch constants from Google Sheets.
  - Usage Instructions:
    - To enable this feature, set the environment variable `USE_DYNAMIC_CONSTANTS=true`.
    - Access the Google Sheet containing the constants at the following link: [Google Sheets](https://docs.google.com/spreadsheets/d/1J3jleGcFIfi0sNxtcR-pl3XHSA93_DLM_U8Y-QhYZ3M/edit).
    - Simply run your tests as usual; the system will automatically retrieve the constants from the sheet and populate the `gsheet-kv.ts` file.
    - The Google Sheet is fully editable, allowing you to add, remove, or modify constants as needed.
    - For real-time updates, execute `npm run dev`. This command will monitor the Google Sheet for any changes and update the `gsheet-kv.ts` file accordingly.
- Chore: Add `vitest` to the project to run the test scripts.
- Chore: Update `eslint` config and allow no-networkidle, no-wait-for-timeout, no-wait-for-selector to make the test more flexible.

## Version 1.7.0 (2024-07-31)

- Chore: Attempted to upgrade Playwright to `1.45.3` (latest), but please note that we can only use a maximum of the current version `1.41.1` because after **Chromium 122**, the stealth plugin no longer works.
- Use Eslint v9
- Upgrade dependencies
- Fix all test examples
- Set the retry count on CI to `0`, as it often makes the test pipeline longer and slower without providing any benefits. However, it can be adjusted via the environment variable `CI_FAILURE_RETRIES` if certain projects require retries.
- Disable trace mode and video mode on CI, as most maintainers prefer to debug using traces and videos on their local machines.

## Version 1.6.2 (2024-07-30)

### Changes

- Fix: Use `@playwright/test` version `1.41.1`
- Update visual test snapshots

## Version 1.6.1 (2024-07-30)

### Changes

- Fix: Add `page.goto(LOGIN_URL)` on `IAPLogin` method to ensure that the page is redirected to the login URL before the IAP login is executed.

## Version 1.6.0 (2024-06-05)

### Changes

- **Feature:** Added a new configurable environment variable, `IAP_LOGIN_URL`. This allows you to change the IAP login URL to a path that is IAP protected (default is the staging login URL). This is useful when you have a test page that is IAP protected, and you need to enable `IAP_ENABLED`, but your staging login URL is not IAP protected. With this, `IAP_LOGIN_URL` will be used solely for IAP authentication, and then the resulting token will be used to authenticate on the IAP-protected page, allowing it to be bypassed.
  - Issue discussion: [Slack thread](https://merdeka-belajar.slack.com/archives/C05E54X133M/p1717573710109689?thread_ts=1717504351.356609&cid=C05E54X133M)

## Version 1.5.1 (2024-05-06)

### Changes

- Feat: Add new step on the update snapshot visual test pipeline to notify on slack when merge request is created.
- Fix: GitLab configuration rules to filter job runs using the `STAGING_ONLY` and `PRODUCTION_ONLY` environment variables.

## Version 1.5.0 (2024-04-29)

### Changes

- Feat: Add a feature to automate updating snapshot visual tests using a CI pipeline by adding the environment variable `RUN_UPDATE_SNAPSHOT_VISUAL_TEST=true` to run a pipeline that updates the snapshot visual tests and creates a merge request with the updated snapshot visual tests.
- Chore: set retry on CI to be 1 time only.

## Version 1.4.1 (2024-04-04)

### Changes

- Chore: add new function in `global.helpers`: `getTestEnv` and `execFunctionOnEnv`

## Version 1.4.0 (2024-04-03)

### Changes

- Feat: Able to run specific test cases that are defined in merge request description, so that we can run only the test cases that are related to the changes in the merge request.

## Version 1.3.3 (2024-04-03)

### Changes

- Fix: Prevent send tracking test case when @setup test is failed.

## Version 1.3.2 (2024-04-02)

### Changes

- Fix: regex on playwright test list command

## Version 1.3.1 (2024-04-02)

### Changes

- Fix: Add new env variable `USE_GREP_FILTER` to allow unfiltered test cases to run in CI so that we can list all test cases that written in the test suite.
- Fix: Modify the script to filter and transform `playwright test --list` so that the JIRA keys are not duplicated and do not include "@" symbols.

## Version 1.3.0 (2024-03-27)

### Changes

- Feat: Add a script to automatically update the Jira ticket labels and components by adding the "automated" label and "automated" component when the test case is executed.
- Fix: Longer timeout on login and default timeout on test cases in CI.
- Fix: Package.json script to reset auth cache.

## Version 1.2.1 (2024-02-26)

### Changes

- Chore: adjust default config of video + trace mode to `retain-on-failure` on CI.

## Version 1.2.0 (2024-01-22)

### Changes

- Chore: bump dependencies to latest minor & patch changes
- Fix: typing on constant default device

## Version 1.1.7 (2024-01-15)

- Fixed consent screen handling in the `googleLogin` function. Now it detects if the consent screen has more than one flow and handles it accordingly.

## Version 1.1.6 (2024-01-03)

### Changes

- Chore: increase timeout on login setup from hardcoded 3 minutes to 40 seconds per user.

## Version 1.1.5 (2023-12-14)

### Changes

- Fix: Replace the usage of cache on CI with artifacts for more reliable CI job especially when running triggered by other repository.

## Version 1.1.4 (2023-12-04)

### Changes

- Fix: change CI_PIPELINE_SOURCE condition from 'trigger' to 'pipeline' because it is multiproject downstream pipeline. Source: <https://docs.gitlab.com/ee/ci/pipelines/downstream_pipelines.html?tab=Multi-project+pipeline#use-rules-to-control-downstream-pipeline-jobs>.

## Version 1.1.3 (2023-11-30)

### Changes

- Fix: success and failed rate calculation.
- Fix: skip tracking event when triggering pipeline from other repository (via `trigger` source pipeline).
- Chore: don't need to run `prepare` job when cache is available.

## Version 1.1.2 (2023-11-27)

### Changes

- Fix: skip tracking event when running specific test cases.

## Version 1.1.1 (2023-11-24)

### Changes

- Fix: use `CI_PIPELINE_ID` instead of nanoid to accumulate the result of staging and production test cases.
- Fix: failed test cases because of `extraHTTPHeaders`.

## Version 1.1.0 (2023-11-23)

### New Features

- **E2E Test Case Tracker as Custom Reporter**
  - **Integration**: Script integrated into CI, exclusively for the main branch.
  - **Metrics Tracked**:
    - Success Rate: Percentage of passed tests.
    - Failed Rate: Percentage of failed tests.
    - Test Case Count: Number of test cases, categorized by tags and the overall total.
  - **Dashboard Access**: Detailed analytics available on [Looker Studio Dashboard](https://lookerstudio.google.com/u/0/reporting/d8580779-ff11-4ad3-9987-8a0d440d13b5/page/p_4cf1oorybd).
  - Note: When the custom reporter is already stable, there will be patch changes to move it to the FE-toolbox repository. This is done to minimize maintenance efforts and reduce the possibility of unintended changes.

## Version 1.0.9 (2023-10-19)

- Fix: IAP login waits for URL changes now it only waits for network idle (if already on the login page) otherwise it will wait for the URL to change.

## Version 1.0.8 (2023-10-11)

- Chore: Bump dependencies
  - Playwright from 1.37 to 1.39 (check this changedoc for more info: <https://playwright.dev/docs/release-notes#version-139>)
  - Fix Zod vulnerability
- Fix: IAP login waits for URL changes now it only waits for network idle
- Fix: Google login expects the first text to exist instead of looking for many text

## Version 1.0.7 (2023-09-14)

- Fix: Avoid running IAP authentication setup and refrain from using IAP storage state in production job testing.

## Version 1.0.6 (2023-09-13)

- Fix: IAP login issue to wait until the page is redirected to the staging page.

## Version 1.0.5 (2023-09-11)

- Fix: Execute `[env].setup` when `IAP_ENABLED` is set to `false` to ensure the correct usage of IAP.
- Fix: Display Report URL on CI based on the GitLab project name
- Chore: Optionally use Bun and Upgrade playwright version to 1.37.1
- Chore: Convert `test/playwright-config.util` from JavaScript to TypeScript.

## Version 1.0.4 (2023-09-07)

- Fix: command `test:ui` can't run properly on some windows machine.

## Version 1.0.3 (2023-09-06)

- Refactor: Improved maintainability of the Google login function.
- Fix: Utilized `STAGING_ONLY` and `PRODUCTION_ONLY` for staging and production GitLab CI jobs.
- Fix: Resolved intermittent IAP issue in the staging setup.
- Fix: Resolved issue with `storageState` when `IAP_ENABLED` is set to `false`.

## Version 1.0.2 (2023-09-05)

- Fixed: Add fallback check when logging in with Google and waiting for the popup to show up.

## Version 1.0.1 (2023-09-04)

- Fixed: Developer experience issue on auth cache check and no auth flow (bug in checking value on env file)
- Fixed: Env check on mode staging/production only
- Fixed: Run IAP only on `IAP_ENABLED` or not `PRODUCTION_ONLY`
- Chore: Split IAP user as separated user testing.
  - Due to changes in src/auth/users.ts adding `IAP` user, now every project recommended to have env for `USER_EMAIL_IAP` and `USER_PASSWORD_IAP` for IAP user.
