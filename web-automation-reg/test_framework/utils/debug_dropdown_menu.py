#!/usr/bin/env python3
"""
Debug script to see what's in the dropdown menu after clicking user initials
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_dropdown_menu():
    """Debug what appears in the dropdown after clicking user initials"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # Navigate to homepage (assuming user will be logged in)
            print("🌐 Navigating to homepage...")
            await page.goto("https://rumah-baru.staging.belajar.id/")
            await page.wait_for_load_state('networkidle')
            
            # Look for user initials (we know "TE" should be there)
            print("🔍 Looking for user initials...")
            viewport = page.viewport_size
            
            # Find and click user initials
            js_click_initials = """
            () => {
                const elements = document.querySelectorAll('*');
                const viewport = {width: window.innerWidth, height: window.innerHeight};
                
                for (let el of elements) {
                    const text = el.textContent?.trim();
                    if (text === 'TE') {
                        const rect = el.getBoundingClientRect();
                        if (rect.x > viewport.width * 0.5 && rect.y < viewport.height * 0.3) {
                            console.log('Clicking TE initials at:', rect);
                            el.click();
                            return {success: true, text: text, rect: rect};
                        }
                    }
                }
                return {success: false};
            }
            """
            
            result = await page.evaluate(js_click_initials)
            if result['success']:
                print(f"✅ Successfully clicked user initials: {result['text']}")
                
                # Wait for dropdown to appear
                await page.wait_for_timeout(3000)
                
                # Take screenshot after clicking
                import os
                screenshot_path = os.path.join(os.path.dirname(__file__), "debug_after_initials_click.png")
                await page.screenshot(path=screenshot_path)
                print(f"📸 Screenshot saved: {screenshot_path}")
                
                # Now analyze what's visible on the page
                print("🔍 Analyzing page content after clicking initials...")
                
                # Get all visible text elements
                js_analyze = """
                () => {
                    const elements = document.querySelectorAll('*');
                    const visibleElements = [];
                    
                    for (let el of elements) {
                        const text = el.textContent?.trim();
                        if (text && text.length > 0 && text.length < 50) {
                            const rect = el.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0) {
                                const style = getComputedStyle(el);
                                if (style.display !== 'none' && style.visibility !== 'hidden') {
                                    visibleElements.push({
                                        text: text,
                                        tagName: el.tagName,
                                        className: el.className,
                                        rect: {x: rect.x, y: rect.y, width: rect.width, height: rect.height},
                                        isClickable: el.tagName === 'BUTTON' || el.tagName === 'A' || 
                                                   el.onclick !== null || el.getAttribute('role') === 'button' ||
                                                   getComputedStyle(el).cursor === 'pointer'
                                    });
                                }
                            }
                        }
                    }
                    
                    return visibleElements;
                }
                """
                
                visible_elements = await page.evaluate(js_analyze)
                
                print(f"📊 Found {len(visible_elements)} visible text elements:")
                
                # Filter for potential logout elements
                logout_candidates = []
                for elem in visible_elements:
                    text_lower = elem['text'].lower()
                    if any(word in text_lower for word in ['logout', 'log out', 'keluar', 'sign out', 'exit']):
                        logout_candidates.append(elem)
                        print(f"🎯 LOGOUT CANDIDATE: '{elem['text']}' - {elem['tagName']} - clickable: {elem['isClickable']}")
                        print(f"    Position: {elem['rect']}")
                        print(f"    Class: {elem['className']}")
                
                if len(logout_candidates) == 0:
                    print("❌ No logout candidates found!")
                    print("🔍 Showing all clickable elements instead:")
                    
                    clickable_elements = [elem for elem in visible_elements if elem['isClickable']]
                    for i, elem in enumerate(clickable_elements[:20]):  # Show first 20
                        print(f"  {i+1}. '{elem['text']}' - {elem['tagName']} at {elem['rect']}")
                
                # Also check for dropdown/menu containers
                print("\n🔍 Looking for dropdown/menu containers...")
                js_containers = """
                () => {
                    const containers = document.querySelectorAll('.dropdown, .menu, .dropdown-menu, [role="menu"], [role="menuitem"]');
                    const result = [];
                    
                    for (let container of containers) {
                        const rect = container.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            const style = getComputedStyle(container);
                            if (style.display !== 'none' && style.visibility !== 'hidden') {
                                result.push({
                                    tagName: container.tagName,
                                    className: container.className,
                                    innerHTML: container.innerHTML.substring(0, 200),
                                    rect: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                                });
                            }
                        }
                    }
                    
                    return result;
                }
                """
                
                containers = await page.evaluate(js_containers)
                print(f"📊 Found {len(containers)} dropdown/menu containers:")
                for i, container in enumerate(containers):
                    print(f"  {i+1}. {container['tagName']} - {container['className']}")
                    print(f"     Content: {container['innerHTML'][:100]}...")
                    print(f"     Position: {container['rect']}")
                
            else:
                print("❌ Could not find or click user initials")
            
            # Keep browser open for manual inspection
            print("\n🔍 Keeping browser open for 10 seconds for manual inspection...")
            await asyncio.sleep(10)
            
        except Exception as e:
            print(f"❌ Error during debugging: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    print("🧪 Starting dropdown menu debugging...")
    print("=" * 50)
    
    asyncio.run(debug_dropdown_menu())
    
    print("=" * 50)
    print("🏁 Dropdown debugging completed!")
