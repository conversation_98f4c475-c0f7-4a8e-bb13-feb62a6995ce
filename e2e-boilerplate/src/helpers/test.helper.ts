import { Page, expect } from '@playwright/test'
import { chromium } from 'playwright-extra'
import stealth from 'puppeteer-extra-plugin-stealth'

import { USERS, type UserKey } from '@/constants/users.constant'

import { googleLogin } from '@/auth/google-login.helper'
import { isProduction } from './global.helper'

chromium.use(stealth())

export const goToBuktiKaryaPage = (page: Page) => page.goto(`/bukti-karya`)
export const goToPerangkatAjarPage = async (page: Page) => {
  await page.goto(`/home`)

  await page.getByTestId('home-menu-perangkat-ajar').click()
}

/**
 * Asserts that the given page has a logged-in user matching the specified user key.
 *
 * @param {Page} page - The page to be checked.
 * @param {UserKey} userKey - The key identifying the user.
 *
 * @example
 * const page = // Obtain the page
 * const userKey = 'GURU_SATU'
 * await expectLoggedInUser(page, userKey)
 *
 */
export const expectLoggedInUser = async (page: Page, userKey: UserKey) => {
  const localStorage = await page.context().storageState()

  const currentPageOrigin = page.url().split('/').slice(0, 3).join('/')

  const appLogin = JSON.parse(
    localStorage.origins
      .find((domain) => domain.origin === currentPageOrigin)
      ?.localStorage.find((item) => item.name === 'app-login')?.value ?? '{}'
  ) as { user: { email: string } }

  expect(appLogin?.user?.email).toBe(USERS[userKey].email)
}

/**
 * Asserts that the given page is currently in the login page.
 *
 * @param {Page} page - The page to be checked.
 *
 * @example
 * await expectInLoginPage(page)
 * // Continue with further assertions or operations if the page is indeed the login page
 */
export const expectInLoginPage = async (page: Page) => {
  expect(page.url()).toEqual(expect.stringContaining('/login'))
}

/**
 * Logs in to a page using the specified user credentials.
 *
 * @param {Page} page - The page to be closed before logging in.
 * @param {UserKey} userKey - The key identifying the user.
 * @returns {Promise<Page>} The new page after logging in.
 *
 * @example
 * page = await login(page, 'GURU_EMPAT')
 * // Use the loggedPage for further testing or operations
 */
export const login = async (page: Page, userKey: UserKey): Promise<Page> => {
  await page.close()

  const browser = await chromium.launch({ headless: false })
  const newPage = await browser.newPage()

  if (isProduction(page)) {
    await googleLogin(newPage, USERS[userKey], 'production')
  } else {
    await googleLogin(newPage, USERS[userKey], 'staging')
  }

  return newPage
}
