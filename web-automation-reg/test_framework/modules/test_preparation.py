import asyncio
import re
import json
import os
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Optional, Tuple
from .locator_repository import LocatorRepository
import urllib.parse

class SimpleGherkinInterpreter:
    """Simple pattern matching interpreter for Gherkin steps without LLM."""
    
    def interpret_step(self, step: str) -> Optional[Dict[str, str]]:
        """Interpret a Gherkin step using pattern matching."""
        step_lower = step.lower()
        
        # Navigation patterns
        if "i am on" in step_lower or "i visit" in step_lower:
            return {
                'action_type': 'navigate',
                'target_description': 'main page',
                'value': ''
            }
        
        # Click patterns
        if "i click" in step_lower:
            # Extract the target from quotes
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'click',
                    'target_description': target,
                    'value': ''
                }
        
        # Type patterns
        if "i type" in step_lower:
            # Extract value and field
            value_match = re.search(r'"([^"]+)"', step)
            if value_match:
                value = value_match.group(1)
                # Determine field type
                if "search" in step_lower:
                    target = "search field"
                else:
                    target = "input field"
                return {
                    'action_type': 'type',
                    'target_description': target,
                    'value': value
                }
        
        # Assertion patterns
        if "i should see" in step_lower:
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'assert',
                    'target_description': target,
                    'value': ''
                }
        
        # Scroll patterns
        if "i scroll" in step_lower:
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'scroll',
                    'target_description': target,
                    'value': ''
                }
        
        return None

class TestPreparation:
    """Prepares test execution by analyzing steps and collecting locators in advance."""
    
    def __init__(self, steps: List[str], interpreter=None, dom_analyzer=None):
        """Initialize the test preparation module.
        
        Args:
            steps: List of test steps in natural language
            interpreter: LLMInterpreter instance for parsing steps
            dom_analyzer: DOMAnalyzer instance for finding locators
        """
        self.steps = steps
        self.interpreter = interpreter or SimpleGherkinInterpreter()
        self.dom_analyzer = dom_analyzer
        self.base_url = None
        self.domain = None
        self.collected_locators = {}
        self.step_locator_map = {}
        self.locator_repo = LocatorRepository()
    
    def extract_base_url(self, steps: List[str]) -> Optional[str]:
        """Extract base URL from the test steps."""
        for step in steps:
            step_lower = step.lower()
            if "buka halaman" in step_lower or "go to" in step_lower or "navigate to" in step_lower:
                # Extract URL using regex
                url_match = re.search(r'https?://[^\s"\']+', step)
                if url_match:
                    return url_match.group(0)
        return None
    
    def extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        parsed_url = urllib.parse.urlparse(url)
        return parsed_url.netloc
    
    async def prepare(self) -> bool:
        """Prepare the test by analyzing steps and collecting locators.
        
        Returns:
            bool: True if all locators were found, False otherwise
        """
        print("Starting test preparation...")
        
        # Step 1: Read and analyze test steps
        print("Step 1: Analyzing test steps...")
        self.base_url = self.extract_base_url(self.steps)
        if not self.base_url:
            print("Error: Could not find base URL in test steps")
            return False
        
        self.domain = self.extract_domain(self.base_url)
        print(f"Base URL identified: {self.base_url} (Domain: {self.domain})")
        
        # Step 2: Check if we already have locators for this domain
        print("Step 2: Checking for existing locators...")
        existing_locators = self.locator_repo.load_locators(self.domain)
        if existing_locators and len(existing_locators) > 1:  # More than just _last_updated
            print(f"Found {len(existing_locators) - 1} existing locators for domain {self.domain}")
            self.collected_locators = existing_locators
            
            # Try to match with existing locators first
            all_locators_found = await self.match_locators_with_steps()
            if all_locators_found:
                print("All locators matched using existing repository!")
                return True
            else:
                print("Not all locators could be matched with existing repository. Will collect new locators.")
        
        # Step 3: Open base URL and collect locators
        print("Step 3: Opening base URL to collect locators...")
        locators_found = await self.collect_locators_from_url(self.base_url)
        if not locators_found:
            print("Error: Failed to collect locators from base URL")
            return False
        
        # Save collected locators to repository
        self.locator_repo.save_locators(self.domain, self.collected_locators)
        
        # Step 4: Match locators with test steps
        print("Step 4: Matching locators with test steps...")
        all_locators_found = await self.match_locators_with_steps()
        
        # Step 5: If not all locators found, try to find additional locators
        max_attempts = 3
        attempt = 0
        
        while not all_locators_found and attempt < max_attempts:
            attempt += 1
            print(f"Step 5: Not all locators found. Attempt {attempt}/{max_attempts} to find additional locators...")
            additional_locators_found = await self.find_additional_locators()
            
            if additional_locators_found:
                # Save newly collected locators to repository
                self.locator_repo.save_locators(self.domain, self.collected_locators)
                
                # Try matching again
                all_locators_found = await self.match_locators_with_steps()
                if all_locators_found:
                    break
        
        if all_locators_found:
            print("All locators successfully matched with test steps!")
        else:
            print("Warning: Not all locators could be matched with test steps")
            
        return all_locators_found
    
    async def collect_locators_from_url(self, url: str) -> bool:
        """Collect all locators from a given URL.
        
        Args:
            url: The URL to collect locators from
            
        Returns:
            bool: True if locators were successfully collected
        """
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                # Navigate to the URL
                await page.goto(url, wait_until="networkidle")
                
                # Get page content
                html_content = await page.content()
                
                # Extract all interactive elements
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Collect buttons
                buttons = soup.find_all(['button', 'input'])
                for button in buttons:
                    if button.name == 'input' and button.get('type') not in ['submit', 'button']:
                        continue
                    
                    button_id = button.get('id', '')
                    button_text = button.text.strip() if button.text else ''
                    button_name = button.get('name', '')
                    button_value = button.get('value', '')
                    button_class = ' '.join(button.get('class', []))
                    button_type = button.get('type', '')
                    
                    # Create a unique identifier for this button
                    identifier = f"button_{button_id or button_name or button_text or button_value}"
                    
                    # Store locator information
                    self.collected_locators[identifier] = {
                        'type': 'button',
                        'id': button_id,
                        'text': button_text,
                        'name': button_name,
                        'value': button_value,
                        'class': button_class,
                        'element_type': button_type,
                        'selector': self._generate_selector(button)
                    }
                
                # Collect links
                links = soup.find_all('a')
                for link in links:
                    link_id = link.get('id', '')
                    link_text = link.text.strip() if link.text else ''
                    link_href = link.get('href', '')
                    link_class = ' '.join(link.get('class', []))
                    
                    # Create a unique identifier for this link
                    identifier = f"link_{link_id or link_text}"
                    
                    # Store locator information
                    self.collected_locators[identifier] = {
                        'type': 'link',
                        'id': link_id,
                        'text': link_text,
                        'href': link_href,
                        'class': link_class,
                        'selector': self._generate_selector(link)
                    }
                
                # Collect input fields
                inputs = soup.find_all(['input', 'textarea', 'select'])
                for input_field in inputs:
                    if input_field.name == 'input' and input_field.get('type') in ['submit', 'button']:
                        continue
                    
                    input_id = input_field.get('id', '')
                    input_name = input_field.get('name', '')
                    input_placeholder = input_field.get('placeholder', '')
                    input_type = input_field.get('type', '')
                    input_class = ' '.join(input_field.get('class', []))
                    
                    # Create a unique identifier for this input
                    identifier = f"input_{input_id or input_name or input_placeholder}"
                    
                    # Store locator information
                    self.collected_locators[identifier] = {
                        'type': 'input',
                        'id': input_id,
                        'name': input_name,
                        'placeholder': input_placeholder,
                        'input_type': input_type,
                        'class': input_class,
                        'selector': self._generate_selector(input_field)
                    }
                
                # Collect other interactive elements
                other_elements = soup.find_all(['div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                for element in other_elements:
                    element_id = element.get('id', '')
                    element_text = element.text.strip() if element.text else ''
                    element_class = ' '.join(element.get('class', []))
                    
                    # Only store elements with text or id/class
                    if element_text or element_id or element_class:
                        # Create a unique identifier for this element
                        identifier = f"element_{element_id or element_text[:20]}"
                        
                        # Store locator information
                        self.collected_locators[identifier] = {
                            'type': 'element',
                            'id': element_id,
                            'text': element_text,
                            'class': element_class,
                            'selector': self._generate_selector(element)
                        }
                
                print(f"Collected {len(self.collected_locators)} locators from {url}")
                await browser.close()
                return True
                
        except Exception as e:
            print(f"Error collecting locators from {url}: {e}")
            return False
    
    def _generate_selector(self, element) -> str:
        """Generate a CSS selector for a BeautifulSoup element."""
        # Try ID first
        if element.get('id'):
            return f"#{element['id']}"
        
        # Try combining tag and class
        if element.get('class'):
            return f"{element.name}.{'.'.join(element['class'])}"
        
        # Try name attribute
        if element.get('name'):
            return f"{element.name}[name='{element['name']}']"
        
        # Try text content
        if element.text.strip():
            return f"{element.name}:has-text('{element.text.strip()}')"
        
        # Fallback to a more complex selector
        return f"{element.name}"
    
    async def match_locators_with_steps(self) -> bool:
        """Match collected locators with test steps.
        
        Returns:
            bool: True if all steps have matching locators
        """
        all_locators_found = True
        
        for i, step in enumerate(self.steps):
            # Skip the first step if it's just navigation
            if i == 0 and ("buka halaman" in step.lower() or "go to" in step.lower()):
                self.step_locator_map[i] = {
                    'action': 'navigate',
                    'url': self.base_url,
                    'locator': None
                }
                continue
            
            # Interpret the step
            if self.interpreter:
                action_plan = self.interpreter.interpret_step(step)
                if not action_plan:
                    print(f"Warning: Could not interpret step {i+1}: {step}")
                    all_locators_found = False
                    continue
                
                action_type = action_plan.get('action_type', '')
                target = action_plan.get('target_description', '')
                value = action_plan.get('value', '')
                
                # Find matching locator for this step
                matching_locator = self._find_matching_locator(action_type, target)
                
                if matching_locator:
                    self.step_locator_map[i] = {
                        'action': action_type,
                        'target': target,
                        'value': value,
                        'locator': matching_locator
                    }
                else:
                    print(f"Warning: No matching locator found for step {i+1}: {step}")
                    all_locators_found = False
            else:
                print(f"Warning: No interpreter available to parse step {i+1}: {step}")
                all_locators_found = False
        
        return all_locators_found
    
    def _find_matching_locator(self, action_type: str, target: str) -> Optional[Dict[str, Any]]:
        """Find a matching locator for a given action and target.
        
        Args:
            action_type: Type of action (click, type, etc.)
            target: Description of the target element
            
        Returns:
            Optional[Dict[str, Any]]: Matching locator information or None if not found
        """
        if not target or action_type in ['navigate', 'wait', 'assert']:
            return None
        
        target_lower = target.lower()
        
        # For click actions, look for buttons and links
        if action_type == 'click':
            # First try exact matches
            for identifier, locator in self.collected_locators.items():
                if locator['type'] in ['button', 'link']:
                    if (locator['text'].lower() == target_lower or 
                        locator['id'].lower() == target_lower or 
                        target_lower in locator['text'].lower()):
                        return locator
            
            # Then try partial matches
            for identifier, locator in self.collected_locators.items():
                if locator['type'] in ['button', 'link']:
                    if (target_lower in locator['text'].lower() or 
                        target_lower in locator['id'].lower() or
                        (locator['type'] == 'button' and target_lower in locator['value'].lower())):
                        return locator
        
        # For type actions, look for input fields
        elif action_type == 'type':
            # First try exact matches
            for identifier, locator in self.collected_locators.items():
                if locator['type'] == 'input':
                    if (locator['name'].lower() == target_lower or 
                        locator['id'].lower() == target_lower or 
                        locator['placeholder'].lower() == target_lower):
                        return locator
            
            # Then try partial matches
            for identifier, locator in self.collected_locators.items():
                if locator['type'] == 'input':
                    if (target_lower in locator['name'].lower() or 
                        target_lower in locator['id'].lower() or
                        target_lower in locator['placeholder'].lower()):
                        return locator
        
        return None
    
    async def find_additional_locators(self) -> bool:
        """Find additional locators by exploring the page further.
        
        Returns:
            bool: True if additional locators were found
        """
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                # Navigate to the base URL
                await page.goto(self.base_url, wait_until="networkidle")
                
                # Click on navigation elements to explore more pages
                nav_elements = await page.query_selector_all('nav a, .nav a, .menu a, .navigation a')
                
                initial_locator_count = len(self.collected_locators)
                
                for nav_element in nav_elements[:5]:  # Limit to first 5 to avoid too much exploration
                    try:
                        # Get the URL before clicking
                        current_url = page.url
                        
                        # Click the navigation element
                        await nav_element.click()
                        await page.wait_for_load_state("networkidle")
                        
                        # If we navigated to a new page, collect locators
                        if page.url != current_url:
                            html_content = await page.content()
                            soup = BeautifulSoup(html_content, 'html.parser')
                            
                            # Collect buttons, links, inputs, etc. (similar to collect_locators_from_url)
                            # ... (code omitted for brevity, would be similar to the collection code above)
                            
                            # Go back to the previous page
                            await page.go_back()
                            await page.wait_for_load_state("networkidle")
                    except Exception as e:
                        print(f"Error exploring navigation element: {e}")
                
                await browser.close()
                
                # Return True if we found additional locators
                return len(self.collected_locators) > initial_locator_count
                
        except Exception as e:
            print(f"Error finding additional locators: {e}")
            return False
    
    def speak(self, text: str) -> None:
        """Voice narration disabled - only print to console."""
        print(f"Voice narration (disabled): {text}")
    
    def get_locator_for_step(self, step_index: int) -> Optional[Dict[str, Any]]:
        """Get the locator for a specific step.
        
        Args:
            step_index: Index of the step
            
        Returns:
            Optional[Dict[str, Any]]: Locator information or None if not found
        """
        return self.step_locator_map.get(step_index, {}).get('locator')
    
    def get_action_for_step(self, step_index: int) -> Tuple[str, str, str]:
        """Get the action, target and value for a specific step.
        
        Args:
            step_index: Index of the step
            
        Returns:
            Tuple[str, str, str]: Action type, target and value
        """
        step_info = self.step_locator_map.get(step_index, {})
        return (
            step_info.get('action', ''),
            step_info.get('target', ''),
            step_info.get('value', '')
        )
