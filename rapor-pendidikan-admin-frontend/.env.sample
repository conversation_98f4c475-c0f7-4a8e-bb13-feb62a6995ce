PRODUCTION_BASE_URL=https://admin.raporpendidikan.kemdikbud.go.id
STAGING_BASE_URL=https://admin.rapor-pendidikan.staging.belajar.id

## Use this pattern if you have multiple users/role to test
## Update constants config in src/auth/users.ts
# User IAP
USER_EMAIL_IAP="<EMAIL>"
USER_PASSWORD_IAP="password_iap"
# User admin
USER_EMAIL_ADMIN_USER=<EMAIL>
USER_PASSWORD_ADMIN_USER=your_password

# to run examples
RUN_EXAMPLES=false
AUTH_CACHE_EXPIRY_TIME_DAYS=7

# to print out auth debug logs
AUTH_DEBUG=true

# set it false if you want to disable authentication flow in the e2e tests (default: true)
AUTH_SETUP_ENABLED=true

# set it false if your app doesn't have IAP on staging (default: true)
IAP_ENABLED=true

# set to true to run e2e tests only on staging (default: false)
STAGING_ONLY=true
