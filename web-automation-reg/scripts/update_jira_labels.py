#!/usr/bin/env python3
"""
Update Jira Labels Script for Web Automation Reg
Extracts Jira keys from feature files and updates tickets with 'automated' label
"""

import os
import re
import sys
import logging
import requests
from typing import List, Set, Dict, Any
from pathlib import Path
import yaml

# Add the test_framework directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'test_framework'))

from modules.xray_integration import XrayIntegration

class JiraLabelUpdater:
    """Updates Jira ticket labels for automated test cases"""
    
    def __init__(self, config_path: str = None):
        """
        Initialize Jira label updater
        
        Args:
            config_path: Path to configuration file
        """
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.xray_integration = XrayIntegration(self.config.get('xray', {}))
        
        # Jira API configuration
        self.jira_base_url = self.config.get('jira', {}).get('base_url', 'https://wartek.atlassian.net')
        self.jira_auth = self._setup_jira_auth()
        
        # Session for connection pooling
        self.session = requests.Session()
        if self.jira_auth:
            self.session.auth = self.jira_auth
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        if not config_path:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'xray_config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            self.logger.warning(f"Could not load config file {config_path}: {e}")
            return {}
    
    def _setup_jira_auth(self) -> tuple:
        """Setup Jira authentication"""
        username = os.getenv('JIRA_USERNAME') or os.getenv('XRAY_USERNAME')
        password = os.getenv('JIRA_PASSWORD') or os.getenv('XRAY_PASSWORD')
        api_token = os.getenv('JIRA_API_TOKEN') or os.getenv('XRAY_API_TOKEN')
        
        if api_token:
            return (username, api_token)
        elif username and password:
            return (username, password)
        else:
            self.logger.warning("No Jira authentication credentials provided")
            return None
    
    def extract_jira_keys_from_features(self, features_dir: str = None) -> Set[str]:
        """
        Extract Jira ticket keys from feature files
        
        Args:
            features_dir: Directory containing feature files
            
        Returns:
            Set of unique Jira ticket keys
        """
        if not features_dir:
            features_dir = os.path.join(os.path.dirname(__file__), '..', 'test_framework', 'features')
        
        jira_keys = set()
        feature_files = Path(features_dir).glob('*.feature')
        
        # Pattern to match @TEST_RP-932 format
        pattern = re.compile(r'@TEST_([A-Z]+-\d+)')
        
        for feature_file in feature_files:
            try:
                with open(feature_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all matches
                matches = pattern.findall(content)
                for match in matches:
                    jira_keys.add(match)  # Add RP-932 (without @TEST_ prefix)
                    
            except Exception as e:
                self.logger.error(f"Error reading feature file {feature_file}: {e}")
        
        self.logger.info(f"Found {len(jira_keys)} unique Jira keys in feature files")
        return jira_keys
    
    def filter_automated_issues(self, jira_keys: Set[str]) -> List[str]:
        """
        Filter out issues that already have the 'automated' label
        
        Args:
            jira_keys: Set of Jira ticket keys
            
        Returns:
            List of ticket keys that need to be updated
        """
        if not self.jira_auth:
            self.logger.warning("No Jira authentication, skipping filter")
            return list(jira_keys)
        
        issues_to_update = []
        
        for key in jira_keys:
            try:
                # Get issue details
                url = f"{self.jira_base_url}/rest/api/2/issue/{key}"
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    issue_data = response.json()
                    labels = issue_data.get('fields', {}).get('labels', [])
                    
                    if 'automated' not in labels:
                        issues_to_update.append(key)
                        self.logger.debug(f"Issue {key} needs 'automated' label")
                    else:
                        self.logger.debug(f"Issue {key} already has 'automated' label")
                        
                elif response.status_code == 404:
                    self.logger.warning(f"Issue {key} not found")
                else:
                    self.logger.warning(f"Failed to get issue {key}: {response.status_code}")
                    # Add to update list to be safe
                    issues_to_update.append(key)
                    
            except Exception as e:
                self.logger.error(f"Error checking issue {key}: {e}")
                # Add to update list to be safe
                issues_to_update.append(key)
        
        self.logger.info(f"Found {len(issues_to_update)} issues that need 'automated' label")
        return issues_to_update
    
    def bulk_update_jira_issues(self, jira_keys: List[str], label: str = 'automated') -> bool:
        """
        Update multiple Jira issues with the specified label
        
        Args:
            jira_keys: List of Jira ticket keys to update
            label: Label to add to the issues
            
        Returns:
            True if all updates successful, False otherwise
        """
        if not self.jira_auth:
            self.logger.error("No Jira authentication configured")
            return False
        
        if not jira_keys:
            self.logger.info("No issues to update")
            return True
        
        success_count = 0
        
        for key in jira_keys:
            try:
                # Get current labels
                get_url = f"{self.jira_base_url}/rest/api/2/issue/{key}"
                response = self.session.get(get_url, timeout=30)
                
                if response.status_code != 200:
                    self.logger.error(f"Failed to get issue {key}: {response.status_code}")
                    continue
                
                issue_data = response.json()
                current_labels = issue_data.get('fields', {}).get('labels', [])
                
                # Add the new label if not already present
                if label not in current_labels:
                    new_labels = current_labels + [label]
                    
                    # Update the issue
                    update_url = f"{self.jira_base_url}/rest/api/2/issue/{key}"
                    update_data = {
                        "fields": {
                            "labels": new_labels
                        }
                    }
                    
                    response = self.session.put(
                        update_url,
                        json=update_data,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                    
                    if response.status_code == 204:
                        self.logger.info(f"Successfully added '{label}' label to {key}")
                        success_count += 1
                    else:
                        self.logger.error(f"Failed to update issue {key}: {response.status_code} - {response.text}")
                else:
                    self.logger.info(f"Issue {key} already has '{label}' label")
                    success_count += 1
                    
            except Exception as e:
                self.logger.error(f"Error updating issue {key}: {e}")
        
        self.logger.info(f"Successfully updated {success_count}/{len(jira_keys)} issues")
        return success_count == len(jira_keys)
    
    def run(self) -> bool:
        """
        Main execution method
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract Jira keys from feature files
            jira_keys = self.extract_jira_keys_from_features()
            
            if not jira_keys:
                self.logger.info("No Jira keys found in feature files")
                return True
            
            # Filter out issues that already have the automated label
            issues_to_update = self.filter_automated_issues(jira_keys)
            
            if not issues_to_update:
                self.logger.info("All issues already have the 'automated' label")
                return True
            
            # Update the issues
            return self.bulk_update_jira_issues(issues_to_update, 'automated')
            
        except Exception as e:
            self.logger.error(f"Error in main execution: {e}")
            return False
        finally:
            self.session.close()


def main():
    """Main entry point"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run the updater
    updater = JiraLabelUpdater()
    success = updater.run()
    
    if success:
        print("✅ Jira label update completed successfully")
        sys.exit(0)
    else:
        print("❌ Jira label update failed")
        sys.exit(1)


if __name__ == '__main__':
    main()
