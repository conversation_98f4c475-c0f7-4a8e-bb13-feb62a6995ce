# AI Test Framework Configuration - Ultra-Fast Mode
# This configuration optimizes for maximum speed with cached selectors

models:
  default: "pattern_matching"  # Fast pattern matching
  alternatives:
    - "llama3:8b"  # Only if needed
    - "mixtral:8x7b"  # Only if needed

# Base URL for the application
base_url: "https://rumah-baru.staging.belajar.id/"

browser:
  type: "chromium"
  headless: true  # Ultra-fast headless mode

timeouts:
  page_load: 800   # Ultra-fast loading
  element_wait: 100  # Ultra-fast element finding
  step_execution: 2000  # Ultra-fast execution

paths:
  locators: "./locators"
  reports: "./test_framework/reports"
  recordings: "./test_framework/test_recordings"

# Performance optimizations
performance:
  skip_animations: true
  disable_images: true
  disable_css: false
  disable_js: false
  fast_mode: true

ai_settings:
  max_retries: 3
  confidence_threshold: 0.6  # Lower threshold to accept more pattern matches
  # Pattern matching is the primary method - no AI
  enable_ai_locator_finding: false  # Disabled - no AI interaction
  enable_ai_step_interpretation: false  # Disabled - no AI interaction
  ai_timeout: 10
  # Pattern matching is the primary method
  fallback_to_pattern_matching: true
  prefer_pattern_matching: true
  # No AI usage at all
  use_ai_sparingly: false
  max_ai_requests_per_test: 0
  # No cloud AI - no local AI
  use_cloud_ai: false
  use_local_ai: false

test_execution:
  # Automatically skip failed steps and continue
  auto_skip_failed_steps: true
  max_retry_attempts: 2
  retry_delay: 2
  # Continue test execution even when elements are not found
  continue_on_element_not_found: true
  # Performance optimizations
  optimize_for_performance: true 