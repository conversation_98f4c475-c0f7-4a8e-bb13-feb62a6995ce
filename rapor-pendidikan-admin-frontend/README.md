# Frontend E2E Boilerplate

This project is a boilerplate for e2e testing using <PERSON><PERSON> and integrated with Xray.

## Prerequisites

Before you begin, ensure you have met the following requirements:

- You have installed [`nodejs`](https://nodejs.org/en/download/) >= 18.16.0.
- If you use Windows, you must already have [`WSL`](https://docs.microsoft.com/en-us/windows/wsl/install-win10) installed and read this [docs](https://wartek.atlassian.net/wiki/spaces/ENG/pages/1980989445/Running+Electron+Playwright+or+Cypress+in+WSL2)

## Tooling

The following tools are used in this project:

- [`playwright`](https://playwright.dev/) - A Node.js library to automate Chromium, Firefox and WebKit with a single API.
- [`typescript`](https://www.typescriptlang.org/) - A typed superset of JavaScript that compiles to plain JavaScript.
- [`xray`](https://www.getxray.app/) - A test management tool for Jira.

### Up & running the e2e project

To install the project, follow these steps:

1. **Fork the repository**. You can fork the repository by clicking the `Fork` button in the top right corner of the repository page and make sure project namespace value is `wartek-id/e2e-web`. Project name value must be equal to the project name that will be tested. For example, if you want to test `rapor-pendidikan-admin-frontend` project, the project name value must be `rapor-pendidikan-admin-frontend`.

_note: Forking the repository will be easier for you to sync the repository with the original repository if there are any changes._

2. **Clone the repository**: Once you've successfully forked the repository, you'll need to clone it to your local machine. Open your terminal or command prompt and use the following command:

```bash
<NAME_EMAIL>:wartek-id/e2e-web/rapor-pendidikan-admin-frontend.git e2e-rapor-pendidikan-admin-frontend
# Replace <your-e2e-project> with the name of the forked repository and <project_name> with the desired name of the local project directory.
```

3. **Install Dependencies**: Move into the cloned project directory and install the required dependencies using npm. Run the following command:

```bash
cd <project_name>
npm install
```

4. Copy the `.env.sample` file to `.env`.

```
cp .env.sample .env
```

5. Set up your environment variables in the `.env` file especially for users authentication.

   a. Check your `.env` file and update the `USER_EMAIL_ADMIN_USER` or add new user if needed. The value of the variable must be the same as the `email` in the `src/auth/users.ts` file.

   b. Just FYI, that variable is used in `/src/auth/users.ts` file. All of your testing users should be defined in that file.

   c. Update all of the variables in the `.env` file if needed.

### Running the tests

To run the tests locally, there are several ways to run the tests:

- Run all tests using `npm run test` command. This command will run all tests in production and staging environment based on defined project in the `playwright.config.ts` file. If we want to run tests with specific project, we can use flag `--project`.

```bash
npm run test -- --project=staging.mobile # run all tests in staging.mobile project

# or

npm run test -- --project=production.web # run all tests in production.web project
```

- Run specific test using jira task key tag. If your test scenario have tag `@SHIP-123`, you can run the test using flag `--grep`.

```bash
npm run test -- --grep SHIP-123
```

- For a more user-friendly debugging experience, you can use the UI mode to select the project and test scenarios:

```bash
npm run test:ui --project=staging.mobile # run all tests in staging.mobile project
```

![ui-debug](/docs/ui-debug.png)

## Commands

The following commands are available:

| Command                                     | Description                                                   |
| ------------------------------------------- | ------------------------------------------------------------- |
| `npm run test`                              | Run all tests with the current environment variables.         |
| `npm run test -- --grep SHIP-123`           | Run tests with the title containing "SHIP-123".               |
| `npm run test -- --grep SHIP-123\|SHIP-999` | Run tests with the title containing "SHIP-123" or "SHIP-999". |
| `npm run test:staging`                      | Run all tests containing `@staging` tag.                      |
| `npm run test:production`                   | Run all tests containing `@production` tag.                   |
| `npm run test:visual`                       | Run all visual regression tests containing `@visual` tag.     |
| `npm run test:snapshot`                     | Run all visual regression with updating snapshot.             |
| `npm run test:ui`                           | Run all tests in UI mode.                                     |
| `npm run test:debug`                        | Run all tests in debug mode.                                  |
| `npm run test:headed`                       | Run all tests in headed mode.                                 |
| `npm run report`                            | Run generated report.                                         |
| `npm run lint`                              | Run linting.                                                  |
| `npm run docker`                            | Run docker container for running tests.                       |

## Project Structure

The project structure is as follows:

```
├── .env
├── src
│   ├── auth                            # auth helper to login to the application
│   │   ├── google-auth.helper.ts
│   │   ├── custom-auth.helper.ts
│   │   └── users.ts
│   ├── constants                       # constants variables for tests
│   │   └── <constant_name>.constant.ts
│   ├── utils                           # utility files for tests that can be reused put here
│   └── helpers                         # helper files for tests that can be reused put here
│       ├── <helper_name>.helper.ts
│       └── <helper_name>.helper.ts
├── tests                               # focus on this directory for writing tests
│   ├── configs                         # configuration files for tests
│   │   ├── global.setup.ts             # global setup file to run before all tests
│   │   ├── production.setup.ts         # configuration file for production environment
│   │   └── staging.setup.ts            # configuration file for staging environment
│   ├── <feature_name>                  # folder tests related to a feature, we can have multiple feature folders
│   │   ├── <scenario_name>.spec.ts
│   │   └── <scenario_name>.spec.ts
│   └── <feature_name>
│       ├── <scenario_name>.spec.ts
│       └── <scenario_name>.spec.ts
└── playwright.config.ts                # used to configure playwright

```

## Create new test

To add a new test scenario to the e2e project, follow these steps:

1. **Create a new folder**: Begin by creating a new folder in the tests directory with a descriptive name that represents the feature or functionality you are testing e.g. `tests/bukti-karya`.
2. **Create a Test File**: Inside the newly created folder, add a new test file that corresponds to the specific scenario you want to test. The test file name should be descriptive and related to the scenario. For instance, if you are testing the bookmark functionality with different user roles, you could create a test file named e.g. `tests/bukti-karya/bookmark.spec.ts`.
3. **Tagging the Test**: Consider using tags to label and categorize your tests. For example, you can add `@staging` or `@production` tags on the title test to indicate which environment the test should run in. You can also use specific Jira task keys as tags for test traceability. For instance, adding `@XRAY-123` as a tag will help integrate the test case with Xray. Use `connectXray` function in the test file to connect the test case with Xray. You can see the example in the `tests/examples/guru/*.spec.ts` file. Make sure the test case key in the `connectXray` function is the same as the `@XRAY-123` tag in the test title.
4. **Run the Test**: After writing the test, you can run it locally using the available npm commands, such as `npm run test` with specific project flags or `npm run test:ui` for UI mode debugging.
5. **View Test Results**: Once the tests are executed, you can view the test results either in the console or generate a report using `npm run report` command.

_note: if you want your test case integrated with Xray, you must follow configuration steps in the [Xray Integration](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2443182083/How+to+Create+Jira+Automation+for+E2E+Web+Tests) section._

## FAQ

<details>
<summary>Where is the confluence about e2e web?</summary>

You can find the confluence [here](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2434302405/Draft+-+Buku+Saku+-+End+to+End+E2E+Test+Web).

</details>

<details>
<summary>Why do we have to implement the E2E tests?</summary>

You can read deatil about it on [this article](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2244706515)

</details>

<details>
<summary>My squad wants to start implementing end-to-end tests; what should I do?</summary>

You can read [this section](<https://wartek.atlassian.net/wiki/spaces/ENG/pages/2434302405/Buku+Saku+-+End+to+End+(E2E)+Test+Web#Setup>) to get started implementing E2E test

</details>

<summary>My squad wants to start implementing visual tests; what should I do?</summary>

You can read [this section](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2452226402/Visual+Testing) to get started implementing Visual test

</details>

<details>
<summary>How and where to trigger the E2E test?</summary>

- Manual run pipeline on Gitlab E2E Repository
- [Jira/Xray automation trigger](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2442920051/How+to+Trigger+E2E+Tests+CI+Pipeline+from+Jira+Xray)
- [Gitlab scheduler](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2444001652)

</details>

<details>
<summary>How to integrate the E2E tests into XRay/Jira?</summary>

- [How to Trigger E2E Tests CI Pipeline from Jira/Xray](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2442920051/How+to+Trigger+E2E+Tests+CI+Pipeline+from+Jira+Xray)
- [How to Create Jira Automation for E2E Web Tests](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2443182083)

</details>

<details>
<summary>My E2E is not using Google Auth for login. Can I use a different login strategy?</summary>

Of course. You can add a custom authentication strategy to handle login in your e2e tests. You can create a custom authentication helper following the steps outlined in [this article](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2444853423).

</details>

<details>
<summary>Is it possible running a Smoke Test?</summary>

Absolutely. GitLab CI/CD schedules allow you to run end-to-end tests as smoke tests at regular intervals. Set up a schedule with a short interval, such as daily, to ensure critical functionalities are frequently tested. For a step-by-step guide on integrating GitLab CI/CD schedules, check out [this article](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2444001652).

</details>

<details>
<summary>What if I have multiple users to test?</summary>

Tools that we build can handle case if we want to test multiple users. You can read detail about it on [this article](https://wartek.atlassian.net/wiki/spaces/ENG/pages/2446295186).

</details>

## Changelog

See [CHANGELOG.md](CHANGELOG.md).
