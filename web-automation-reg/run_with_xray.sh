#!/bin/bash

# Web Automation Reg - Run with Xray Integration
# This script runs the test suite and ensures Xray integration is properly configured

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to load environment variables
load_environment() {
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env"
        export $(grep -v '^#' .env | xargs)
    else
        print_warning ".env file not found. Using default configuration."
        print_warning "Copy .env.example to .env and configure your Xray credentials."
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Python
    if ! command_exists python3; then
        print_error "Python 3 is required but not installed."
        exit 1
    fi
    
    # Check pip packages
    if ! python3 -c "import requests, yaml, lxml" 2>/dev/null; then
        print_warning "Some required packages may be missing. Installing dependencies..."
        pip3 install -r requirements.txt
    fi
    
    # Check if run_scaled.sh exists
    if [ ! -f "run_scaled.sh" ]; then
        print_error "run_scaled.sh not found. Please run this script from the project root."
        exit 1
    fi
    
    # Make run_scaled.sh executable
    chmod +x run_scaled.sh
    
    print_success "Prerequisites check completed"
}

# Function to test Xray configuration
test_xray_config() {
    print_status "Testing Xray configuration..."
    
    if [ "${XRAY_ENABLED:-true}" = "false" ]; then
        print_warning "Xray integration is disabled (XRAY_ENABLED=false)"
        return 0
    fi
    
    if [ -z "${XRAY_USERNAME:-}" ] || [ -z "${XRAY_API_TOKEN:-}" ]; then
        print_warning "Xray credentials not configured. Tests will run without Xray integration."
        print_warning "Set XRAY_USERNAME and XRAY_API_TOKEN environment variables to enable Xray."
        return 0
    fi
    
    # Run Xray setup test
    if python3 scripts/setup_xray.py > /tmp/xray_test.log 2>&1; then
        print_success "Xray configuration test passed"
    else
        print_warning "Xray configuration test failed. Check /tmp/xray_test.log for details."
        print_warning "Tests will run but Xray integration may not work properly."
    fi
}

# Function to update Jira labels
update_jira_labels() {
    if [ "${XRAY_ENABLED:-true}" = "true" ] && [ -n "${XRAY_USERNAME:-}" ] && [ -n "${XRAY_API_TOKEN:-}" ]; then
        print_status "Updating Jira labels for automated tests..."
        
        if python3 scripts/update_jira_labels.py; then
            print_success "Jira labels updated successfully"
        else
            print_warning "Failed to update Jira labels (non-blocking)"
        fi
    else
        print_status "Skipping Jira label update (Xray not configured)"
    fi
}

# Function to run tests
run_tests() {
    print_status "Starting test execution with Xray integration..."
    
    # Set environment variables for test execution
    export XRAY_ENABLED="${XRAY_ENABLED:-true}"
    export GENERATE_JUNIT_XML="true"
    
    # Run the main test script
    print_status "Executing: ./run_scaled.sh -a"
    
    if ./run_scaled.sh -a; then
        print_success "Test execution completed successfully"
        return 0
    else
        print_error "Test execution failed"
        return 1
    fi
}

# Function to check test results
check_results() {
    print_status "Checking test results..."
    
    # Check if JUnit XML was generated
    if [ -f "junit.xml" ]; then
        print_success "JUnit XML report generated: junit.xml"
        
        # Parse and display summary
        if command_exists python3; then
            python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('junit.xml')
    root = tree.getroot()
    for testsuite in root.findall('testsuite'):
        name = testsuite.get('name', 'Unknown')
        tests = testsuite.get('tests', '0')
        failures = testsuite.get('failures', '0')
        errors = testsuite.get('errors', '0')
        skipped = testsuite.get('skipped', '0')
        time = testsuite.get('time', '0')
        print(f'📊 Test Suite: {name}')
        print(f'   Tests: {tests}, Failures: {failures}, Errors: {errors}, Skipped: {skipped}')
        print(f'   Execution Time: {time}s')
except Exception as e:
    print(f'⚠️ Could not parse JUnit XML: {e}')
"
        fi
    else
        print_warning "JUnit XML report not found"
    fi
    
    # Check reports directory
    if [ -d "test_framework/reports" ]; then
        report_count=$(find test_framework/reports -name "*.html" -o -name "*.json" | wc -l)
        print_success "Generated $report_count report files in test_framework/reports/"
    fi
    
    # Check if Xray integration worked
    if [ "${XRAY_ENABLED:-true}" = "true" ] && [ -n "${XRAY_USERNAME:-}" ]; then
        print_status "Xray integration status will be shown in the test output above"
    fi
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up temporary files..."
    
    # Remove temporary files if they exist
    [ -f "/tmp/xray_test.log" ] && rm -f /tmp/xray_test.log
    
    print_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  --skip-xray-test    Skip Xray configuration test"
    echo "  --skip-labels       Skip Jira label update"
    echo "  --xray-only         Only run Xray configuration test"
    echo ""
    echo "Environment Variables:"
    echo "  XRAY_ENABLED        Enable/disable Xray integration (default: true)"
    echo "  XRAY_USERNAME       Jira username/email"
    echo "  XRAY_API_TOKEN      Jira API token"
    echo "  XRAY_BASE_URL       Jira instance URL"
    echo "  XRAY_PROJECT_KEY    Project key (default: RP)"
    echo ""
    echo "Examples:"
    echo "  $0                  Run tests with full Xray integration"
    echo "  $0 --skip-xray-test Run tests without testing Xray config first"
    echo "  $0 --xray-only      Only test Xray configuration"
    echo ""
}

# Main execution
main() {
    local skip_xray_test=false
    local skip_labels=false
    local xray_only=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            --skip-xray-test)
                skip_xray_test=true
                shift
                ;;
            --skip-labels)
                skip_labels=true
                shift
                ;;
            --xray-only)
                xray_only=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_status "🚀 Web Automation Reg - Xray Integration Runner"
    print_status "=============================================="
    
    # Load environment
    load_environment
    
    # Check prerequisites
    check_prerequisites
    
    # Test Xray configuration
    if [ "$skip_xray_test" = false ]; then
        test_xray_config
    fi
    
    # If only testing Xray, exit here
    if [ "$xray_only" = true ]; then
        print_success "Xray configuration test completed"
        exit 0
    fi
    
    # Update Jira labels
    if [ "$skip_labels" = false ]; then
        update_jira_labels
    fi
    
    # Run tests
    if run_tests; then
        # Check results
        check_results
        
        print_success "🎉 Test execution with Xray integration completed successfully!"
        exit 0
    else
        print_error "❌ Test execution failed"
        exit 1
    fi
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run main function with all arguments
main "$@"
