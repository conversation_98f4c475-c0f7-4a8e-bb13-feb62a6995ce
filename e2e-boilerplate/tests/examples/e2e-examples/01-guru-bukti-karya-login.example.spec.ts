import { connectXray, screenshot } from '@/helpers/global.helper'
import { expectLoggedInUser, goToBuktiKaryaPage } from '@/helpers/test.helper'
import { expect, test } from '@playwright/test'

test.describe('<PERSON><PERSON><PERSON>', () => {
  // https://wartek.atlassian.net/browse/SHIP-2780 (It uses WEBEX-850 for testing purposes.)
  test('@WEBEX-850 @production Check the draft list on Karya Saya', async ({ page }, testInfo) => {
    connectXray(testInfo, { key: 'SHIP-2780' })

    await test.step('Given guru is on home page with already login using account "GURU_SATU"', async () => {
      await goToBuktiKaryaPage(page)
      await expectLoggedInUser(page, 'GURU_SATU')
    })

    await test.step('And guru follow coach mark', async () => {
      await page.getByRole('button', { name: '<PERSON>n<PERSON><PERSON>' }).click()
      await page.getByRole('button', { name: '<PERSON><PERSON><PERSON><PERSON>' }).click()
      await page.getByRole('button', { name: '<PERSON><PERSON><PERSON>' }).click()
    })

    await test.step('When guru go to Bukti Karya menu with "Karya Saya" tab', async () => {
      await page.waitForLoadState('networkidle')
      await page.getByRole('tab', { name: 'Karya Saya' }).click({ delay: 1000 })
    })

    await test.step('Then user sees a list of categories and draft files', async () => {
      await expect(page.getByRole('tab', { name: 'Karya Saya' })).toBeVisible()
      await expect(page.getByText('Artikel', { exact: true })).toBeVisible()
      await expect(page.getByText('Bahan Ajar', { exact: true })).toBeVisible()
      await expect(page.getByText('Kepemimpinan Sekolah', { exact: true })).toBeVisible()
      await expect(page.getByText('Praktik Pembelajaran', { exact: true })).toBeVisible()
      await expect(page.getByText('Praktik Baik', { exact: true })).toBeVisible()
      await expect(page.getByText('RPP/Modul Ajar', { exact: true })).toBeVisible()
      await expect(page.getByText('Karya Lainnya', { exact: true })).toBeVisible()
    })

    await screenshot(page, testInfo)
  })

  // https://wartek.atlassian.net/browse/SHIP-2810
  // Skipped. The feature already changed but this test implementation is a good example.
  // eslint-disable-next-line playwright/no-skipped-test
  test.skip('@SHIP-EXAMPLE2810 @production Action to bookmark Karya and unbookmark in Video Detail page', async ({
    page,
  }, testInfo) => {
    connectXray(testInfo, { key: 'SHIP-2810' })

    await test.step('Given guru is on home page with already login using account "GURU_SATU"', async () => {
      await goToBuktiKaryaPage(page)
      await expectLoggedInUser(page, 'GURU_SATU')
    })

    await test.step('And guru navigate to "Bukti Karya" page', async () => {
      expect(page.url()).toEqual(expect.stringContaining('/bukti-karya'))
    })

    await test.step('And guru follow coach mark', async () => {
      await page.getByRole('button', { name: 'Lanjut' }).click()
      await page.getByRole('button', { name: 'Lanjut' }).click()
      await page.getByRole('button', { name: 'Selesai' }).click()
    })

    let karyaId: string

    await test.step('When guru save the video on video detail page', async () => {
      const explorationFirstItem = page.getByTestId(/bk-exploration-item/).first()
      const anchorElement = explorationFirstItem.getByRole('link')
      const href = await anchorElement.getAttribute('href')

      await page
        .getByTestId(/bk-exploration-item/)
        .first()
        .click()

      karyaId = href.split('/')[3].split('?')[0]

      await page.getByRole('button', { name: 'Tandai' }).click()
    })

    await test.step('Then the message "Karya berhasil ditandai" will be display', async () => {
      await expect(page.getByText('Karya berhasil ditandai', { exact: true })).toBeVisible()
    })

    await test.step('And guru unsave the video from bookmark page', async () => {
      await page.getByRole('button', { name: 'header-bar-left-action' }).click()
      await page.getByRole('tab', { name: 'Ditandai' }).click()

      await page.waitForLoadState('networkidle')

      await page.locator(`a[href*="${karyaId}"]`).click()
      await page.getByRole('button', { name: 'Tandai' }).click()

      await expect(page.getByText('Dihapus dari daftar yang ditandai', { exact: true })).toBeVisible()

      await screenshot(page, testInfo)
    })
  })
})
