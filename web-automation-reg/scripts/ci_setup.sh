#!/bin/bash

# CI Setup Script for Web Automation Reg
# Prepares the environment for running tests in CI/CD pipelines

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[CI-SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[CI-SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[CI-WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[CI-ERROR]${NC} $1"
}

# Function to detect CI environment
detect_ci_environment() {
    if [ -n "${GITLAB_CI}" ]; then
        echo "gitlab"
    elif [ -n "${GITHUB_ACTIONS}" ]; then
        echo "github"
    elif [ -n "${CI}" ]; then
        echo "generic"
    else
        echo "local"
    fi
}

# Function to set CI-specific environment variables
setup_ci_environment() {
    local ci_env=$(detect_ci_environment)
    
    print_status "Detected CI environment: $ci_env"
    
    # Set common CI environment variables
    export CI=true
    export HEADLESS=true
    export BROWSER_HEADLESS=true
    export PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    
    # Set test configuration for CI
    export TEST_TIMEOUT=60
    export TEST_PARALLEL_WORKERS=4
    export TEST_CHUNK_SIZE=5
    export GENERATE_JUNIT_XML=true
    export SAVE_SCREENSHOTS=true
    export SAVE_VIDEOS=false
    
    # Set Xray integration for CI
    export XRAY_ENABLED="${XRAY_ENABLED:-true}"
    export XRAY_ENVIRONMENT="${XRAY_ENVIRONMENT:-staging}"
    
    # Set browser configuration for CI
    export BROWSER_TYPE=chromium
    export BROWSER_TIMEOUT=30000
    export BROWSER_SLOW_MO=0
    
    # Set performance optimizations for CI
    export ENABLE_CACHE=false
    export PARALLEL_MODE=true
    export DEBUG_MODE=false
    
    case $ci_env in
        "gitlab")
            setup_gitlab_ci
            ;;
        "github")
            setup_github_actions
            ;;
        "generic")
            setup_generic_ci
            ;;
        "local")
            setup_local_environment
            ;;
    esac
}

# Function to setup GitLab CI specific configuration
setup_gitlab_ci() {
    print_status "Setting up GitLab CI configuration"
    
    # GitLab CI specific environment variables
    export CI_PIPELINE_ID="${CI_PIPELINE_ID:-unknown}"
    export CI_JOB_ID="${CI_JOB_ID:-unknown}"
    export CI_COMMIT_SHA="${CI_COMMIT_SHA:-unknown}"
    export CI_COMMIT_REF_NAME="${CI_COMMIT_REF_NAME:-unknown}"
    
    # Set test execution metadata
    export TEST_EXECUTION_ID="gitlab-${CI_PIPELINE_ID}-${CI_JOB_ID}"
    export TEST_ENVIRONMENT="${CI_COMMIT_REF_NAME}"
    
    # Adjust environment based on branch
    case "${CI_COMMIT_REF_NAME}" in
        "main"|"master")
            export XRAY_ENVIRONMENT="production"
            export BASE_URL="${PRODUCTION_BASE_URL:-https://rumah.pendidikan.go.id/}"
            ;;
        "develop"|"staging")
            export XRAY_ENVIRONMENT="staging"
            export BASE_URL="${STAGING_BASE_URL:-https://rumah-baru.staging.belajar.id/}"
            ;;
        *)
            export XRAY_ENVIRONMENT="staging"
            export BASE_URL="${STAGING_BASE_URL:-https://rumah-baru.staging.belajar.id/}"
            ;;
    esac
    
    print_status "GitLab CI configuration completed"
    print_status "  Pipeline ID: ${CI_PIPELINE_ID}"
    print_status "  Job ID: ${CI_JOB_ID}"
    print_status "  Branch: ${CI_COMMIT_REF_NAME}"
    print_status "  Environment: ${XRAY_ENVIRONMENT}"
    print_status "  Base URL: ${BASE_URL}"
}

# Function to setup GitHub Actions specific configuration
setup_github_actions() {
    print_status "Setting up GitHub Actions configuration"
    
    # GitHub Actions specific environment variables
    export GITHUB_RUN_ID="${GITHUB_RUN_ID:-unknown}"
    export GITHUB_RUN_NUMBER="${GITHUB_RUN_NUMBER:-unknown}"
    export GITHUB_SHA="${GITHUB_SHA:-unknown}"
    export GITHUB_REF_NAME="${GITHUB_REF_NAME:-unknown}"
    
    # Set test execution metadata
    export TEST_EXECUTION_ID="github-${GITHUB_RUN_ID}-${GITHUB_RUN_NUMBER}"
    export TEST_ENVIRONMENT="${GITHUB_REF_NAME}"
    
    print_status "GitHub Actions configuration completed"
}

# Function to setup generic CI configuration
setup_generic_ci() {
    print_status "Setting up generic CI configuration"
    
    # Generic CI environment variables
    export TEST_EXECUTION_ID="ci-$(date +%s)"
    export TEST_ENVIRONMENT="ci"
    
    print_status "Generic CI configuration completed"
}

# Function to setup local development environment
setup_local_environment() {
    print_status "Setting up local development environment"
    
    # Local development environment variables
    export CI=false
    export HEADLESS=false
    export BROWSER_HEADLESS=false
    export SAVE_VIDEOS=true
    export DEBUG_MODE=true
    
    # Use staging environment for local development
    export XRAY_ENVIRONMENT="staging"
    export BASE_URL="${STAGING_BASE_URL:-https://rumah-baru.staging.belajar.id/}"
    export TEST_EXECUTION_ID="local-$(date +%s)"
    
    print_status "Local development configuration completed"
}

# Function to verify CI setup
verify_ci_setup() {
    print_status "Verifying CI setup..."
    
    # Check required directories
    local required_dirs=("test_framework" "test_framework/features" "test_framework/modules")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            print_error "Required directory not found: $dir"
            return 1
        fi
    done
    
    # Check required files
    local required_files=("run_scaled.sh" "requirements.txt" "test_framework/parallel_executor.py")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            return 1
        fi
    done
    
    # Check if run_scaled.sh is executable
    if [ ! -x "run_scaled.sh" ]; then
        print_warning "run_scaled.sh is not executable, fixing..."
        chmod +x run_scaled.sh
    fi
    
    # Check Python dependencies
    if ! python3 -c "import playwright, requests, yaml" 2>/dev/null; then
        print_error "Required Python dependencies not installed"
        return 1
    fi
    
    # Check Playwright browsers
    if ! playwright --version >/dev/null 2>&1; then
        print_error "Playwright not properly installed"
        return 1
    fi
    
    print_success "CI setup verification completed successfully"
    return 0
}

# Function to display environment summary
display_environment_summary() {
    print_status "Environment Summary:"
    echo "  CI Environment: $(detect_ci_environment)"
    echo "  Headless Mode: ${HEADLESS:-false}"
    echo "  Test Environment: ${XRAY_ENVIRONMENT:-staging}"
    echo "  Base URL: ${BASE_URL:-not set}"
    echo "  Parallel Workers: ${TEST_PARALLEL_WORKERS:-4}"
    echo "  Chunk Size: ${TEST_CHUNK_SIZE:-5}"
    echo "  Xray Enabled: ${XRAY_ENABLED:-true}"
    echo "  JUnit XML: ${GENERATE_JUNIT_XML:-true}"
    echo "  Test Execution ID: ${TEST_EXECUTION_ID:-not set}"
}

# Main execution
main() {
    print_status "🚀 Web Automation Reg - CI Setup"
    print_status "================================="
    
    # Setup CI environment
    setup_ci_environment
    
    # Verify setup
    if verify_ci_setup; then
        print_success "✅ CI setup completed successfully"
        
        # Display summary
        display_environment_summary
        
        # Export all environment variables to a file for sourcing
        cat > ci_environment.env << EOF
# CI Environment Variables for Web Automation Reg
export CI=${CI}
export HEADLESS=${HEADLESS}
export BROWSER_HEADLESS=${BROWSER_HEADLESS}
export TEST_TIMEOUT=${TEST_TIMEOUT}
export TEST_PARALLEL_WORKERS=${TEST_PARALLEL_WORKERS}
export TEST_CHUNK_SIZE=${TEST_CHUNK_SIZE}
export GENERATE_JUNIT_XML=${GENERATE_JUNIT_XML}
export XRAY_ENABLED=${XRAY_ENABLED}
export XRAY_ENVIRONMENT=${XRAY_ENVIRONMENT}
export BASE_URL=${BASE_URL}
export TEST_EXECUTION_ID=${TEST_EXECUTION_ID}
export BROWSER_TYPE=${BROWSER_TYPE}
export BROWSER_TIMEOUT=${BROWSER_TIMEOUT}
export SAVE_SCREENSHOTS=${SAVE_SCREENSHOTS}
export SAVE_VIDEOS=${SAVE_VIDEOS}
export DEBUG_MODE=${DEBUG_MODE}
EOF
        
        print_success "Environment variables saved to ci_environment.env"
        print_status "Ready to run tests with: ./run_scaled.sh -a"
        
        return 0
    else
        print_error "❌ CI setup failed"
        return 1
    fi
}

# Run main function
main "$@"
