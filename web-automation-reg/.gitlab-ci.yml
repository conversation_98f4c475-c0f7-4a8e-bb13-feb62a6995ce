stages:
  - test
  - report

variables:
  # Python and Playwright versions
  PYTHON_VERSION: "3.9"
  PLAYWRIGHT_VERSION: "v1.41.1-focal"

  # Test configuration
  TEST_FEATURES_DIR: "./test_framework/features"
  TEST_OUTPUT_DIR: "./output"
  TEST_REPORTS_DIR: "./test_framework/reports"

  # Xray integration configuration
  XRAY_ENABLED: "true"
  XRAY_BASE_URL: "https://wartek.atlassian.net"
  XRAY_PROJECT_KEY: "RP"
  XRAY_ENVIRONMENT: "staging"
  XRAY_TIMEOUT: "30"
  XRAY_RETRY_ATTEMPTS: "3"

# Cache dependencies for faster builds
cache:
  paths:
    - .cache/pip
    - .cache/playwright

.e2e_test_template: &e2e_test_template
  stage: test
  image: mcr.microsoft.com/playwright:${PLAYWRIGHT_VERSION}
  before_script:
    # Update package lists and install Python dependencies
    - apt-get update && apt-get install -y python3 python3-pip python3-venv
    - python3 --version
    - pip3 --version
    
    # Install Python dependencies
    - pip3 install -r requirements.txt
    
    # Install Playwright browsers
    - playwright install
    
    # Verify installation
    - python3 -c "import playwright; print('Playwright installed successfully')"
    - playwright --version
    
    # Create necessary directories
    - mkdir -p ${TEST_OUTPUT_DIR}
    - mkdir -p ${TEST_REPORTS_DIR}
    
    # Set permissions for output directories
    - chmod -R 777 ${TEST_OUTPUT_DIR}
    - chmod -R 777 ${TEST_REPORTS_DIR}
  
  script:
    # Run the existing test script without changing the flow
    - echo "🚀 Starting web-automation-reg test execution..."
    - echo "📁 Features directory: ${TEST_FEATURES_DIR}"
    - echo "📁 Output directory: ${TEST_OUTPUT_DIR}"
    - echo "📁 Reports directory: ${TEST_REPORTS_DIR}"
    
    # Execute the existing run script
    - ./run_scaled.sh -a
    
    # Verify JUnit XML was generated
    - echo "🔍 Checking for generated reports..."
    - ls -la ${TEST_REPORTS_DIR}/
    - ls -la ./
    
    # Check if junit.xml exists
    - if [ -f "junit.xml" ]; then
        echo "✅ JUnit XML generated successfully";
        echo "📊 JUnit XML content preview:";
        head -20 junit.xml;
      else
        echo "❌ JUnit XML not found in root directory";
        echo "🔍 Checking test_framework directory:";
        find . -name "junit.xml" -type f;
      fi

    # Update Jira labels for automated tests
    - echo "🏷️ Updating Jira labels for automated tests..."
    - python3 scripts/update_jira_labels.py || echo "⚠️ Jira label update failed (non-blocking)"
  
  artifacts:
    reports:
      junit: junit.xml
    paths:
      - junit.xml
      - ${TEST_REPORTS_DIR}/
      - ${TEST_OUTPUT_DIR}/
      - test_framework/reports/
    expire_in: 1 week
    
  tags:
    - docker
  
  # Allow failure for now during development
  allow_failure: true

# Main test job
e2e-test:
  <<: *e2e_test_template
  stage: test
  variables:
    TEST_ENVIRONMENT: "staging"
  only:
    - main
    - develop
    - merge_requests
  except:
    - tags

# Test job for merge requests
e2e-test-mr:
  <<: *e2e_test_template
  stage: test
  variables:
    TEST_ENVIRONMENT: "staging"
  only:
    - merge_requests
  allow_failure: true

# Test job for specific branches
e2e-test-branch:
  <<: *e2e_test_template
  stage: test
  variables:
    TEST_ENVIRONMENT: "staging"
  only:
    - branches
  except:
    - main
    - develop
    - tags
  allow_failure: true

# Report generation job (optional)
generate-report:
  stage: report
  image: mcr.microsoft.com/playwright:${PLAYWRIGHT_VERSION}
  dependencies:
    - e2e-test
  script:
    - echo "📊 Generating comprehensive test report..."
    - echo "📁 Available artifacts:"
    - ls -la
    
    # Check if JUnit XML exists
    - if [ -f "junit.xml" ]; then
        echo "✅ JUnit XML found, processing...";
        echo "📊 Test results summary:";
        python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('junit.xml')
    root = tree.getroot()
    for testsuite in root.findall('testsuite'):
        name = testsuite.get('name', 'Unknown')
        tests = testsuite.get('tests', '0')
        failures = testsuite.get('failures', '0')
        errors = testsuite.get('errors', '0')
        skipped = testsuite.get('skipped', '0')
        time = testsuite.get('time', '0')
        print(f'Suite: {name}')
        print(f'  Tests: {tests}')
        print(f'  Failures: {failures}')
        print(f'  Errors: {errors}')
        print(f'  Skipped: {skipped}')
        print(f'  Time: {time}s')
        print()
except Exception as e:
    print(f'Error parsing JUnit XML: {e}')
        ";
      else
        echo "❌ JUnit XML not found, cannot generate report";
        exit 1;
      fi
    
    - echo "🎯 Report generation completed"
  
  artifacts:
    paths:
      - junit.xml
    expire_in: 1 week
    
  tags:
    - docker
  
  only:
    - main
    - develop
    - merge_requests
  except:
    - tags
  allow_failure: true
