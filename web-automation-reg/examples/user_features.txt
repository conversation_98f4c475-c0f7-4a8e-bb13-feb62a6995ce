# Example User Features - Text Format
# This file shows how users can write features in plain text format

Feature: User Login Test
@TC_LOGIN_001
Scenario: Successful Login
buka halaman utama
ketik "<EMAIL>" di field email
ketik "password123" di field password
klik tombol login
pastikan halaman dashboard terbuka

@TC_LOGIN_002
Scenario: Failed Login with Invalid Credentials
buka halaman utama
ketik "<EMAIL>" di field email
ketik "wrongpassword" di field password
klik tombol login
pastikan pesan error muncul

Feature: Search Functionality
@TC_SEARCH_001
Scenario: Basic Search
buka halaman utama
ketik "P<PERSON><PERSON>han" di field pencarian
klik tombol cari
pastikan hasil pencarian muncul
pastikan "Pela<PERSON>han" ada di hasil

@TC_SEARCH_002
Scenario: Search with No Results
buka halaman utama
ketik "xyz123nonexistent" di field pencarian
klik tombol cari
pastikan pesan "Tidak ada hasil" muncul

Feature: Navigation Test
@TC_NAV_001
Scenario: Navigate to Different Sections
buka halaman utama
scroll ke section "Ruang GTK"
klik "Ruang GTK"
pastikan halaman Ruang GTK terbuka
pastikan "Layanan yang Tersedia" muncul
scroll ke section "Karir dan Kinerja"
pastikan "Karir dan Kinerja" muncul 