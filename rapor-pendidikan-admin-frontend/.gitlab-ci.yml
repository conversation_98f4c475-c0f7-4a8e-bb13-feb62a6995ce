image: mcr.microsoft.com/playwright:v1.41.1-jammy

include:
  # https://gitlab.com/wartek-id/e2e-web/cicd-templates/-/blob/main/e2e-web.gitlab-ci.yml
  - project: wartek-id/e2e-web/cicd-templates
    file: e2e-web.gitlab-ci.yml
    ref: main
  - project: wartek-id/e2e-web/e2e-trigger
    file: config/groups/rapor-pendidikan-frontend.yml
    ref: main

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_PIPELINE_SOURCE == "trigger"'
    - if: '$CI_PIPELINE_SOURCE == "api"'
    - if: '$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS'
      when: never
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_COMMIT_TAG'

variables:
  NODE_CACHE_DIR: $CI_PROJECT_DIR/node_modules/
  GITLAB_AUTH_TOKEN: $FE_TOOLBOX_READ_REGISTRY_TOKEN

stages:
  - before-ci
  - test
  - after-test
  - after-ci

.skip-production:
  rules:
    - if: $STAGING_ONLY == "true"
      when: never
    - when: always

.skip-staging:
  rules:
    - if: $PRODUCTION_ONLY == "true"
      when: never
    - when: always

prepare:
  stage: before-ci
  script:
    - npm ci
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - $NODE_CACHE_DIR
    policy: pull-push
  artifacts:
    paths:
      - node_modules/

typecheck-lint:
  stage: test
  script:
    - cp $ENVS .env
    - npm run test:utils
    - npm run typecheck
    - npm run lint

e2e-test-staging:
  extends:
    - .template_test
  rules:
    - !reference [.skip-staging, rules]
  variables:
    ENVIRONMENT: staging
    STAGING_ONLY: 'true'
  script:
    - npx tsx scripts/ci-filter-test-keys-defined-in-MR.ts
    - !reference [.template_test, script]
    # - you can extend the script here

e2e-test-production:
  extends:
    - .template_test
  rules:
    - !reference [.skip-production, rules]
  variables:
    ENVIRONMENT: production
    PRODUCTION_ONLY: 'true'
  script:
    - npx tsx scripts/ci-filter-test-keys-defined-in-MR.ts
    - !reference [.template_test, script]
    # - you can extend the script here

pages:
  stage: after-ci
  dependencies:
    - e2e-test-staging
    - e2e-test-production
  tags:
    - himem
  script:
    - rm -rf public
    - mkdir public
    - cp -r playwright-report/* public/
  artifacts:
    paths:
      - public
