import re
import time
import async<PERSON>
from playwright.async_api import Page
from bs4 import BeautifulSoup

class SmartLocatorFinder:
    """Advanced smart locator finder that can handle dynamic websites and find elements more reliably."""
    
    def __init__(self):
        """Initialize the smart locator finder."""
        self.found_locators = {}
        self.page_content_cache = {}
        self.retry_count = 0
        self.max_retries = 3
        self.collected_locators = {}  # Store all collected locators
        self.context_aware_patterns = {
            'faq': ['accordion', 'question', 'answer', 'expand', 'collapse'],
            'search': ['search', 'cari', 'pencarian', 'find'],
            'navigation': ['menu', 'nav', 'link', 'button'],
            'form': ['input', 'submit', 'form', 'field'],
            'content': ['text', 'title', 'heading', 'paragraph'],
            'service': ['service', 'layanan', 'ruang', 'pendidikan', 'akun', 'rapor', 'neraca', 'manajemen', 'any_listed_service']
        }
        
    async def collect_all_locators(self, page: Page):
        """Collect all available locators from the current page dynamically."""
        try:
            print("🔍 Collecting all locators from current page...")
            
            # Get page content
            html_content = await page.content()
            soup = BeautifulSoup(html_content, 'html.parser')
            
            collected = {
                'buttons': [],
                'links': [],
                'inputs': [],
                'text_elements': [],
                'images': [],
                'forms': []
            }
            
            # Collect buttons
            buttons = soup.find_all(['button', 'input[type="button"]', 'input[type="submit"]'])
            for button in buttons:
                if button.text and button.text.strip():
                    collected['buttons'].append({
                        'text': button.text.strip(),
                        'tag': button.name,
                        'id': button.get('id'),
                        'class': button.get('class'),
                        'type': button.get('type'),
                        'aria_label': button.get('aria-label'),
                        'role': button.get('role')
                    })
            
            # Collect links
            links = soup.find_all('a')
            for link in links:
                if link.text and link.text.strip():
                    collected['links'].append({
                        'text': link.text.strip(),
                        'href': link.get('href'),
                        'id': link.get('id'),
                        'class': link.get('class'),
                        'aria_label': link.get('aria-label')
                    })
            
            # Collect inputs
            inputs = soup.find_all(['input', 'textarea', 'select'])
            for input_elem in inputs:
                collected['inputs'].append({
                    'type': input_elem.get('type', 'text'),
                    'name': input_elem.get('name'),
                    'id': input_elem.get('id'),
                    'class': input_elem.get('class'),
                    'placeholder': input_elem.get('placeholder'),
                    'value': input_elem.get('value')
                })
            
            # Collect text elements
            text_elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'])
            for elem in text_elements:
                if elem.text and elem.text.strip() and len(elem.text.strip()) > 3:
                    collected['text_elements'].append({
                        'text': elem.text.strip(),
                        'tag': elem.name,
                        'id': elem.get('id'),
                        'class': elem.get('class')
                    })
            
            # Collect images
            images = soup.find_all('img')
            for img in images:
                collected['images'].append({
                    'alt': img.get('alt'),
                    'src': img.get('src'),
                    'id': img.get('id'),
                    'class': img.get('class'),
                    'title': img.get('title')
                })
            
            # Store collected locators
            self.collected_locators = collected
            print(f"✅ Collected {len(collected['buttons'])} buttons, {len(collected['links'])} links, {len(collected['inputs'])} inputs, {len(collected['text_elements'])} text elements")
            
            return collected
            
        except Exception as e:
            print(f"❌ Error collecting locators: {e}")
            return {}

    async def find_element_by_context(self, page: Page, target_text: str, context_type: str = None):
        """Find element based on context and collected locators."""
        try:
            # First, collect all locators if not already done
            if not self.collected_locators:
                await self.collect_all_locators(page)
            
            # Determine context type if not provided
            if not context_type:
                context_type = self._determine_context_type(target_text)
            
            print(f"🎯 Context-aware search: '{target_text}' (context: {context_type})")
            
            # Search based on context
            if context_type == 'faq':
                return await self._find_faq_element(page, target_text)
            elif context_type == 'search':
                return await self._find_search_element(page, target_text)
            elif context_type == 'navigation':
                return await self._find_navigation_element(page, target_text)
            elif context_type == 'form':
                return await self._find_form_element(page, target_text)
            elif context_type == 'content':
                return await self._find_content_element(page, target_text)
            elif context_type == 'service':
                return await self._find_service_element(page, target_text)
            else:
                return await self._find_generic_element(page, target_text)
                
        except Exception as e:
            print(f"❌ Error in context-aware search: {e}")
            return None

    def _determine_context_type(self, target_text: str) -> str:
        """Determine the context type based on target text."""
        target_lower = target_text.lower()
        
        for context, patterns in self.context_aware_patterns.items():
            for pattern in patterns:
                if pattern in target_lower:
                    return context
        
        # Default to content if no specific context found
        return 'content'

    async def _find_faq_element(self, page: Page, target_text: str):
        """Find FAQ-related elements with specialized strategies."""
        strategies = [
            # Strategy 1: Direct FAQ button selectors
            lambda: page.query_selector(f'button:has-text("{target_text}")'),
            lambda: page.query_selector(f'[role="button"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'[aria-expanded]:has-text("{target_text}")'),
            
            # Strategy 2: FAQ accordion patterns
            lambda: page.query_selector(f':has-text("{target_text}") ~ button'),
            lambda: page.query_selector(f':has-text("{target_text}") + button'),
            lambda: page.query_selector(f':has-text("{target_text}") ~ [role="button"]'),
            
            # Strategy 3: FAQ-specific classes
            lambda: page.query_selector('button.accordion'),
            lambda: page.query_selector('button.faq'),
            lambda: page.query_selector('[data-faq]'),
            
            # Strategy 4: Icon-based FAQ buttons
            lambda: page.query_selector(f':has-text("{target_text}") ~ button svg'),
            lambda: page.query_selector(f':has-text("{target_text}") ~ button i'),
        ]
        
        return await self._try_strategies(strategies, "FAQ element")

    async def _find_search_element(self, page: Page, target_text: str):
        """Find search-related elements with specialized strategies."""
        if 'input' in target_text.lower() or 'search' in target_text.lower():
            # Search input
            strategies = [
                lambda: page.query_selector('input[type="search"]'),
                lambda: page.query_selector('input[name="q"]'),
                lambda: page.query_selector('input[name="search"]'),
                lambda: page.query_selector('input[placeholder*="search" i]'),
                lambda: page.query_selector('input[placeholder*="cari" i]'),
                lambda: page.query_selector('input[type="text"]'),
                lambda: page.query_selector('.search-input'),
                lambda: page.query_selector('#search'),
            ]
            return await self._try_strategies(strategies, "search input")
        else:
            # Search button
            strategies = [
                lambda: page.query_selector(f'button:has-text("{target_text}")'),
                lambda: page.query_selector(f'[role="button"]:has-text("{target_text}")'),
                lambda: page.query_selector('button[type="submit"]'),
                lambda: page.query_selector('.search-button'),
                lambda: page.query_selector('button:has(svg)'),
            ]
            return await self._try_strategies(strategies, "search button")

    async def _find_navigation_element(self, page: Page, target_text: str):
        """Find navigation elements with specialized strategies."""
        strategies = [
            # Strategy 1: Direct text matching
            lambda: page.query_selector(f'a:has-text("{target_text}")'),
            lambda: page.query_selector(f'button:has-text("{target_text}")'),
            lambda: page.query_selector(f'[role="button"]:has-text("{target_text}")'),

            # Strategy 2: Navigation-specific selectors
            lambda: page.query_selector(f'nav a:has-text("{target_text}")'),
            lambda: page.query_selector(f'.nav a:has-text("{target_text}")'),
            lambda: page.query_selector(f'.navigation a:has-text("{target_text}")'),

            # Strategy 3: Menu patterns
            lambda: page.query_selector(f'.menu a:has-text("{target_text}")'),
            lambda: page.query_selector(f'[role="menuitem"]:has-text("{target_text}")'),

            # Strategy 4: Card-based navigation (common in modern UIs)
            lambda: page.query_selector(f'.card:has-text("{target_text}")'),
            lambda: page.query_selector(f'.item:has-text("{target_text}")'),
            lambda: page.query_selector(f'.tile:has-text("{target_text}")'),
            lambda: page.query_selector(f'.box:has-text("{target_text}")'),
            lambda: page.query_selector(f'div[onclick]:has-text("{target_text}")'),
            lambda: page.query_selector(f'div[role="button"]:has-text("{target_text}")'),

            # Strategy 5: Section-based navigation (for "ruang" elements)
            lambda: page.query_selector(f'section:has-text("{target_text}")'),
            lambda: page.query_selector(f'article:has-text("{target_text}")'),
            lambda: page.query_selector(f'div[class*="section"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'div[class*="room"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'div[class*="ruang"]:has-text("{target_text}")'),

            # Strategy 6: Heading-based navigation
            lambda: page.query_selector(f'h1:has-text("{target_text}")'),
            lambda: page.query_selector(f'h2:has-text("{target_text}")'),
            lambda: page.query_selector(f'h3:has-text("{target_text}")'),
            lambda: page.query_selector(f'h4:has-text("{target_text}")'),
            lambda: page.query_selector(f'h5:has-text("{target_text}")'),
            lambda: page.query_selector(f'h6:has-text("{target_text}")'),

            # Strategy 7: Link patterns
            lambda: page.query_selector(f'a[href*="{target_text.lower().replace(" ", "-")}"]'),
            lambda: page.query_selector(f'[href]:has-text("{target_text}")'),
            lambda: page.query_selector(f'[onclick]:has-text("{target_text}")'),

            # Strategy 8: Comprehensive element search
            lambda: page.query_selector(f'div:has-text("{target_text}")'),
            lambda: page.query_selector(f'span:has-text("{target_text}")'),
            lambda: page.query_selector(f'p:has-text("{target_text}")'),
            lambda: page.query_selector(f'li:has-text("{target_text}")'),
        ]

        return await self._try_strategies(strategies, "navigation element")

    async def _find_form_element(self, page: Page, target_text: str):
        """Find form elements with specialized strategies."""
        strategies = [
            # Strategy 1: Input fields
            lambda: page.query_selector(f'input[name*="{target_text.lower().replace(" ", "_")}"]'),
            lambda: page.query_selector(f'input[placeholder*="{target_text}"]'),
            lambda: page.query_selector(f'label:has-text("{target_text}") + input'),
            lambda: page.query_selector(f'label:has-text("{target_text}") ~ input'),
            
            # Strategy 2: Form buttons
            lambda: page.query_selector(f'button:has-text("{target_text}")'),
            lambda: page.query_selector(f'input[type="submit"][value*="{target_text}"]'),
            
            # Strategy 3: Form-specific patterns
            lambda: page.query_selector(f'form button:has-text("{target_text}")'),
            lambda: page.query_selector(f'.form button:has-text("{target_text}")'),
        ]
        
        return await self._try_strategies(strategies, "form element")

    async def _find_content_element(self, page: Page, target_text: str):
        """Find content elements with specialized strategies."""
        strategies = [
            # Strategy 1: Direct text matching
            lambda: page.query_selector(f':has-text("{target_text}")'),
            lambda: page.query_selector(f'text="{target_text}"'),
            
            # Strategy 2: Heading elements
            lambda: page.query_selector(f'h1:has-text("{target_text}")'),
            lambda: page.query_selector(f'h2:has-text("{target_text}")'),
            lambda: page.query_selector(f'h3:has-text("{target_text}")'),
            
            # Strategy 3: Paragraph and span elements
            lambda: page.query_selector(f'p:has-text("{target_text}")'),
            lambda: page.query_selector(f'span:has-text("{target_text}")'),
            lambda: page.query_selector(f'div:has-text("{target_text}")'),
            
            # Strategy 4: Link elements
            lambda: page.query_selector(f'a:has-text("{target_text}")'),
        ]
        
        return await self._try_strategies(strategies, "content element")

    async def _find_generic_element(self, page: Page, target_text: str):
        """Find generic elements with fallback strategies."""
        strategies = [
            # Strategy 1: All clickable elements
            lambda: page.query_selector(f'button:has-text("{target_text}")'),
            lambda: page.query_selector(f'a:has-text("{target_text}")'),
            lambda: page.query_selector(f'[role="button"]:has-text("{target_text}")'),

            # Strategy 2: Enhanced text elements (more comprehensive)
            lambda: page.query_selector(f'div:has-text("{target_text}")'),
            lambda: page.query_selector(f'span:has-text("{target_text}")'),
            lambda: page.query_selector(f'h1:has-text("{target_text}")'),
            lambda: page.query_selector(f'h2:has-text("{target_text}")'),
            lambda: page.query_selector(f'h3:has-text("{target_text}")'),
            lambda: page.query_selector(f'h4:has-text("{target_text}")'),
            lambda: page.query_selector(f'h5:has-text("{target_text}")'),
            lambda: page.query_selector(f'h6:has-text("{target_text}")'),
            lambda: page.query_selector(f'p:has-text("{target_text}")'),
            lambda: page.query_selector(f'li:has-text("{target_text}")'),
            lambda: page.query_selector(f'.card:has-text("{target_text}")'),
            lambda: page.query_selector(f'.item:has-text("{target_text}")'),
            lambda: page.query_selector(f'.menu-item:has-text("{target_text}")'),
            lambda: page.query_selector(f'.nav-item:has-text("{target_text}")'),
            lambda: page.query_selector(f'[onclick]:has-text("{target_text}")'),
            lambda: page.query_selector(f'[href]:has-text("{target_text}")'),
            lambda: page.query_selector(f':has-text("{target_text}")'),
            lambda: page.query_selector(f'text="{target_text}"'),

            # Strategy 3: Input elements
            lambda: page.query_selector(f'input[placeholder*="{target_text}"]'),
            lambda: page.query_selector(f'input[value*="{target_text}"]'),

            # Strategy 4: Partial matching with attributes
            lambda: page.query_selector(f'[aria-label*="{target_text}" i]'),
            lambda: page.query_selector(f'[title*="{target_text}" i]'),
            lambda: page.query_selector(f'[data-testid*="{target_text.lower().replace(" ", "-")}" i]'),
            lambda: page.query_selector(f'[class*="{target_text.lower().replace(" ", "-")}" i]'),
            lambda: page.query_selector(f'[id*="{target_text.lower().replace(" ", "-")}" i]'),
        ]

        return await self._try_strategies(strategies, "generic element")

    async def _find_service_element(self, page: Page, target_text: str):
        """Find service elements with specialized strategies for Ruang Pendidikan services."""
        strategies = [
            # Strategy 1: Direct text matching for service names
            lambda: page.query_selector(f'a:has-text("{target_text}")'),
            lambda: page.query_selector(f'button:has-text("{target_text}")'),
            lambda: page.query_selector(f'[role="button"]:has-text("{target_text}")'),

            # Strategy 2: Service card patterns
            lambda: page.query_selector(f'.service:has-text("{target_text}")'),
            lambda: page.query_selector(f'.card:has-text("{target_text}")'),
            lambda: page.query_selector(f'.item:has-text("{target_text}")'),
            lambda: page.query_selector(f'[class*="service"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'[class*="card"]:has-text("{target_text}")'),

            # Strategy 3: Ruang-specific patterns
            lambda: page.query_selector(f'[class*="ruang"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'a[href*="ruang"]:has-text("{target_text}")'),
            lambda: page.query_selector(f'a[href*="pendidikan"]:has-text("{target_text}")'),

            # Strategy 4: Link patterns for specific services
            lambda: page.query_selector(f'a[href*="akun"]:has-text("{target_text}")') if 'akun' in target_text.lower() else None,
            lambda: page.query_selector(f'a[href*="rapor"]:has-text("{target_text}")') if 'rapor' in target_text.lower() else None,
            lambda: page.query_selector(f'a[href*="neraca"]:has-text("{target_text}")') if 'neraca' in target_text.lower() else None,
            lambda: page.query_selector(f'a[href*="manajemen"]:has-text("{target_text}")') if 'manajemen' in target_text.lower() else None,

            # Strategy 5: Generic clickable elements containing the text
            lambda: page.query_selector(f'[onclick]:has-text("{target_text}")'),
            lambda: page.query_selector(f'[tabindex]:has-text("{target_text}")'),
        ]

        # Filter out None strategies
        strategies = [s for s in strategies if s is not None]

        return await self._try_strategies(strategies, "service element")

    async def _try_strategies(self, strategies, element_type):
        """Try multiple strategies to find an element."""
        for i, strategy in enumerate(strategies):
            try:
                element = await strategy()
                if element and await element.is_visible():
                    selector = await self._get_element_selector(element)
                    print(f"✅ Found {element_type} using strategy {i+1}: {selector}")
                    return {
                        'type': element_type,
                        'selector': selector,
                        'confidence': 0.9 - (i * 0.05),
                        'strategy': f'strategy_{i+1}'
                    }
            except Exception as e:
                print(f"Strategy {i+1} failed: {e}")
                continue
        
        print(f"❌ No {element_type} found with any strategy")
        return None

    async def find_search_input(self, page: Page, target_text: str = None):
        """Find search input with multiple strategies."""
        return await self.find_element_by_context(page, target_text or "search", "search")

    async def find_search_button(self, page: Page, target_text: str = "Cari"):
        """Find search button with multiple strategies."""
        return await self.find_element_by_context(page, target_text, "search")

    async def find_text_element(self, page: Page, target_text: str):
        """Find text element with multiple strategies."""
        return await self.find_element_by_context(page, target_text, "content")

    async def find_clickable_element(self, page: Page, target_text: str):
        """Find clickable element with multiple strategies."""
        return await self.find_element_by_context(page, target_text, "navigation")

    async def _fuzzy_text_search(self, page: Page, target_text: str):
        """Advanced fuzzy text search with multiple fallback strategies."""
        try:
            # Get page content
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            # Strategy 1: Find elements containing similar text
            similar_elements = soup.find_all(text=re.compile(re.escape(target_text), re.IGNORECASE))
            if similar_elements:
                element = similar_elements[0].parent
                return await page.query_selector(f':has-text("{element.get_text().strip()}")')
            
            # Strategy 2: Find elements with partial text match
            words = target_text.split()
            for word in words:
                if len(word) > 3:  # Only search for words longer than 3 characters
                    element = soup.find(text=re.compile(re.escape(word), re.IGNORECASE))
                    if element:
                        return await page.query_selector(f':has-text("{element.strip()}")')
            
            # Strategy 3: Look for elements with similar attributes
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div', 'a']):
                if element.get_text().strip() and any(word.lower() in element.get_text().lower() for word in words):
                    return await page.query_selector(f':has-text("{element.get_text().strip()}")')
                    
        except Exception as e:
            print(f"Fuzzy text search failed: {e}")
        
        return None

    async def _advanced_clickable_search(self, page: Page, target_text: str):
        """Advanced clickable element search with multiple strategies."""
        try:
            # Strategy 1: Look for elements with similar text in clickable containers
            elements = await page.query_selector_all('button, a, [role="button"], [onclick]')
            
            for element in elements:
                try:
                    text = await element.text_content()
                    if text and target_text.lower() in text.lower():
                        if await element.is_visible():
                            return element
                except:
                    continue
            
            # Strategy 2: Look for elements with similar aria-label or title
            for element in elements:
                try:
                    aria_label = await element.get_attribute('aria-label')
                    title = await element.get_attribute('title')
                    
                    if (aria_label and target_text.lower() in aria_label.lower()) or \
                       (title and target_text.lower() in title.lower()):
                        if await element.is_visible():
                            return element
                except:
                    continue
                    
        except Exception as e:
            print(f"Advanced clickable search failed: {e}")
        
        return None

    async def _wait_and_find_search_input(self, page: Page):
        """Wait for dynamic content and find search input."""
        try:
            # Wait for page to be ready
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # Try to find search input after waiting
            return await page.query_selector('input[type="text"]:not([readonly])')
        except:
            return None
    
    async def _wait_and_find_search_button(self, page: Page, target_text: str):
        """Wait for dynamic content and find search button."""
        try:
            # Wait for page to be ready
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # Try to find search button after waiting
            return await page.query_selector(f'button:has-text("{target_text}")')
        except:
            return None
    
    async def _wait_and_find_text_element(self, page: Page, target_text: str):
        """Wait for dynamic content and find text element."""
        try:
            # Wait for page to be ready
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # Try to find text element after waiting
            return await page.query_selector(f':has-text("{target_text}")')
        except:
            return None
    
    async def _wait_and_find_clickable_element(self, page: Page, target_text: str):
        """Wait for dynamic content and find clickable element."""
        try:
            # Wait for page to be ready
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # Try to find clickable element after waiting
            return await page.query_selector(f'button:has-text("{target_text}")')
        except:
            return None
    
    async def _get_element_selector(self, element):
        """Generate a simple and reliable selector for an element."""
        try:
            # Strategy 1: Try to get a unique ID first (most reliable)
            element_id = await element.get_attribute('id')
            if element_id and element_id.strip():
                return f"#{element_id}"
            
            # Strategy 2: Try to get a simple class name (avoid complex CSS)
            element_class = await element.get_attribute('class')
            if element_class and element_class.strip():
                # Split classes and take the first meaningful one
                classes = element_class.split()
                for cls in classes:
                    # Avoid complex CSS classes with special characters
                    if cls and not any(char in cls for char in ['[', ']', '!', ':', '(', ')', 'var', '--']):
                        return f".{cls}"
            
            # Strategy 3: Try to get name attribute
            element_name = await element.get_attribute('name')
            if element_name and element_name.strip():
                tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                return f"{tag_name}[name='{element_name}']"
            
            # Strategy 4: Try to get type attribute
            element_type = await element.get_attribute('type')
            if element_type and element_type.strip():
                tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                return f"{tag_name}[type='{element_type}']"
            
            # Strategy 5: Try to get role attribute
            element_role = await element.get_attribute('role')
            if element_role and element_role.strip():
                tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                return f"{tag_name}[role='{element_role}']"
            
            # Strategy 6: Use text content for buttons/links
            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
            if tag_name in ['button', 'a', 'span', 'div']:
                text_content = await element.text_content()
                if text_content and text_content.strip():
                    # Use a simple text-based selector
                    return f"{tag_name}:has-text('{text_content.strip()}')"
            
            # Strategy 7: Fallback to tag name only
            return tag_name
            
        except Exception as e:
            print(f"Error generating selector: {e}")
            return "button"  # Safe fallback
    
    async def smart_find_locator(self, page: Page, action_type: str, target: str, value: str = None):
        """AI-like smart locator finder that analyzes failures and adapts strategies."""
        print(f"🔍 AI Smart finding locator for: {action_type} '{target}'")
        
        # Reset retry count for new search
        self.retry_count = 0
        failure_reasons = []
        tried_strategies = []
        
        # Try multiple times with intelligent adaptation
        while self.retry_count < self.max_retries:
            try:
                # Analyze previous failures and adapt strategy
                if failure_reasons:
                    print(f"🧠 AI Analysis: Previous failures: {failure_reasons}")
                    adapted_strategy = self._adapt_strategy_based_on_failures(failure_reasons, action_type, target)
                    print(f"🧠 AI Adaptation: Using adapted strategy: {adapted_strategy}")
                
                # Use context-aware element finding
                result = await self.find_element_by_context(page, target, self._determine_context_type(target))
                
                if result:
                    # Generate intelligent fallback selectors based on context
                    result['all_selectors'] = await self._generate_intelligent_fallbacks(page, action_type, target, result, failure_reasons)
                    result['ai_analysis'] = {
                        'failures_analyzed': failure_reasons,
                        'strategies_tried': tried_strategies,
                        'adaptation_used': len(failure_reasons) > 0
                    }
                    return result
                
                # If not found, analyze why and adapt
                self.retry_count += 1
                if self.retry_count < self.max_retries:
                    failure_analysis = await self._analyze_failure_reason(page, action_type, target)
                    failure_reasons.append(failure_analysis)
                    tried_strategies.append(f"attempt_{self.retry_count}")
                    
                    print(f"🧠 AI Learning: Failure reason: {failure_analysis}")
                    print(f"🔄 AI Retrying... Attempt {self.retry_count + 1}/{self.max_retries}")
                    await asyncio.sleep(1)
                    
            except Exception as e:
                print(f"🧠 AI Error Analysis: {e}")
                failure_reasons.append(f"Exception: {str(e)}")
                self.retry_count += 1
                if self.retry_count < self.max_retries:
                    await asyncio.sleep(1)
        
        print(f"❌ AI Smart locator finder could not find element for {action_type} '{target}'")
        print(f"🧠 AI Final Analysis: All failures: {failure_reasons}")
        return None

    def _adapt_strategy_based_on_failures(self, failure_reasons, action_type, target):
        """AI-like strategy adaptation based on failure analysis."""
        adaptations = []
        
        for failure in failure_reasons:
            if "Timeout" in failure:
                adaptations.append("timeout_optimization")
            elif "not visible" in failure.lower():
                adaptations.append("visibility_wait")
            elif "complex css" in failure.lower() or "!" in failure:
                adaptations.append("simple_selectors_only")
            elif "multiple elements" in failure.lower():
                adaptations.append("specific_targeting")
            elif "not found" in failure.lower():
                adaptations.append("broader_search")
        
        return adaptations

    async def _analyze_failure_reason(self, page, action_type, target):
        """AI-like failure analysis to understand why locator finding failed."""
        try:
            # Analyze page structure
            page_content = await page.content()
            
            # Check if target text exists on page
            if target.lower() in page_content.lower():
                return "text_exists_but_selector_failed"
            
            # Check for complex CSS classes
            if "focus:!outline-none" in page_content or "var(--" in page_content:
                return "complex_css_detected"
            
            # Check for dynamic content
            if "data-state" in page_content or "aria-expanded" in page_content:
                return "dynamic_content_detected"
            
            # Check for multiple similar elements
            elements = await page.query_selector_all(f'*:has-text("{target}")')
            if len(elements) > 1:
                return "multiple_elements_found"
            
            return "element_not_found"
            
        except Exception as e:
            return f"analysis_error: {str(e)}"

    async def _generate_intelligent_fallbacks(self, page, action_type, target, primary_result, failure_reasons):
        """Generate intelligent fallback selectors based on failure analysis."""
        fallback_selectors = []
        
        # Analyze failures to determine best fallback strategy
        use_simple_selectors = any("complex_css" in reason for reason in failure_reasons)
        use_text_based = any("text_exists" in reason for reason in failure_reasons)
        use_wait_strategy = any("dynamic" in reason for reason in failure_reasons)
        
        try:
            if action_type == 'click' and ('search' in target.lower() or 'cari' in target.lower()):
                if use_simple_selectors:
                    # Use only simple, reliable selectors
                    fallback_selectors = [
                        f'button:has-text("{target}")',
                        f'[role="button"]:has-text("{target}")',
                        'button[type="submit"]',
                        'button.btn',
                        'button.button'
                    ]
                elif use_text_based:
                    # Prioritize text-based selectors
                    fallback_selectors = [
                        f'button:has-text("{target}")',
                        f'[role="button"]:has-text("{target}")',
                        f'a:has-text("{target}")',
                        f'*:has-text("{target}")'
                    ]
                else:
                    # Standard fallback strategy
                    fallback_selectors = [
                        f'button:has-text("{target}")',
                        f'[role="button"]:has-text("{target}")',
                        f'a:has-text("{target}")',
                        'button[type="submit"]',
                        'button.btn',
                        'button.button',
                        'button:has(svg)',
                        'button.inline-flex'
                    ]
            elif action_type == 'type':
                fallback_selectors = [
                    'input[type="text"]',
                    'input[type="search"]',
                    'input[name="q"]',
                    'input[name="search"]',
                    'input.form-control',
                    '.form-control',
                    'input[placeholder*="search" i]',
                    'input[placeholder*="cari" i]'
                ]
            elif action_type == 'assert':
                fallback_selectors = [
                    f':has-text("{target}")',
                    f'*:has-text("{target}")',
                    'html',
                    'body'
                ]
            else:
                fallback_selectors = [
                    f'button:has-text("{target}")',
                    f'a:has-text("{target}")',
                    f'[role="button"]:has-text("{target}")',
                    'button',
                    'a',
                    '[role="button"]'
                ]
            
            # Filter out problematic selectors based on failure analysis
            safe_selectors = []
            for selector in fallback_selectors:
                if use_simple_selectors:
                    # Avoid complex CSS selectors
                    if not any(char in selector for char in ['[', ']', '!', ':', '(', ')', 'var', '--']):
                        safe_selectors.append(selector)
                else:
                    safe_selectors.append(selector)
            
            return safe_selectors[:5]  # Limit to 5 most reliable selectors
            
        except Exception as e:
            print(f"Error generating intelligent fallbacks: {e}")
            return []

    async def _generate_fallback_selectors(self, page: Page, action_type: str, target: str, primary_result: dict):
        """Generate multiple simple fallback selectors for better reliability."""
        fallback_selectors = []
        
        try:
            if action_type == 'click' and ('search' in target.lower() or 'cari' in target.lower()):
                # For search buttons, generate simple text-based selectors
                fallback_selectors = [
                    f'button:has-text("{target}")',
                    f'[role="button"]:has-text("{target}")',
                    f'a:has-text("{target}")',
                    'button[type="submit"]',
                    'button.btn',
                    'button.button',
                    '.btn',
                    '.button',
                    'button:has(svg)',
                    'button.inline-flex',
                    'button.flex',
                    'button.items-center'
                ]
            elif action_type == 'type':
                # For input fields, generate simple input selectors
                fallback_selectors = [
                    'input[type="text"]',
                    'input[type="search"]',
                    'input[name="q"]',
                    'input[name="search"]',
                    'input.form-control',
                    '.form-control',
                    'input[placeholder*="search" i]',
                    'input[placeholder*="cari" i]'
                ]
            elif action_type == 'assert':
                # For text assertions, generate simple text selectors
                fallback_selectors = [
                    f':has-text("{target}")',
                    f'*:has-text("{target}")',
                    'html',
                    'body'
                ]
            else:
                # Generic fallback selectors
                fallback_selectors = [
                    f'button:has-text("{target}")',
                    f'a:has-text("{target}")',
                    f'[role="button"]:has-text("{target}")',
                    'button',
                    'a',
                    '[role="button"]'
                ]
            
            # Filter out any selectors that might cause issues
            safe_selectors = []
            for selector in fallback_selectors:
                if not any(char in selector for char in ['[', ']', '!', ':', '(', ')', 'var', '--']):
                    safe_selectors.append(selector)
            
            return safe_selectors[:5]  # Limit to 5 most reliable selectors
            
        except Exception as e:
            print(f"Error generating fallback selectors: {e}")
            return [] 