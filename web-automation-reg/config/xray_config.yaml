# Xray Integration Configuration
# Configuration for Jira Xray test management integration

xray:
  # Enable/disable Xray integration
  enabled: true
  
  # Jira instance configuration
  base_url: "https://wartek.atlassian.net"
  
  # Project configuration
  project_key: "RP"  # R<PERSON>h <PERSON> project key
  
  # Test execution configuration
  # These can be overridden by environment variables
  test_plan_key: null  # Set via XRAY_TEST_PLAN_KEY env var
  test_execution_key: null  # Set via XRAY_TEST_EXECUTION_KEY env var
  
  # Environment mapping
  environments:
    staging: "STAGING"
    production: "PRODUCTION"
  
  # Status mapping from test framework to Xray
  status_mapping:
    passed: "PASS"
    failed: "FAIL"
    skipped: "TODO"
    error: "FAIL"
  
  # Retry configuration
  retry:
    enabled: true
    max_attempts: 3
    delay_seconds: 2
  
  # Batch processing
  batch:
    enabled: true
    max_batch_size: 50
    timeout_seconds: 30

# Authentication configuration
# These should be set via environment variables for security:
# XRAY_USERNAME - Jira username or email
# XRAY_PASSWORD - Jira password or API token
# XRAY_ENABLED - Enable/disable integration (true/false)
# XRAY_BASE_URL - Override base URL if needed
# XRAY_PROJECT_KEY - Override project key if needed
# XRAY_TEST_PLAN_KEY - Test plan key for linking executions
# XRAY_TEST_EXECUTION_KEY - Specific test execution key to update

# Example environment variables:
# export XRAY_USERNAME="<EMAIL>"
# export XRAY_PASSWORD="your-api-token"
# export XRAY_ENABLED="true"
# export XRAY_PROJECT_KEY="RP"
