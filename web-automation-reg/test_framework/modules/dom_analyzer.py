import re
from bs4 import BeautifulSoup

class DOMAnalyzer:
    """Analyzes DOM structure to find elements and extract information."""
    
    def __init__(self):
        """Initialize the DOM analyzer."""
        self.bs4_available = True
        try:
            from bs4 import BeautifulSoup
            self.BeautifulSoup = BeautifulSoup
        except ImportError:
            self.bs4_available = False
            print("Warning: BeautifulSoup not available. DOM analysis will be limited.")
    
    def find_search_elements(self, html_content):
        """Find search-related elements in the DOM.
        
        Args:
            html_content: HTML content of the page
            
        Returns:
            dict: Dictionary with search box and button selectors
        """
        if not self.bs4_available:
            return None
        
        if not html_content:
            print("Warning: Empty HTML content provided to find_search_elements")
            return None
        
        soup = self.BeautifulSoup(html_content, 'html.parser')
        result = {}
        
        # Find search input
        search_input = None
        search_input_candidates = [
            soup.find('input', {'id': lambda x: x and 'search' in x.lower()}),
            soup.find('input', {'name': lambda x: x and 'search' in x.lower()}),
            soup.find('input', {'placeholder': lambda x: x and ('search' in x.lower() or 'cari' in x.lower())}),
            soup.find('input', {'type': 'search'}),
            soup.find('input', {'class': lambda x: x and isinstance(x, list) and any('search' in c.lower() for c in x)})
        ]
        
        for candidate in search_input_candidates:
            if candidate:
                search_input = candidate
                break
                
        if search_input:
            if search_input.get('id'):
                result['search_input'] = {'selector': f"#{search_input['id']}"}
            elif search_input.get('name'):
                result['search_input'] = {'selector': f"input[name='{search_input['name']}']"}
            else:
                # Create a more complex selector
                selector_parts = []
                if search_input.get('class') and isinstance(search_input.get('class'), list):
                    selector_parts.append(f".{' .'.join(search_input['class'])}")
                if search_input.get('type'):
                    selector_parts.append(f"[type='{search_input['type']}']")
                if search_input.get('placeholder'):
                    selector_parts.append(f"[placeholder*='{search_input['placeholder']}']")
                
                if selector_parts:
                    result['search_input'] = {'selector': f"input{' '.join(selector_parts)}"}
        
        # Find search button
        search_button = None
        search_button_candidates = [
            soup.find('button', {'id': lambda x: x and ('search' in x.lower() or 'cari' in x.lower())}),
            soup.find('button', {'class': lambda x: x and isinstance(x, list) and any(('search' in c.lower() or 'cari' in c.lower()) for c in x)}),
            soup.find('button', text=lambda x: x and ('search' in x.lower() or 'cari' in x.lower())),
            soup.find('input', {'type': 'submit', 'value': lambda x: x and ('search' in x.lower() or 'cari' in x.lower())}),
            soup.find('a', {'class': lambda x: x and isinstance(x, list) and any(('search' in c.lower() or 'cari' in c.lower()) for c in x)})
        ]
        
        for candidate in search_button_candidates:
            if candidate:
                search_button = candidate
                break
                
        if search_button:
            if search_button.name == 'button' and search_button.get('id'):
                result['search_button'] = {'selector': f"#{search_button['id']}"}
            elif search_button.name == 'button' and search_button.get('class') and isinstance(search_button.get('class'), list):
                result['search_button'] = {'selector': f"button.{' .'.join(search_button['class'])}"}
            elif search_button.name == 'input':
                result['search_button'] = {'selector': f"input[type='submit'][value*='{search_button['value']}']"}
            elif search_button.name == 'a' and search_button.get('class') and isinstance(search_button.get('class'), list):
                result['search_button'] = {'selector': f"a.{' .'.join(search_button['class'])}"}
            else:
                # Try to create a selector based on text content
                if search_button.text and search_button.text.strip():
                    result['search_button'] = {'selector': f"button:text('{search_button.text.strip()}')"} 
        
        return result
    
    def find_best_locator(self, html_content, target_description):
        """Find the best locator for a given target description.
        
        Args:
            html_content: HTML content of the page
            target_description: Description of the target element
            
        Returns:
            dict: Dictionary with selector and confidence
        """
        if not self.bs4_available:
            return None
        
        if not html_content or not target_description:
            print("Warning: Empty HTML content or target description provided to find_best_locator")
            return None
        
        soup = self.BeautifulSoup(html_content, 'html.parser')
        target_lower = target_description.lower()
        
        # Check for buttons
        buttons = soup.find_all(['button', 'input', 'a'])
        for button in buttons:
            # Check button text
            button_text = button.text.strip().lower() if button.text else ""
            if button_text and (button_text == target_lower or target_lower in button_text):
                if button.get('id'):
                    return {'selector': f"#{button['id']}", 'confidence': 0.9}
                elif button.get('class') and isinstance(button.get('class'), list):
                    return {'selector': f"{button.name}.{' .'.join(button['class'])}", 'confidence': 0.8}
                else:
                    return {'selector': f"{button.name}:text('{button_text}')", 'confidence': 0.7}
            
            # Check button attributes
            for attr in ['value', 'placeholder', 'title', 'aria-label']:
                if button.get(attr) and target_lower in button.get(attr).lower():
                    return {'selector': f"{button.name}[{attr}*='{button.get(attr)}']", 'confidence': 0.8}
        
        # Check for form fields
        if "field" in target_lower or "input" in target_lower or "kolom" in target_lower:
            inputs = soup.find_all(['input', 'textarea', 'select'])
            for input_elem in inputs:
                for attr in ['id', 'name', 'placeholder', 'aria-label']:
                    if input_elem.get(attr) and target_lower in input_elem.get(attr).lower():
                        return {'selector': f"{input_elem.name}[{attr}*='{input_elem.get(attr)}']", 'confidence': 0.8}
        
        # Check for text elements
        if "text" in target_lower or "teks" in target_lower:
            text_elements = soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'div'])
            for elem in text_elements:
                if elem.text and target_lower in elem.text.lower():
                    if elem.get('id'):
                        return {'selector': f"#{elem['id']}", 'confidence': 0.9}
                    elif elem.get('class') and isinstance(elem.get('class'), list):
                        return {'selector': f"{elem.name}.{' .'.join(elem['class'])}", 'confidence': 0.7}
                    else:
                        return {'selector': f"{elem.name}:text('{elem.text.strip()}')", 'confidence': 0.6}
        
        # Fallback: try to find any element containing the target text
        elements = soup.find_all(text=lambda text: text and target_lower in text.lower())
        if elements:
            element = elements[0].parent
            if element.get('id'):
                return {'selector': f"#{element['id']}", 'confidence': 0.7}
            elif element.get('class') and isinstance(element.get('class'), list):
                return {'selector': f"{element.name}.{' .'.join(element['class'])}", 'confidence': 0.6}
            else:
                return {'selector': f"{element.name}:text('{target_lower}')", 'confidence': 0.5}
        
        return None

    def extract_all_locators(self, html_content):
        """Extract all potential locators from the page.
        
        Args:
            html_content: HTML content of the page
        
        Returns:
            dict: Dictionary of locators with their selectors and metadata
        """
        if not self.bs4_available:
            return {}
        
        if not html_content:
            print("Warning: Empty HTML content provided to extract_all_locators")
            return {}
        
        soup = self.BeautifulSoup(html_content, 'html.parser')
        locators = {}
        
        # Extract buttons
        buttons = soup.find_all(['button', 'input[type="button"]', 'input[type="submit"]', 'a.btn', 'a[role="button"]'])
        for i, button in enumerate(buttons):
            locator_id = f"button_{i}"
            selector = None
            confidence = 0.7
            button_text = button.text.strip() if button.text else ""
            
            if button.get('id'):
                selector = f"#{button['id']}"
                confidence = 0.9
            elif button.get('aria-label'):
                selector = f"{button.name}[aria-label='{button['aria-label']}']"
                confidence = 0.95  # Very high confidence for aria-label
            elif button.get('class') and isinstance(button.get('class'), list) and len(button.get('class')) > 0:
                # Filter out invalid CSS classes
                valid_classes = []
                for cls in button['class']:
                    if self._is_valid_css_class(cls):
                        valid_classes.append(cls)
                
                if valid_classes:
                    # Use the most specific valid class
                    best_class = max(valid_classes, key=len)
                    selector = f"{button.name}.{best_class}"
                    confidence = 0.8
            elif button_text:
                selector = f"{button.name}:has-text('{button_text}')"
                confidence = 0.7
            elif button.get('name'):
                selector = f"{button.name}[name='{button['name']}']"
                confidence = 0.8
                
            if selector:
                # For buttons with text, also add text-based selectors
                selectors = [selector]
                if button_text:
                    # Add text-based selectors for buttons
                    text_selectors = [
                        f"button:has-text('{button_text}')",
                        f"button[type='button']:has-text('{button_text}')"
                    ]
                    selectors.extend(text_selectors)
                
                # Use the best selector as primary, but store all options
                # Reorder selectors to prioritize text-based ones
                if button_text and len(selectors) > 1:
                    # Move text-based selectors to the front
                    text_selectors = [s for s in selectors if 'has-text' in s]
                    other_selectors = [s for s in selectors if 'has-text' not in s]
                    selectors = text_selectors + other_selectors
                
                primary_selector = selectors[0]
                
                locators[locator_id] = {
                    'type': 'button',
                    'selector': primary_selector,
                    'text': button_text,
                    'confidence': confidence,
                    'all_selectors': selectors  # Store all selector options
                }
        
        # Extract input fields
        inputs = soup.find_all(['input', 'textarea', 'select'])
        for i, input_elem in enumerate(inputs):
            locator_id = f"input_{i}"
            selector = None
            confidence = 0.7
            input_type = input_elem.get('type', 'text')
            
            if input_elem.get('id'):
                selector = f"#{input_elem['id']}"
                confidence = 0.9
            elif input_elem.get('name'):
                selector = f"{input_elem.name}[name='{input_elem['name']}']"
                confidence = 0.8
            elif input_elem.get('placeholder'):
                selector = f"{input_elem.name}[placeholder='{input_elem['placeholder']}']"
                confidence = 0.8
            elif input_elem.get('class') and isinstance(input_elem.get('class'), list) and len(input_elem.get('class')) > 0:
                # Filter out invalid CSS classes
                valid_classes = []
                for cls in input_elem['class']:
                    if self._is_valid_css_class(cls):
                        valid_classes.append(cls)
                
                if valid_classes:
                    # Use the most specific valid class
                    best_class = max(valid_classes, key=len)
                    selector = f"{input_elem.name}.{best_class}"
                    confidence = 0.7
                
            if selector:
                locators[locator_id] = {
                    'type': 'input',
                    'input_type': input_type,
                    'selector': selector,
                    'placeholder': input_elem.get('placeholder', ''),
                    'name': input_elem.get('name', ''),
                    'confidence': confidence
                }
        
        # Extract links
        links = soup.find_all('a')
        for i, link in enumerate(links):
            locator_id = f"link_{i}"
            selector = None
            confidence = 0.7
            link_text = link.text.strip() if link.text else ""
            
            if link.get('id'):
                selector = f"#{link['id']}"
                confidence = 0.9
            elif link.get('aria-label'):
                selector = f"a[aria-label='{link['aria-label']}']"
                confidence = 0.95  # Very high confidence for aria-label
            elif link.get('class') and isinstance(link.get('class'), list) and len(link.get('class')) > 0:
                # Filter out invalid CSS classes
                valid_classes = []
                for cls in link['class']:
                    if self._is_valid_css_class(cls):
                        valid_classes.append(cls)
                
                if valid_classes:
                    # Use the most specific valid class
                    best_class = max(valid_classes, key=len)
                    selector = f"a.{best_class}"
                    confidence = 0.8
            elif link_text:
                selector = f"a:has-text('{link_text}')"
                confidence = 0.7
            elif link.get('href'):
                selector = f"a[href='{link['href']}']"
                confidence = 0.6
                
            if selector:
                locators[locator_id] = {
                    'type': 'link',
                    'selector': selector,
                    'text': link_text,
                    'href': link.get('href', ''),
                    'confidence': confidence
                }
        
        # Extract search elements
        search_elements = self.find_search_elements(html_content)
        if search_elements:
            if 'search_input' in search_elements:
                locators['search_input'] = {
                    'type': 'input',
                    'input_type': 'search',
                    'selector': search_elements['search_input']['selector'],
                    'confidence': 0.9
                }
            if 'search_button' in search_elements:
                locators['search_button'] = {
                    'type': 'button',
                    'selector': search_elements['search_button']['selector'],
                    'text': 'Search',
                    'confidence': 0.9
                }
        
        # Extract text elements (headings, paragraphs)
        text_elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p'])
        for i, elem in enumerate(text_elements):
            locator_id = f"text_{i}"
            selector = None
            confidence = 0.6
            text = elem.text.strip() if elem.text else ""
            
            if not text:
                continue
                
            if elem.get('id'):
                selector = f"#{elem['id']}"
                confidence = 0.9
            elif elem.get('class') and isinstance(elem.get('class'), list) and len(elem.get('class')) > 0:
                # Filter out invalid CSS classes
                valid_classes = []
                for cls in elem['class']:
                    if self._is_valid_css_class(cls):
                        valid_classes.append(cls)
                
                if valid_classes:
                    # Use the most specific valid class
                    best_class = max(valid_classes, key=len)
                    selector = f"{elem.name}.{best_class}"
                    confidence = 0.7
            else:
                selector = f"{elem.name}:has-text('{text}')"
                confidence = 0.6
                
            if selector:
                # For text elements, also add text-based selectors
                selectors = [selector]
                if text:
                    # Add text-based selectors for better matching
                    text_selectors = [
                        f":has-text('{text}')",
                        f"text=\"{text}\""
                    ]
                    selectors.extend(text_selectors)
                
                # Use the best selector as primary, but store all options
                primary_selector = selectors[0]
                
                locators[locator_id] = {
                    'type': 'text',
                    'selector': primary_selector,
                    'text': text,
                    'confidence': confidence,
                    'all_selectors': selectors  # Store all selector options
                }
        
        return locators

    def find_element_by_text_content(self, html_content, target_text):
        """
        Find elements that contain the specified text with enhanced detection.
        
        Args:
            html_content: HTML content of the page
            target_text: Text to search for
        
        Returns:
            dict: Dictionary with selector and confidence
        """
        if not self.bs4_available:
            return None
        
        if not html_content or not target_text:
            return None
        
        soup = self.BeautifulSoup(html_content, 'html.parser')
        target_lower = target_text.lower()
        
        # Enhanced search strategies
        search_strategies = [
            # Strategy 1: Exact text match in button elements (highest priority for buttons)
            lambda: soup.find_all('button', string=lambda text: text and text.strip().lower() == target_lower),
            
            # Strategy 2: Search in aria-label attributes
            lambda: soup.find_all(attrs={'aria-label': lambda x: x and target_lower in x.lower()}),
            
            # Strategy 3: Exact text match in any element
            lambda: soup.find_all(string=lambda text: text and text.strip().lower() == target_lower),
            
            # Strategy 4: Partial text match in button elements specifically
            lambda: soup.find_all('button', string=lambda text: text and target_lower in text.strip().lower()),
            
            # Strategy 5: Partial text match in any element
            lambda: soup.find_all(string=lambda text: text and target_lower in text.strip().lower()),
            
            # Strategy 6: Search in title attributes
            lambda: soup.find_all(attrs={'title': lambda x: x and target_lower in x.lower()}),
            
            # Strategy 7: Search in data attributes
            lambda: soup.find_all(attrs={'data-testid': lambda x: x and target_lower in x.lower()}),
            
            # Strategy 8: Search in alt attributes for images
            lambda: soup.find_all('img', attrs={'alt': lambda x: x and target_lower in x.lower()}),
            
            # Strategy 9: Search for clickable elements containing images with matching alt text
            lambda: soup.find_all(['a', 'button', 'div'], attrs={'onclick': True, 'role': lambda x: x in ['button', 'link']}),
            
            # Strategy 10: Search for elements with click handlers or cursor pointer
            lambda: soup.find_all(['a', 'button', 'div'], attrs={'style': lambda x: x and 'cursor: pointer' in x.lower()}),
            
            # Strategy 11: Search for elements with specific classes that might be clickable
            lambda: soup.find_all(['a', 'button', 'div'], attrs={'class': lambda x: x and isinstance(x, list) and any('click' in c.lower() or 'button' in c.lower() or 'link' in c.lower() for c in x)})
        ]
        
        best_element = None
        best_confidence = 0
        best_selector = None
        best_text = None
        
        for strategy_idx, strategy in enumerate(search_strategies):
            try:
                elements = strategy()
                if elements:
                    for element in elements:
                        confidence = 0.9 - (strategy_idx * 0.1)  # Higher confidence for earlier strategies
                        
                        # Get the actual element (not just the text node)
                        if hasattr(element, 'parent'):
                            actual_element = element.parent
                        else:
                            actual_element = element
                        
                        # Special handling for image elements
                        if actual_element.name == 'img':
                            # Try to find the clickable parent container
                            clickable_parent = self._find_clickable_parent(actual_element)
                            if clickable_parent:
                                actual_element = clickable_parent
                                confidence += 0.1  # Boost confidence for clickable elements
                        
                        # Generate selector
                        selector = self._generate_robust_selector(actual_element, target_text)
                        
                        if selector and confidence > best_confidence:
                            best_element = actual_element
                            best_confidence = confidence
                            best_selector = selector
                            best_text = element.strip() if hasattr(element, 'strip') else str(element)
                            
            except Exception as e:
                continue
        
        if best_element and best_selector:
            # Validate the selector
            if self._is_valid_selector(best_selector):
                return {
                    'selector': best_selector,
                    'confidence': best_confidence,
                    'text': best_text,
                    'element_type': best_element.name if hasattr(best_element, 'name') else 'unknown'
                }
            else:
                # Try to generate a simpler, valid selector
                simple_selector = self._generate_simple_selector(best_element, target_text)
                if simple_selector:
                    return {
                        'selector': simple_selector,
                        'confidence': best_confidence * 0.8,  # Lower confidence for fallback
                        'text': best_text,
                        'element_type': best_element.name if hasattr(best_element, 'name') else 'unknown'
                    }
        
        return None
    
    def _generate_robust_selector(self, element, target_text):
        """
        Generate a robust CSS selector for an element.
        """
        if not element:
            return None
        selectors = []
        # Strategy 1: Use ID if available
        if element.get('id'):
            selectors.append(f"#{element['id']}")
        # Strategy 2: Use data-testid if available
        if element.get('data-testid'):
            selectors.append(f"[data-testid='{element['data-testid']}']")
        # Strategy 3: Use aria-label if available
        if element.get('aria-label'):
            selectors.append(f"{element.name}[aria-label='{element['aria-label']}']")
        # Strategy 4: Use type attribute for form elements
        if element.get('type'):
            element_type = element.get('type')
            if element.name in ['input', 'button']:
                selectors.append(f"{element.name}[type='{element_type}']")
        # Strategy 5: Use class-based selector (but be more specific)
        if element.get('class') and isinstance(element.get('class'), list):
            classes = element.get('class')
            stable_classes = [cls for cls in classes if self._is_valid_css_class(cls)]
            if stable_classes:
                best_class = max(stable_classes, key=len)
                selectors.append(f"{element.name}.{best_class}")
        # Strategy 6: Use text content with element type (for clickable elements)
        if element.text and element.text.strip():
            clean_text = element.text.strip()
            if len(clean_text) <= 50:
                if element.name == 'button' and clean_text:
                    selectors.append(f"button:has-text('{clean_text}')")
                    selectors.append(f"button[type='button']:has-text('{clean_text}')")
                elif element.name in ['a', 'div']:
                    selectors.append(f"{element.name}:has-text('{clean_text}')")
        # Strategy 7: Use data attributes if available
        data_attrs = ['data-testid', 'data-id', 'data-name', 'data-value']
        for attr in data_attrs:
            if element.get(attr):
                selectors.append(f"{element.name}[{attr}='{element[attr]}']")
        # Strategy 8: Use title attribute if available
        if element.get('title'):
            selectors.append(f"{element.name}[title='{element['title']}']")
        # Strategy 9: Use position-based selector as last resort
        if element.parent:
            siblings = element.parent.find_all(element.name, recursive=False)
            if len(siblings) > 1:
                index = list(siblings).index(element)
                selectors.append(f"{element.name}:nth-of-type({index + 1})")
        # Strategy 10: Use simple element type as fallback
        selectors.append(element.name)
        # Return all selectors (for executor to try all)
        return selectors if len(selectors) > 1 else selectors[0] if selectors else None
    
    def _find_clickable_parent(self, img_element):
        """
        Find the clickable parent container of an image element.
        
        Args:
            img_element: BeautifulSoup img element
        
        Returns:
            BeautifulSoup element: The clickable parent or None
        """
        if not img_element or img_element.name != 'img':
            return None
        
        # Look for clickable parents (up to 3 levels up)
        current = img_element.parent
        levels_checked = 0
        max_levels = 3
        
        while current and levels_checked < max_levels:
            # Check if this element is clickable
            if self._is_clickable_element(current):
                return current
            
            # Check if this element has clickable attributes
            if (current.get('onclick') or 
                current.get('role') in ['button', 'link'] or
                current.get('tabindex') or
                (current.get('style') and 'cursor: pointer' in current.get('style', '').lower()) or
                (current.get('class') and isinstance(current.get('class'), list) and 
                 any('click' in c.lower() or 'button' in c.lower() or 'link' in c.lower() for c in current.get('class', [])))):
                return current
            
            current = current.parent
            levels_checked += 1
        
        # If no clickable parent found, return the immediate parent
        return img_element.parent
    
    def _is_clickable_element(self, element):
        """
        Check if an element is clickable.
        
        Args:
            element: BeautifulSoup element
        
        Returns:
            bool: True if element is clickable
        """
        if not element:
            return False
        
        # Check element type
        if element.name in ['a', 'button', 'input']:
            return True
        
        # Check for clickable attributes
        clickable_attrs = ['onclick', 'onmousedown', 'onmouseup', 'onmouseover']
        for attr in clickable_attrs:
            if element.get(attr):
                return True
        
        # Check for role attribute
        if element.get('role') in ['button', 'link', 'tab']:
            return True
        
        # Check for cursor pointer style
        if element.get('style') and 'cursor: pointer' in element.get('style', '').lower():
            return True
        
        # Check for clickable classes
        if element.get('class') and isinstance(element.get('class'), list):
            clickable_classes = ['clickable', 'button', 'link', 'btn', 'card', 'tile']
            for cls in element.get('class', []):
                if any(clickable in cls.lower() for clickable in clickable_classes):
                    return True
        
        return False
    
    def _is_valid_selector(self, selector):
        """
        Check if a CSS selector is valid.
        
        Args:
            selector: CSS selector string
        
        Returns:
            bool: True if selector is valid
        """
        if not selector:
            return False
        
        # Check for common invalid patterns
        invalid_patterns = [
            'sm:', 'md:', 'lg:', 'xl:',  # Tailwind breakpoints
            'hover:', 'focus:', 'active:', 'disabled:',  # Tailwind states
            'group:', 'peer:', 'dark:', 'light:',  # Tailwind modifiers
            'motion:', 'print:', 'supports:',  # Tailwind utilities
            'data:', 'aria:', 'rtl:', 'ltr:',  # Tailwind utilities
            ':',  # Any pseudo-class (too broad)
        ]
        
        for pattern in invalid_patterns:
            if pattern in selector:
                return False
        
        # Check for basic validity
        if selector.startswith('.') and len(selector) == 1:
            return False
        
        if selector.startswith('#') and len(selector) == 1:
            return False
        
        return True
    
    def _is_valid_css_class(self, class_name):
        """
        Check if a CSS class name is valid for use in selectors.
        
        Args:
            class_name: CSS class name string
        
        Returns:
            bool: True if class name is valid for selectors
        """
        if not class_name or len(class_name) < 2:
            return False
        
        # Skip classes that start with invalid patterns
        invalid_patterns = [
            'sm:', 'md:', 'lg:', 'xl:',  # Tailwind breakpoints
            'hover:', 'focus:', 'active:', 'disabled:',  # Tailwind states
            'group:', 'peer:', 'dark:', 'light:',  # Tailwind modifiers
            'motion:', 'print:', 'supports:',  # Tailwind utilities
            'data:', 'aria:', 'rtl:', 'ltr:',  # Tailwind utilities
            ':',  # Any pseudo-class
        ]
        
        for pattern in invalid_patterns:
            if class_name.startswith(pattern):
                return False
        
        # Skip classes with CSS custom properties (square brackets)
        if '[' in class_name and ']' in class_name:
            return False
        
        # Skip classes with CSS variables (var(--...))
        if 'var(--' in class_name:
            return False
        
        # Skip classes with invalid characters for CSS selectors
        invalid_chars = ['[', ']', '(', ')', '{', '}', ':', ';', '"', "'", '\\']
        for char in invalid_chars:
            if char in class_name:
                return False
        
        # Skip classes that are too long (likely dynamic)
        if len(class_name) > 50:
            return False
        
        return True
    
    def _generate_simple_selector(self, element, target_text):
        """
        Generate a simple, valid selector as fallback.
        
        Args:
            element: BeautifulSoup element
            target_text: Target text for context
        
        Returns:
            str: Simple CSS selector
        """
        if not element:
            return None
        
        # Strategy 1: Use element type with text
        if element.text and element.text.strip():
            clean_text = element.text.strip()
            if len(clean_text) <= 20:
                return f"{element.name}:has-text('{clean_text}')"
        
        # Strategy 2: Use element type only
        return element.name
