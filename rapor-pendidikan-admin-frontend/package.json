{"version": "1.4.1", "name": "e2e-boilerplate", "description": "boilerplate for e2e tests", "scripts": {"test": "playwright test", "test:staging": "STAGING_ONLY=true playwright test --grep @staging", "test:production": "PRODUCTION_ONLY=true playwright test --grep @production", "test:visual": "playwright test --grep @visual", "test:ui": "npm run test -- --grep @setup && npx playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:snapshot": "npm run test:visual -- --update-snapshots", "auth:reset": "find .auth .auth-dev -type f -name \"*.json\" -delete ;npm run test -- -g @setup", "auth:reset:production": "find .auth .auth-dev -type f -name \"*.production.json\" -delete ;npx playwright test -g '(?=.*@setup)(?=.*@production)'", "auth:reset:staging": "find .auth .auth-dev -type f -name \"*.staging.json\" -delete ;npx playwright test -g '(?=.*@setup)(?=.*@staging)'", "docker": "docker run --rm --network host -v $(pwd):/work/ -w /work/ -e GITLAB_AUTH_TOKEN=$GITLAB_AUTH_TOKEN -it mcr.microsoft.com/playwright:v1.41.1-jammy /bin/bash", "test:utils": "tsx ./src/**/*.test.ts", "typecheck": "tsc --noEmit --project tsconfig.ci.json", "report": "playwright show-report", "lint": "eslint . --ext ts,js --fix --max-warnings=0", "format": "prettier --write .", "postinstall": "playwright install chromium && simple-git-hooks"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.41.1", "@total-typescript/ts-reset": "^0.4.2", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.202", "@types/node": "^20.11.5", "@types/shelljs": "^0.8.15", "@types/xml2js": "^0.4.14", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "app-root-path": "^3.1.0", "dayjs": "^1.11.10", "dotenv": "^16.3.2", "eslint": "^8.56.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-playwright": "^0.12.0", "eslint-plugin-prettier": "^4.2.1", "lint-staged": "^13.3.0", "prettier": "^2.8.8", "shelljs": "^0.8.5", "simple-git-hooks": "^2.9.0", "tsx": "^4.7.0", "typescript": "^5.3.3", "xml2js": "^0.5.0", "yargs": "^17.7.2", "znv": "^0.3.2"}, "lint-staged": {"*.{js,ts}": ["eslint --ext ts,js --fix --max-warnings=0"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@wartek-id/e2e-jira-labelling": "^1.0.0", "@wartek-id/toolbox-event-tracker": "^1.2.0", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "nanoid": "^3.3.7", "playwright": "^1.47.1", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "tiny-invariant": "^1.3.1", "ts-node": "^10.9.2", "zod": "^3.22.4"}, "volta": {"node": "18.16.1"}}