import { <PERSON><PERSON><PERSON>, <PERSON>, expect } from '@playwright/test'
import { chromium } from 'playwright-extra'
import stealth from 'puppeteer-extra-plugin-stealth'

import { PRODUCTION_LOGIN_URL, STAGING_LOGIN_URL } from '@/constants/global.constant'
import { USER_IAP, type User } from '@/constants/users.constant'
import { getAuthFile } from '@/helpers/global.helper'
import { ENVIRONMENT, PRODUCTION_BASE_URL, STAGING_BASE_URL } from '@/utils/env.util'
import { log } from '@/utils/playwright-config.util'

chromium.use(stealth())

export const STORAGE_STATE_IAP = '.auth/iap.json'

export async function googleLogin(page: Page, user: User, environment: typeof ENVIRONMENT = 'staging') {
  const BASE_URL = environment === 'staging' ? STAGING_BASE_URL : PRODUCTION_BASE_URL
  const LOGIN_URL = environment === 'staging' ? STAGING_LOGIN_URL : PRODUCTION_LOGIN_URL

  const LOGIN_SUCCESS_REDIRECT_URL = `${BASE_URL}**`
  const LOGIN_SUCCESS_VISIBLE_CONTENT = /Selamat datang di Dashboard Admin/i

  const loginButton = page.getByText('Masuk dengan belajar.id')

  await page.goto(LOGIN_URL)

  // remove style on body
  await page.evaluate(() => {
    document.body.removeAttribute('style')
  })

  const googlePopup = page.waitForEvent('popup', { timeout: 10_000 })
  await loginButton.click({ timeout: 5_000 })
  const googleLoginForm = await googlePopup

  // Wait until the page netowrk is idle. It will fix the list of accounts cannot be clicked.
  await googleLoginForm.waitForLoadState('networkidle')

  log(`[${environment}] 🔑 Logging in as ${user.email}`, environment)

  if (environment === 'staging') {
    let emailInputVisible = false
    while (!emailInputVisible) {
      const useAnotherAccount = googleLoginForm.getByRole('link', { name: 'Use another account' })
      if (await useAnotherAccount.isVisible()) {
        // eslint-disable-next-line playwright/no-force-option
        await useAnotherAccount.click({ force: true })
      }

      await googleLoginForm.waitForLoadState('networkidle')

      emailInputVisible = await googleLoginForm.isVisible('input[type="email"]')
    }
  }

  await googleLoginForm.fill('input[type="email"]', user.email)
  await googleLoginForm.getByRole('button', { name: 'Next' }).click()
  await googleLoginForm.fill('input[type="password"]', user.password)
  await googleLoginForm.getByRole('button', { name: 'Next' }).click()

  async function continueConsentScreen() {
    await page.waitForLoadState('networkidle')

    // Mandatory to wait at least 3000ms to avoid the "element not interactable" error.
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(3000)
    const consentContinueButton = googleLoginForm.getByRole('button', { name: 'Continue' })
    return await consentContinueButton.click({}).catch(() => {
      log(`🔑 Skipping consent screen.`, environment)
    })
  }

  // Clicking allow button on consent screen if it exists.
  await continueConsentScreen()

  // Clicking Continue button on consent screen if it exists (again).
  if (googleLoginForm.getByText(/Make sure you trust/i)) {
    await continueConsentScreen()
  }

  await page.waitForURL(LOGIN_SUCCESS_REDIRECT_URL)

  // Wait until the page receives the cookies.
  await expect(page.getByText(LOGIN_SUCCESS_VISIBLE_CONTENT).first()).toBeVisible({ timeout: 60_000 })

  const authFile = getAuthFile(user.pathFile, environment)

  // End of authentication steps.
  log(`[${environment}] 👍 Logged in. Saving auth to ${authFile}`, environment)
  await page.context().storageState({ path: authFile })
}

export async function IAPLogin(browser: Browser) {
  const page = await browser.newPage()

  await page.goto(STAGING_LOGIN_URL)

  const { email, password } = USER_IAP

  await page.getByRole('textbox', { name: 'Email or phone' }).click()
  await page.getByRole('textbox', { name: 'Email or phone' }).fill(email)
  await page.getByRole('textbox', { name: 'Email or phone' }).press('Enter')
  await page.getByRole('textbox', { name: 'Enter your password' }).click()
  await page.getByRole('textbox', { name: 'Enter your password' }).fill(password)
  await page.getByRole('textbox', { name: 'Enter your password' }).press('Enter')

  await page.waitForLoadState('networkidle')
  if (!page.url().includes(STAGING_LOGIN_URL)) {
    await page.waitForURL(STAGING_LOGIN_URL)
  }

  expect(page.url()).toContain(STAGING_LOGIN_URL)

  if (await page.getByText(/Wrong password/i).isVisible()) {
    throw new Error('Wrong password for IAP account. Please check your env or src/auth/users.ts file.')
  }

  await page.context().storageState({ path: STORAGE_STATE_IAP })

  await page.close()
}
