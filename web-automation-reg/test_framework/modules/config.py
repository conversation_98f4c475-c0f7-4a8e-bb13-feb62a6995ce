import os
import json
import yaml

class Config:
    """Configuration manager for the test framework."""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._load_config()
        return cls._instance

    def __init__(self):
        # Already initialized in __new__
        pass

    def _load_config(self):
        """Load configuration from file."""
        self.config = {
            # Default configuration
            "models": {
                "default": "mixtral:8x7b",
                "alternatives": [
                    "llama3:8b",
                    "tazarov/all-minilm-l6-v2-f32:latest"
                ]
            },
            "browser": {
                "type": "chromium",
                "headless": True
            },
            "timeouts": {
                "page_load": 30000,
                "element_wait": 5000,
                "step_execution": 60000
            },
            "paths": {
                "locators": "./locators",
                "recordings": "./test_recordings"
            },
            "ai_settings": {
                "max_retries": 3,
                "confidence_threshold": 0.7
            }
        }

        # Try to load from config file
        config_paths = [
            "./config/config.yaml",
            "./config.yaml",
            "./config.json",
            os.path.expanduser("~/.test_framework/config.yaml")
        ]

        for path in config_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        if path.endswith('.yaml'):
                            loaded_config = yaml.safe_load(f)
                        else:
                            loaded_config = json.load(f)

                        # Update config with loaded values
                        self._update_dict(self.config, loaded_config)
                        print(f"Loaded configuration from {path}")
                        break
                except Exception as e:
                    print(f"Error loading config from {path}: {e}")

    def _update_dict(self, target, source):
        """Recursively update a dictionary."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict(target[key], value)
            else:
                target[key] = value

    def get(self, key, default=None):
        """Get a configuration value."""
        keys = key.split('.')
        value = self.config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value
