#!/bin/bash
# Smart Gherkin Generator Script
# Converts Excel test cases to Gherkin feature files with enhanced coverage

# Activate virtual environment
source .venv/bin/activate

# Default values
EXCEL_FILE="test_framework/tests.xlsx"
OUTPUT_DIR="test_framework/features"
MAX_ENHANCEMENTS=3
ENABLE_ENHANCEMENT=true
GENERATE_REPORT=false

# Function to show usage
show_usage() {
    echo "🚀 Smart Gherkin Generator"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --excel FILE              Excel file path (default: test_framework/tests.xlsx)"
    echo "  --output DIR              Output directory (default: test_framework/features)"
    echo "  --max-enhancements NUM    Maximum enhancements per test case (default: 3)"
    echo "  --no-enhance              Disable coverage enhancement"
    echo "  --report                  Generate coverage report"
    echo "  --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Generate with default settings"
    echo "  $0 --excel my_tests.xlsx              # Use custom Excel file"
    echo "  $0 --no-enhance                       # Generate without enhancements"
    echo "  $0 --max-enhancements 5 --report      # More enhancements + report"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --excel)
            EXCEL_FILE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --max-enhancements)
            MAX_ENHANCEMENTS="$2"
            shift 2
            ;;
        --no-enhance)
            ENABLE_ENHANCEMENT=false
            shift
            ;;
        --report)
            GENERATE_REPORT=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "❌ Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Excel file exists
if [[ ! -f "$EXCEL_FILE" ]]; then
    echo "❌ Excel file not found: $EXCEL_FILE"
    exit 1
fi

# Build command
CMD="python generate_gherkin.py --excel \"$EXCEL_FILE\" --output \"$OUTPUT_DIR\" --max-enhancements $MAX_ENHANCEMENTS"

if [[ "$ENABLE_ENHANCEMENT" == false ]]; then
    CMD="$CMD --no-enhance"
fi

if [[ "$GENERATE_REPORT" == true ]]; then
    CMD="$CMD --report"
fi

# Show configuration
echo "🎯 Smart Gherkin Generator Configuration:"
echo "   📄 Excel file: $EXCEL_FILE"
echo "   📁 Output directory: $OUTPUT_DIR"
echo "   🔧 Enhancements: $ENABLE_ENHANCEMENT"
echo "   📊 Max enhancements: $MAX_ENHANCEMENTS"
echo "   📋 Generate report: $GENERATE_REPORT"
echo ""

# Run the generator
echo "🚀 Starting generation..."
eval $CMD

# Check if generation was successful
if [[ $? -eq 0 ]]; then
    echo ""
    echo "✅ Generation completed successfully!"
    echo "📁 Check the generated files in: $OUTPUT_DIR"
else
    echo ""
    echo "❌ Generation failed!"
    exit 1
fi 