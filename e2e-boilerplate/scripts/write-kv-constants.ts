import { USE_DYNAMIC_CONSTANTS_GSHEET } from '@/utils/env.util'
import appRootPath from 'app-root-path'
import axios from 'axios'
import { config } from 'dotenv'
import fs from 'fs/promises'
import { GoogleAuth } from 'google-auth-library'
import path from 'path'
import pc from 'picocolors'
import { exec } from 'shelljs'

config()

// Constants
const ROOT_PATH = appRootPath.path
const THROTTLE_INTERVAL = 2000 // 2 seconds in milliseconds
const LAST_FETCH_TIME_FILE = path.join(ROOT_PATH, '/.kv-cache.json')
const GCLOUD_AUTH_COMMAND =
  'gcloud auth application-default login --impersonate-service-account=<EMAIL> --project=wartek-staging'
const TARGET_AUDIENCE = '************-rc75uf618i02m9cv7263pnp0qifltovd.apps.googleusercontent.com'

// Interfaces
/** Represents the structure of the cache data */
interface CacheData {
  lastFetchTime: number
  bearerToken: string | null
}

/** Represents the structure of the API response */
interface ApiResponse {
  data: Record<string, string>
  message: string
}

/**
 * Utility object for file operations
 */
const fileUtils = {
  /**
   * Reads and parses a JSON file
   * @param filePath - Path to the JSON file
   * @param defaultValue - Default value to return if file reading fails
   * @returns Parsed JSON data or default value
   */
  async readJson<T>(filePath: string, defaultValue: T): Promise<T> {
    try {
      const data = await fs.readFile(filePath, 'utf-8')
      return JSON.parse(data) as T
    } catch (error) {
      return defaultValue
    }
  },

  /**
   * Writes data to a JSON file
   * @param filePath - Path to the JSON file
   * @param data - Data to write
   */
  async writeJson<T>(filePath: string, data: T): Promise<void> {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
  },
}

/**
 * Retrieves cache data from the file system
 * @returns Cache data or default values if not found
 */
async function getCacheData(): Promise<CacheData> {
  return fileUtils.readJson(LAST_FETCH_TIME_FILE, { lastFetchTime: 0, bearerToken: null })
}

/**
 * Saves cache data to the file system
 * @param data - Cache data to save
 */
async function setCacheData(data: CacheData): Promise<void> {
  await fileUtils.writeJson(LAST_FETCH_TIME_FILE, data)
}

const auth = new GoogleAuth()

/**
 * Handles credential errors by providing instructions to the user
 */
function handleCredentialsError() {
  console.info(`Running: \`${GCLOUD_AUTH_COMMAND}\` \nthen try again`)
  exec(GCLOUD_AUTH_COMMAND)
}

/**
 * Retrieves a Google token for authentication
 * @returns The bearer token for authentication
 */
export const getGoogleToken = async (): Promise<string | undefined> => {
  console.info(`Requesting IAP token with target audience ${TARGET_AUDIENCE}`)
  const client = await auth.getIdTokenClient(TARGET_AUDIENCE)

  try {
    const requestHeaders = await client.getRequestHeaders()
    return requestHeaders['Authorization']
  } catch (error) {
    if (!process.env.CI) {
      handleCredentialsError()
    } else {
      console.error('Error getting Google token:', error)
    }

    return undefined
  }
}

/**
 * Fetches data from the API with retry logic and throttling
 * @param retryCount - Current retry attempt
 * @param maxRetries - Maximum number of retry attempts
 * @returns API response data from https://docs.google.com/spreadsheets/d/1J3jleGcFIfi0sNxtcR-pl3XHSA93_DLM_U8Y-QhYZ3M/edit?gid=0#gid=0
 */
async function fetchData(retryCount = 0, maxRetries = 3): Promise<ApiResponse> {
  const now = Date.now()
  const cacheData = await getCacheData()
  const timeSinceLastFetch = now - cacheData.lastFetchTime

  // Implement throttling
  if (timeSinceLastFetch < THROTTLE_INTERVAL) {
    const delay = THROTTLE_INTERVAL - timeSinceLastFetch
    console.info(`Throttled: Delaying API call for ${delay}ms`)
    await new Promise((resolve) => setTimeout(resolve, delay))
  }

  const bearerToken = cacheData.bearerToken || (await getGoogleToken())
  await setCacheData({ lastFetchTime: now, bearerToken })

  try {
    const apiUrl =
      process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://webex.staging.belajar.id/'
    const response = await axios.get(`${apiUrl}api/e2e/kv`, {
      params: { sheet: USE_DYNAMIC_CONSTANTS_GSHEET },
      headers: { 'Proxy-Authorization': bearerToken },
    })
    return response.data
  } catch (error) {
    if (error?.response?.status === 401 && retryCount < maxRetries) {
      console.info(`Received 401, invalidating token and retrying... (Attempt ${retryCount + 1}/${maxRetries})`)
      await setCacheData({ ...cacheData, bearerToken: null })
      return fetchData(retryCount + 1, maxRetries)
    }

    if (error.message.includes('Could not load the default credentials.')) {
      handleCredentialsError()
    }

    console.error('Error fetching google sheet data:', error.message)
    return { data: {}, message: error.message }
  }
}

/**
 * Checks if the USE_DYNAMIC_CONSTANTS_GSHEET environment variable is set
 * @returns True if the variable is set, false otherwise
 */
function checkDynamicConstantsEnabled(): boolean {
  if (!USE_DYNAMIC_CONSTANTS_GSHEET) {
    console.info(
      pc.yellow(
        'USE_DYNAMIC_CONSTANTS_GSHEET is not set. If you want to use dynamic constants, set it in .env with the Google Sheet name (like "e2e-boilerplate").'
      )
    )

    return false
  }

  return true
}

/**
 * Main function to run the KV data fetching and updating process
 */
export async function runKv() {
  if (!checkDynamicConstantsEnabled()) {
    return
  }

  console.info(
    'ℹ️ Fetching KV data from Google Sheet. Link: https://docs.google.com/spreadsheets/d/1J3jleGcFIfi0sNxtcR-pl3XHSA93_DLM_U8Y-QhYZ3M/edit'
  )

  try {
    const response = await fetchData()
    if (Object.keys(response.data).length > 0) {
      await updateConstants(response.data)
    }
  } catch (error) {
    console.error('Error updating KV constants:', error.message)

    if (error.message.includes('Could not load the default credentials.')) {
      console.info(`Running: \`${GCLOUD_AUTH_COMMAND}\` \nthen try again`)
      exec(GCLOUD_AUTH_COMMAND)
    }
  }
}

/**
 * Updates the constants file with new data
 * @param data - New constant data to be written
 */
async function updateConstants(data: Record<string, string>) {
  const constantsPath = path.join(ROOT_PATH, 'gsheet-kv.ts')

  /**
   * Parses a constant value and returns a tuple of [key, parsedValue]
   * @param key - Constant key
   * @param value - Constant value
   * @returns Tuple of [key, parsedValue]
   */
  const parseConstant = (key: string, value: string) => {
    if (value.includes('$$')) {
      const params = value.match(/\$\$(\w+)/g)?.map((p) => p.slice(2)) || []
      const fixedValue = value.replace(/\$\$(\w+)/g, '${$1}')
      return [key, `({${params.join(', ')}}: {${params.map((p) => `${p}: string`).join(', ')}}) => \`${fixedValue}\``]
    }
    return [key, value]
  }

  const updatedConstants = Object.fromEntries(Object.entries(data).map(([key, value]) => parseConstant(key, value)))

  /**
   * Merge with existing constants if present
   * For now, we just overwrite the existing constants
   */

  // const existingContent = await fs.readFile(constantsPath, 'utf-8').catch(() => '')
  // if (existingContent) {
  //   const match = existingContent.match(/export const __ = ({[\s\S]*?}) as const/)
  //   if (match) {
  //     const existingConstants = (await import('gsheet-kv.ts')).__
  //     updatedConstants = updatedConstants
  //   }
  // }

  // Generate the updated content
  const constantsContent = Object.entries(updatedConstants)
    .map(([key, value]) => {
      if (typeof value === 'string' && value.startsWith('({') && value.includes('}) =>')) {
        return `  ${JSON.stringify(key)}: ${value},`
      }
      return `  ${JSON.stringify(key)}: ${JSON.stringify(value)},`
    })
    .join('\n')

  const updatedContent = `export const __ = {\n${constantsContent}\n} as const;\n`

  // Write the updated content and format the file
  await fs.writeFile(constantsPath, updatedContent, 'utf-8')
  exec('prettier gsheet-kv.ts --write', { silent: true })

  console.info(
    `✅ Constants file updated successfully at path: gsheet-kv.ts with ${Object.keys(updatedConstants).length} keys`
  )
}

runKv()
