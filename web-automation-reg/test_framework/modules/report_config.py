import os
import yaml
from typing import Dict, Any

class ReportConfig:
    """Configuration for HTML report generation."""
    
    def __init__(self, config_file: str = "config/report_config.yaml"):
        self.config_file = config_file
        self.default_config = {
            "project_name": "Rumah Pendidikan Automation Report",
            "output_directory": "./test_reports",
            "include_screenshots": True,
            "include_logs": True,
            "auto_open_report": False
        }
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # Merge with defaults
                    for key, value in self.default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"Warning: Could not load report config: {e}")
                return self.default_config.copy()
        else:
            # Create default config file
            self.save_config(self.default_config)
            return self.default_config.copy()
            
    def save_config(self, config: Dict[str, Any]):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"Warning: Could not save report config: {e}")
            
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self.config.get(key, default)
        
    def set(self, key: str, value: Any):
        """Set configuration value."""
        self.config[key] = value
        self.save_config(self.config)
        
    def update_project_name(self, project_name: str):
        """Update project name in configuration."""
        self.set("project_name", project_name)
        
    def get_project_name(self) -> str:
        """Get project name from configuration."""
        return self.get("project_name", "Rumah Pendidikan Automation Report") 