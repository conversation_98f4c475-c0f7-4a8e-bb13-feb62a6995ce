# Report Configuration for HTML Test Reports
# This file can be modified to customize the report settings

# Project name that will appear in the report header
project_name: "Rumah Pendidikan Automation Report"

# Output directory for generated HTML reports
output_directory: "./test_reports"

# Include screenshots in the report (if available)
include_screenshots: true

# Include detailed logs in the report
include_logs: true

# Automatically open the report in browser after generation
auto_open_report: false

# Report styling options
styling:
  # Primary color for charts and highlights
  primary_color: "#667eea"
  
  # Secondary color for gradients
  secondary_color: "#764ba2"
  
  # Success color for passed tests
  success_color: "#27ae60"
  
  # Error color for failed tests
  error_color: "#e74c3c"
  
  # Warning color for undefined tests
  warning_color: "#f39c12" 