import { test as setup } from '@playwright/test'
import { chromium } from 'playwright-extra'

import { AUTH_LOGIN_SEQUENTIAL, CI, ENVIRONMENT, STAGING_ONLY } from '@/utils/env.util'
import { USERS, USERS_SHOULD_LOGIN_APP, UserKey } from 'src/constants/users.constant'

import { googleLogin } from '@/auth/google-login.helper'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'

import { log } from '@/utils/playwright-config.util'

setup('@setup @production Setting up production authentication', async () => {
  if (STAGING_ONLY || (CI && ENVIRONMENT !== 'production')) {
    return
  }

  // 2min per user to login
  const numberOfUsers = Object.keys(USERS_SHOULD_LOGIN_APP).length
  const timeout = numberOfUsers * 2 * 60 * 1_000
  setup.setTimeout(timeout)

  if (!CI) {
    const isConfigValid = await checkConfig('production')
    if (isConfigValid) {
      log('🔑 Cached auth files detected; skipping setup production auth.', 'production')
      return
    }
  }

  log('🔑 Logging you in with user data stored in the `src/auth/users.ts` file', 'production')

  const browser = await chromium.launch({ headless: false })

  const runCode = async () => {
    const processUser = async (key: UserKey) => {
      const context = await browser.newContext()
      const page = await context.newPage()
      const user = USERS[key]
      await googleLogin(page, user, 'production')

      await context.close()
    }

    if (CI || AUTH_LOGIN_SEQUENTIAL) {
      for (const key of USERS_SHOULD_LOGIN_APP) {
        await processUser(key)
      }
    } else {
      const promises = USERS_SHOULD_LOGIN_APP.map(processUser)
      await Promise.all(promises)
    }
  }

  await runCode()

  if (!CI) generateConfigAuth('production')

  await browser.close()
})
