import { connectXray } from '@/helpers/global.helper'
import { deleteTableRowsButFirst } from '@/helpers/table.helper'
import { STAGING_BASE_URL } from '@/utils/env.util'
import { setViewportAsDesktop } from '@/utils/setup'
import { expect, test } from '@playwright/test'

const URL_LIST_PAGE_PREFIX = `${STAGING_BASE_URL}/pengaturan-akses`

test.beforeEach(async ({ page }) => {
  setViewportAsDesktop(page)
})

test('@RPNG-3541 @pemberian akses massal satpen @visual @staging should match the bulk access satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3541' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()

  await page.waitForSelector('[data-testid="table-list"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-list"]')

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3543 @pemberian akses massal satpen @visual @staging should match the bulk access detail "Berhasil" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3543' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()

  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('[data-testid="table-list"]')
  await deleteTableRowsButFirst(page, '[data-testid="table-list"]')

  const detailaksesmassal = page.getByRole('row', { name: '19/06/2024 15:29' }).getByRole('button')
  await detailaksesmassal.click()

  //assert pop-up information akses massal
  await expect(
    page
      .getByLabel('Detail Proses Pemberian Akses')
      .locator('div')
      .filter({ hasText: 'Waktu Unggah 19/06/2024 15:29' })
      .nth(1)
  ).toBeVisible()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3545 @pemberian akses massal satpen @visual @staging should match the bulk access detail "Berhasil Sebagian" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3545' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  await page.getByPlaceholder('Cari berdasarkan nama file').fill('<EMAIL>')

  //only get for the last row
  await page.waitForSelector('[data-testid="table-list"]')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 14:12' }).getByRole('button').click()
  await page.waitForLoadState()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3547 @pemberian akses massal satpen @visual @staging should match the bulk access detail "Gagal" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3547' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 14:08' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3548 @pemberian akses massal satpen @visual @staging should match the bulk access detail "Menunggu Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3548' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 10:58' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3550 @pemberian akses massal satpen @visual @staging should match the bulk access detail "Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3550' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 10:56' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3551 @pemberian akses massal kabkota @visual @staging should match the bulk access satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3551' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForSelector('[data-testid="table-list"]')

  await deleteTableRowsButFirst(page, '[data-testid="table-list"]')

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3552 @pemberian akses massal kabkota @visual @staging should match the bulk access detail "Berhasil" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3552' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  const detailaksesmassal = page.getByRole('row', { name: '19/06/2024 11:06' }).getByRole('button')
  await detailaksesmassal.click()

  //assert pop-up information akses massal
  await expect(
    page
      .getByLabel('Detail Proses Pemberian Akses')
      .locator('div')
      .filter({ hasText: 'Waktu Unggah 19/06/2024 11:06' })
      .nth(1)
  ).toBeVisible()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3553 @pemberian akses massal kabkota @visual @staging should match the bulk access detail "Berhasil Sebagian" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3553' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  await page.getByPlaceholder('Cari berdasarkan nama file').fill('<EMAIL>')

  //only get for the last row
  await page.waitForSelector('[data-testid="table-list"]')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 14:05' }).getByRole('button').click()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3554 @pemberian akses massal kabkota @visual @staging should match the bulk access detail "Gagal" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3554' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 11:00' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3555 @pemberian akses massal kabkota @visual @staging should match the bulk access detail "Menunggu Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3555' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 10:53' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3556 @pemberian akses massal kabkota @visual @staging should match the bulk access detail "Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3556' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Kabupaten/Kota' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  await page.waitForSelector('//*[@id="__next"]/div/main/div[2]/div/table')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 10:31' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3540 @pemberian akses massal provinsi @visual @staging should match the bulk access satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3540' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForSelector('[data-testid="table-list"]')

  await deleteTableRowsButFirst(page, '[data-testid="table-list"]')

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3542 @pemberian akses massal provinsi @visual @staging should match the bulk access detail "Berhasil" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3542' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  const detailaksesmassal = page.getByRole('row', { name: '19/06/2024 11:06' }).getByRole('button')
  await detailaksesmassal.click()

  //assert pop-up information akses massal
  await expect(
    page
      .getByLabel('Detail Proses Pemberian Akses')
      .locator('div')
      .filter({ hasText: 'Waktu Unggah 19/06/2024 11:06' })
      .nth(1)
  ).toBeVisible()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3544 @pemberian akses massal provinsi @visual @staging should match the bulk access detail "Berhasil Sebagian" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3544' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  await page.getByPlaceholder('Cari berdasarkan nama file').fill('<EMAIL>')

  //wait list label loaded
  await page.waitForSelector('[data-testid="table-list"]')

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 14:06' }).getByRole('button').click()

  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3546 @pemberian akses massal provinsi @visual @staging should match the bulk access detail "Gagal" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3546' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  //go through detail akses massal
  await page.getByRole('row', { name: '19/06/2024 11:00' }).getByRole('button').click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3549 @pemberian akses massal provinsi @visual @staging should match the bulk access detail "Menunggu Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3549' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('<EMAIL>')
  await page.waitForLoadState()

  //go through detail akses massal
  await page
    .getByRole('row', {
      name: '/06/2024 10:48 Template_Bulk_Setting_Provinsi_20240619-034847.csv <EMAIL> Menunggu Diproses Detail',
    })
    .getByRole('button')
    .click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})

test('@RPNG-3558 @pemberian akses massal provinsi @visual @staging should match the bulk access detail "Diproses" satuan pendidikan user page snapshot', async ({
  page,
}, testInfo) => {
  connectXray(testInfo, { key: 'RPNG-3558' })
  const LIST_PAGE_SATPEN_URL = `${URL_LIST_PAGE_PREFIX}/satuan-pendidikan`
  await page.goto(LIST_PAGE_SATPEN_URL)
  await page.waitForURL(LIST_PAGE_SATPEN_URL)

  // wait until table list bulk displayed
  await page.getByRole('link', { name: 'Dinas Provinsi' }).click()
  await page.waitForLoadState()
  await page.getByRole('tab', { name: 'Pemberian Akses Massal' }).click()
  await page.waitForLoadState()

  //fulfil search column
  const searchcolumn = page.getByPlaceholder('Cari berdasarkan nama file')
  await searchcolumn.fill('')
  await page.getByPlaceholder('Cari berdasarkan nama file').click()
  await page.getByPlaceholder('Cari berdasarkan nama file').fill('<EMAIL>')
  await page.getByPlaceholder('Cari berdasarkan nama file').press('Enter')
  await page.waitForLoadState()

  //go through detail akses massal
  await page
    .getByRole('row', {
      name: '/06/2024 10:48 Template_Bulk_Setting_Provinsi_20240619-034847.csv <EMAIL> Diproses Detail',
    })
    .getByRole('button')
    .click()

  // Take the screenshot with specific options
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})
