/**
 * This file is used to cache authentication results to improve your development experience.
 * So, when you run the test again, you don't need to log in again.
 *
 * This auth caching process is only done in development mode
 * The process is run when you run the test for the first time
 * or when the expiry time has passed.
 *
 * You can adjust the expiry time of the token according to your needs.
 * The default auth cache time is 7 days (assuming your app token is valid for 7 days or more).
 *
 * If you want to force re-login, you can delete the .auth-dev folder.
 * So, when you run the test again, the login process will be performed again.
 * Also, if you change the user data in the `users.ts` file, the login process will be performed again.
 */

export const CONFIG_AUTH_CACHE = '.auth-dev/config.json'

import { USERS, UserKey } from '@/constants/users.constant'
import { AUTH_CACHE_EXPIRY_TIME_DAYS, AUTH_SETUP_ENABLED, ENVIRONMENT, IAP_ENABLED } from '@/utils/env.util'
import { log } from '@/utils/playwright-config.util'

import { STORAGE_STATE_IAP } from '@/auth/google-login.helper'
import dayjs from 'dayjs'
import fs from 'fs-extra'
import { isEqual } from 'lodash'
import { getAuthFile } from './global.helper'

export function generateConfigAuth(env: typeof ENVIRONMENT, userKey: UserKey) {
  /**
   * Cache authentication files for a specified number of days so that we don't need to log in again for every test run.
   * You can adjust this value according to your token expiration time interval.
   */
  const DEV_TOKEN_EXPIRY_TIME_DAYS = AUTH_CACHE_EXPIRY_TIME_DAYS

  if (!fs.existsSync('.auth-dev')) {
    fs.mkdirSync('.auth-dev')
  }

  fs.writeJSONSync(CONFIG_AUTH_CACHE, {
    users: USERS,
    tokenExpiry: new Date().getTime() + DEV_TOKEN_EXPIRY_TIME_DAYS * 24 * 60 * 60 * 1000,
  })

  fs.copySync('.auth', '.auth-dev')
  log(`[Cache] 🚀 Auth file: ${userKey} cached to improve your development experience.`, env)
}

export const checkConfig = async (env: typeof ENVIRONMENT, userKey: UserKey) => {
  // Step 1: Check if config and auth-dev folder exist
  const isConfigExist = fs.existsSync(CONFIG_AUTH_CACHE)
  const isAuthDevExist = fs.existsSync('.auth-dev')

  if (!isConfigExist || (AUTH_SETUP_ENABLED && !isAuthDevExist)) {
    log(`🔍 Config file or auth-dev folder not found for ${userKey}. Need to set up authentication.`, env)
    return false
  }

  // Step 2: Check token validity
  const config = fs.readJSONSync(CONFIG_AUTH_CACHE)
  const isTokenValid = dayjs(config.tokenExpiry).isAfter(dayjs())

  if (!isTokenValid) {
    log(`⏰ Token has expired for ${userKey}. Re-authentication required.`, env)
    return false
  }

  // Step 3: Handle disabled auth setup
  if (!AUTH_SETUP_ENABLED) {
    if (!IAP_ENABLED) {
      log('🔒 Auth setup is disabled and IAP is not enabled. Skipping auth setup.', env)
      return true
    }

    // Check for IAP storage state when auth setup is disabled but IAP is enabled
    const isStorageStateIAPExist = fs.existsSync(STORAGE_STATE_IAP)
    if (!isStorageStateIAPExist) {
      log('🔑 IAP cache not found. Initiating login process.', env)
      return false
    }

    log('✅ IAP cache found. Proceeding with existing authentication.', env)
    return true
  }

  // Step 4: Verify user existence and auth file
  const user = USERS[userKey]
  if (!user) {
    log(`❌ User ${userKey} not found in USERS configuration. Please check your users.ts file.`, env)
    return false
  }

  const userAuthFile = getAuthFile(user.pathFile, env)
  if (!fs.existsSync(userAuthFile) && userKey !== 'IAP') {
    log(`📁 Auth file for ${userKey} not found. Initiating re-authentication.`, env)
    return false
  }

  // Step 5: Check for user configuration changes
  const isUserConfigSame = isEqual(config.users[userKey], user)
  if (!isUserConfigSame) {
    log(`🔄 User configuration for ${userKey} has changed. Re-authentication required.`, env)
    return false
  }

  // Step 6: Special handling for IAP-enabled scenarios
  if (IAP_ENABLED && userKey === 'IAP') {
    if (!fs.existsSync(STORAGE_STATE_IAP)) {
      log('🔐 IAP auth file not found. Initiating IAP authentication process.', env)
      return false
    }
    log('✅ IAP auth file found. Proceeding with existing IAP authentication.', env)
  } else {
    log(`✅ Authentication is valid for ${userKey}. Proceeding with cached credentials.`, env)
  }

  return true
}
