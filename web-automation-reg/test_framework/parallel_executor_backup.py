#!/usr/bin/env python3
"""
Optimized Parallel Test Executor for AI Test Framework
Handles true concurrent execution of multiple test cases for massive performance improvements
"""

import asyncio
import json
import os
import re
import subprocess
import sys
import time
from datetime import datetime
from typing import List, Dict, Any
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import multiprocessing
from dataclasses import dataclass
from pathlib import Path
import threading
import queue

from modules.test_executor import TestExecutor
from modules.report_generator import HTMLReportGenerator
from modules.gherkin_reader import GherkinReader
from modules.test_reader import TestReader


@dataclass
class TestResult:
    """Test execution result data structure"""
    test_case: str
    status: str  # 'passed', 'failed', 'skipped', 'error'
    execution_time: float
    steps_passed: int
    steps_failed: int
    steps_skipped: int
    total_steps: int
    error_message: str = ""
    detailed_steps: List[Dict] = None
    start_time: datetime = None
    end_time: datetime = None


class OptimizedParallelTestExecutor:
    """Executes multiple test cases in true parallel for maximum performance"""
    
    def __init__(self, max_workers=None, chunk_size=5):
        """
        Initialize optimized parallel executor
        
        Args:
            max_workers: Maximum number of parallel workers (default: CPU cores)
            chunk_size: Number of tests to run in each batch
        """
        self.max_workers = max_workers or min(multiprocessing.cpu_count(), 8)
        self.chunk_size = chunk_size
        self.results = []
        self.execution_start_time = None
        self.execution_end_time = None
        
        # Performance tracking
        self.total_tests = 0
        self.completed_tests = 0
        self.failed_tests = 0
        self.passed_tests = 0
        
        # Single report generation
        self.single_report_data = {
            'features': [],
            'scenarios': [],
            'execution_summary': {},
            'test_results': []
        }
        
        # Create output directories
        os.makedirs("test_framework/reports/parallel", exist_ok=True)
        os.makedirs("test_framework/cache/parallel", exist_ok=True)
        
        # Thread-safe result collection
        self.results_lock = threading.Lock()
        self.results_queue = queue.Queue()
    
    async def run_all_tests(self, test_cases: List[str], mode="gherkin") -> Dict[str, Any]:
        """
        Run all test cases in true parallel for maximum performance
        
        Args:
            test_cases: List of test case names to execute
            mode: 'gherkin' or 'excel'
        
        Returns:
            Summary of all test executions
        """
        print(f"🚀 Starting TRUE PARALLEL Test Execution")
        print(f"📊 Total Test Cases: {len(test_cases)}")
        print(f"⚡ Max Parallel Workers: {self.max_workers}")
        print(f"📦 Chunk Size: {self.chunk_size}")
        
        self.total_tests = len(test_cases)
        self.execution_start_time = datetime.now()
        
        # Execute all test cases in true parallel
        print(f"🚀 Starting TRUE PARALLEL execution of {len(test_cases)} test cases...")
        
        # Create tasks for true parallel execution
        tasks = []
        for i, test_case in enumerate(test_cases):
            task = self._execute_single_test_optimized(test_case, mode, i)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                print(f"❌ Test execution failed with exception: {result}")
                continue
            if result:
                self.results.append(result)
        
        self.execution_end_time = datetime.now()
        
        # Generate single comprehensive report
        summary = self._generate_summary(self.results)
        self._generate_single_comprehensive_report(summary)
        
        return summary
    
    async def _execute_single_test_optimized(self, test_case: str, mode: str, index: int) -> TestResult:
        """Execute a single test case with optimized output parsing"""
        start_time = time.time()
        test_start_time = datetime.now()
        
        try:
            # Run the test using subprocess
            cmd = [
                sys.executable, "test_framework/main.py",
                "--test-case", test_case,
                "--mode", mode,
                "--parallel-mode",
                "--single-report"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"  ✅ Completed: {test_case} ({execution_time:.1f}s) - PASSED")
            else:
                print(f"  ❌ Completed: {test_case} ({execution_time:.1f}s) - FAILED")
            
            # Parse the output to extract results
            return self._parse_test_output_optimized(
                test_case, 
                result.stdout, 
                result.stderr, 
                execution_time, 
                test_start_time
            )
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            print(f"  ⏰ Timeout: {test_case} ({execution_time:.1f}s) - TIMEOUT")
            return TestResult(
                test_case=test_case,
                status="timeout",
                execution_time=execution_time,
                steps_passed=0,
                steps_failed=0,
                steps_skipped=0,
                total_steps=0,
                error_message="Test execution timed out",
                start_time=test_start_time,
                end_time=datetime.now()
            )
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"  ❌ Error: {test_case} ({execution_time:.1f}s) - {str(e)}")
            return TestResult(
                test_case=test_case,
                status="error",
                execution_time=execution_time,
                steps_passed=0,
                steps_failed=0,
                steps_skipped=0,
                total_steps=0,
                error_message=str(e),
                start_time=test_start_time,
                end_time=datetime.now()
            )
    
    def _parse_test_output_optimized(self, test_case: str, stdout: str, stderr: str, execution_time: float, start_time: datetime) -> TestResult:
        """Parse test output to extract results without waiting for HTML reports"""
        try:
            # Look for result patterns in stdout
            status = "passed"  # Default
            steps_passed = 0
            steps_failed = 0
            steps_skipped = 0
            total_steps = 0
            error_message = ""
            step_results = {}  # Store individual step results with details
            
            # Parse stdout for test results
            lines = stdout.split('\n')
            current_step = 0
            
            for line in lines:
                # Look for step execution results with new detailed format
                if "🔍 Executing Step" in line:
                    # Extract step number from "🔍 Executing Step X:"
                    step_match = re.search(r'🔍 Executing Step (\d+):', line)
                    if step_match:
                        current_step = int(step_match.group(1))
                        total_steps += 1
                        # Initialize step result
                        step_results[current_step] = {
                            'status': 'pending',
                            'error': '',
                            'result': ''
                        }
                elif "✅ Step" in line and "PASSED:" in line:
                    steps_passed += 1
                    if current_step > 0:
                        step_results[current_step]['status'] = 'passed'
                        # Extract result message if available
                        result_match = re.search(r'PASSED:\s*(.+)', line)
                        if result_match:
                            step_results[current_step]['result'] = result_match.group(1).strip()
                elif "❌ Step" in line and ("FAILED:" in line or "ERROR:" in line):
                    steps_failed += 1
                    if current_step > 0:
                        step_results[current_step]['status'] = 'failed'
                        # Extract error message
                        error_match = re.search(r'(?:FAILED|ERROR):\s*(.+)', line)
                        if error_match:
                            step_results[current_step]['error'] = error_match.group(1).strip()
                            error_message = step_results[current_step]['error']
                
                # Look for overall test status
                if "Test completed" in line or "Test finished" in line:
                    if "failed" in line.lower():
                            status = "failed"
                    elif "passed" in line.lower():
                            status = "passed"
                    elif "skipped" in line.lower():
                        status = "skipped"
                
                # Look for error messages
                if "Error:" in line or "Exception:" in line:
                    error_message = line.strip()
                    if status == "passed":
                        status = "failed"
            
            # Determine overall status based on step results
            if steps_failed > 0:
                    status = "failed"
            elif steps_passed > 0:
                    status = "passed"
            elif total_steps == 0:
                # If no steps were detected, assume it passed (fallback)
                status = "passed"
                steps_passed = 1
                total_steps = 1
            
            return TestResult(
                test_case=test_case,
                status=status,
                execution_time=execution_time,
                steps_passed=steps_passed,
                steps_failed=steps_failed,
                steps_skipped=steps_skipped,
                total_steps=total_steps,
                error_message=error_message,
                detailed_steps=step_results,
                start_time=start_time,
                end_time=datetime.now()
            )
        except Exception as e:
            print(f"Error parsing output for {test_case}: {str(e)}")
            return TestResult(
                test_case=test_case,
                status="error",
                execution_time=execution_time,
                steps_passed=0,
                steps_failed=0,
                steps_skipped=0,
                total_steps=0,
                error_message=f"Error parsing output: {str(e)}",
                start_time=start_time,
                end_time=datetime.now()
            )
    
    def _generate_summary(self, results: List[TestResult]) -> Dict[str, Any]:
        """Generate comprehensive summary of all test executions"""
        total_time = (self.execution_end_time - self.execution_start_time).total_seconds()
        
        # Calculate statistics
        passed_tests = sum(1 for r in results if r.status == "passed")
        failed_tests = sum(1 for r in results if r.status == "failed")
        skipped_tests = sum(1 for r in results if r.status == "skipped")
        error_tests = sum(1 for r in results if r.status == "error")
        timeout_tests = sum(1 for r in results if r.status == "timeout")
        
        total_steps = sum(r.total_steps for r in results)
        passed_steps = sum(r.steps_passed for r in results)
        failed_steps = sum(r.steps_failed for r in results)
        skipped_steps = sum(r.steps_skipped for r in results)
        
        # Calculate performance metrics
        avg_time_per_test = total_time / len(results) if results else 0
        tests_per_minute = (len(results) / total_time) * 60 if total_time > 0 else 0
        
        summary = {
            "execution_summary": {
                "total_tests": len(results),
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "skipped_tests": skipped_tests,
                "error_tests": error_tests,
                "timeout_tests": timeout_tests,
                "success_rate": (passed_tests / len(results)) * 100 if results else 0,
                "total_execution_time": total_time,
                "avg_time_per_test": avg_time_per_test,
                "tests_per_minute": tests_per_minute
            },
            "step_summary": {
                "total_steps": total_steps,
                "passed_steps": passed_steps,
                "failed_steps": failed_steps,
                "skipped_steps": skipped_steps,
                "step_success_rate": (passed_steps / total_steps) * 100 if total_steps > 0 else 0
            },
            "performance_metrics": {
                "parallel_workers": self.max_workers,
                "chunk_size": self.chunk_size,
                "efficiency_improvement": self._calculate_efficiency_improvement(total_time, len(results))
            },
            "detailed_results": [
                {
                    "test_case": r.test_case,
                    "status": r.status,
                    "execution_time": r.execution_time,
                    "steps_passed": r.steps_passed,
                    "steps_failed": r.steps_failed,
                    "steps_skipped": r.steps_skipped,
                    "total_steps": r.total_steps,
                    "error_message": r.error_message,
                    "detailed_steps": r.detailed_steps if r.detailed_steps else {},
                    "start_time": r.start_time.isoformat(),
                    "end_time": r.end_time.isoformat()
                }
                for r in results
            ]
        }
        
        return summary
    
    def _calculate_efficiency_improvement(self, parallel_time: float, test_count: int) -> float:
        """Calculate efficiency improvement compared to sequential execution"""
        # Estimate sequential time: 8 minutes for 14 tests = 34.3 seconds per test
        estimated_sequential_time = test_count * 34.3
        improvement = ((estimated_sequential_time - parallel_time) / estimated_sequential_time) * 100
        return max(0, improvement)
    
    def _generate_single_comprehensive_report(self, summary: Dict[str, Any]):
        """Generate hierarchical HTML reports with navigation between pages"""
        try:
            import os
            from datetime import datetime
            
            # Create timestamp for all reports
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Generate main summary page
            summary_file = f"test_framework/reports/parallel_comprehensive_{timestamp}.html"
            summary_content = self._generate_summary_page(summary, timestamp)
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            # Generate detailed feature page
            feature_file = f"test_framework/reports/parallel_feature_{timestamp}.html"
            feature_content = self._generate_feature_detail_page(summary, timestamp)
            
            with open(feature_file, 'w', encoding='utf-8') as f:
                f.write(feature_content)
            
            print(f"📊 Parallel Execution Reports Generated:")
            print(f"  📄 Summary: {summary_file}")
            print(f"  📄 Feature Details: {feature_file}")
            
        except Exception as e:
            print(f"⚠️ Could not generate comprehensive report: {e}")
        
        # Print final summary
        print("=" * 60)
        print("🎯 PARALLEL EXECUTION SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {len(summary['detailed_results'])}")
        print(f"Success Rate: {summary['execution_summary']['success_rate']:.1f}%")
        print(f"Execution Time: {summary['execution_summary']['total_execution_time']:.1f} seconds")
        print(f"Tests Per Minute: {summary['execution_summary']['tests_per_minute']:.1f}")
        print(f"Efficiency Improvement: {summary['performance_metrics']['efficiency_improvement']:.1f}%")
        print("=" * 60)
        
    def _generate_summary_page(self, summary: Dict[str, Any], timestamp: str) -> str:
        """Generate the main summary page with navigation to feature details"""
        exec_summary = summary["execution_summary"]
        detailed_results = summary["detailed_results"]
        
        # Calculate statistics
        total_tests = len(detailed_results)
        passed_tests = sum(1 for r in detailed_results if r['status'] == 'passed')
        failed_tests = sum(1 for r in detailed_results if r['status'] == 'failed')
        skipped_tests = sum(1 for r in detailed_results if r['status'] == 'skipped')
        error_tests = sum(1 for r in detailed_results if r['status'] == 'error')
        timeout_tests = sum(1 for r in detailed_results if r['status'] == 'timeout')
        
        # Generate feature overview table with clickable link
        feature_overview_html = f"""
        <tr>
            <td style="color: #007bff; font-weight: 500;">
                <a href="parallel_feature_{timestamp}.html" style="color: #007bff; text-decoration: none;">
                    Parallel Test Execution →
                </a>
            </td>
            <td>✅</td>
            <td>🖥️ Runner Machine</td>
            <td>🐧 linux</td>
            <td>🌐 103</td>
            <td>{total_tests}</td>
            <td>{passed_tests}</td>
            <td>{failed_tests}</td>
            <td>{skipped_tests + error_tests + timeout_tests}</td>
        </tr>
        """
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rumah Pendidikan Automation Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header h2 {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }}
        .summary-cards {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .card {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }}
        .card h3 {{
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }}
        .donut-chart {{
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg {passed_tests/total_tests*360}deg, #dc3545 {passed_tests/total_tests*360}deg 360deg);
        }}
        .donut-chart::before {{
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }}
        .donut-chart::after {{
            content: '{total_tests}';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 1.5em;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }}
        .donut-chart.passed {{
            background: conic-gradient(#28a745 0deg 360deg);
        }}
        .donut-chart.failed {{
            background: conic-gradient(#dc3545 0deg 360deg);
        }}
        .donut-chart.mixed {{
            background: conic-gradient(#28a745 0deg {passed_tests/total_tests*360}deg, #dc3545 {passed_tests/total_tests*360}deg 360deg);
        }}
        .passed-tooltip, .failed-tooltip {{
            position: fixed;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.7em;
            font-weight: 500;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 1000;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3);
            backdrop-filter: blur(4px);
        }}
        .passed-tooltip {{
            border-left: 3px solid #28a745;
        }}
        .failed-tooltip {{
            border-left: 3px solid #dc3545;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }}
        .stat-item {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }}
        .stat-label {{
            font-weight: 500;
            color: #666;
        }}
        .stat-value {{
            font-weight: bold;
            color: #333;
        }}
        .test-results {{
            padding: 30px;
        }}
        .test-results h3 {{
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }}
        th, td {{
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }}
        th {{
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }}
        tr:hover {{
            background: #f8f9fa;
        }}
        .footer {{
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }}
        .social-icons {{
            margin-top: 10px;
        }}
        .social-icons a {{
            margin: 0 10px;
            font-size: 1.5em;
            text-decoration: none;
        }}
        .social-icons a:hover {{
            opacity: 0.7;
        }}
        
        /* Error Details Styles */
        .error-details {{
            margin-top: 10px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            overflow: hidden;
        }}
        .error-header {{
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            background: #fed7d7;
            border-left: 4px solid #e53e3e;
        }}
        .error-icon {{
            font-size: 1.1em;
        }}
        .error-text {{
            flex: 1;
            font-weight: 600;
            color: #c53030;
        }}
        .quick-show-btn {{
            background: #38a169;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }}
        .quick-show-btn:hover {{
            background: #2f855a;
        }}
        .error-content {{
            padding: 15px;
            background: white;
        }}
        .error-comparison {{
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }}
        .expected-actual {{
            display: flex;
            flex-direction: column;
            gap: 10px;
        }}
        .expected, .actual {{
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .expected {{
            background: #f0fff4;
            border-bottom: 1px solid #e2e8f0;
        }}
        .actual {{
            background: #fff5f5;
        }}
        .expected strong, .actual strong {{
            min-width: 80px;
            font-size: 0.9em;
        }}
        .expected .value {{
            color: #38a169;
            font-family: monospace;
            background: #f0fff4;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #c6f6d5;
        }}
        .actual .value {{
            color: #e53e3e;
            font-family: monospace;
            background: #fed7d7;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #feb2b2;
        }}
    </style>
    <script>
        function showPassedTooltip(event, passedText) {{
            const tooltip = document.getElementById('passed-tooltip-summary');
            tooltip.innerHTML = passedText;
            tooltip.style.left = (event.clientX - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (event.clientY - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.opacity = '1';
        }}
        
        function showFailedTooltip(event, failedText) {{
            const tooltip = document.getElementById('failed-tooltip-summary');
            tooltip.innerHTML = failedText;
            tooltip.style.left = (event.clientX - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (event.clientY - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.opacity = '1';
        }}
        
        function showPassedTooltip(event, text) {{
            const tooltip = document.getElementById('passed-tooltip-summary');
            tooltip.textContent = text;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }}
        
        function showFailedTooltip(event, text) {{
            const tooltip = document.getElementById('failed-tooltip-summary');
            tooltip.textContent = text;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }}
        
        function hideTooltip() {{
            document.getElementById('passed-tooltip-summary').style.opacity = '0';
            document.getElementById('failed-tooltip-summary').style.opacity = '0';
        }}
        
        function handleDonutHover(event, passedTests, failedTests) {{
            const rect = event.target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            
            // Calculate angle from center to mouse position
            const deltaX = mouseX - centerX;
            const deltaY = centerY - mouseY; // Invert Y axis
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            const normalizedAngle = (angle + 360) % 360;
            
            // Calculate passed section angle
            const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
            
            // Determine which section is being hovered
            if (normalizedAngle <= passedAngle) {{
                // Hovering over passed section (green)
                showPassedTooltip(event, `Passed: ${{passedTests}}`);
                document.getElementById('failed-tooltip-summary').style.opacity = '0';
            }} else {{
                // Hovering over failed section (red)
                showFailedTooltip(event, `Failed: ${{failedTests}}`);
                document.getElementById('passed-tooltip-summary').style.opacity = '0';
            }}
        }}
        
        function toggleErrorDetails(errorId) {{
            const errorContent = document.getElementById(errorId);
            if (errorContent.style.display === 'none') {{
                errorContent.style.display = 'block';
            }} else {{
                errorContent.style.display = 'none';
            }}
        }}
        
        // Expand first scenario by default
        window.onload = function() {{
            const firstScenario = document.querySelector('.scenario-item');
            if (firstScenario) {{
                const scenarioId = firstScenario.querySelector('.scenario-header').getAttribute('onclick').match(/'([^']+)'/)[1];
                toggleScenario(scenarioId);
            }}
        }};
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <h3>Features</h3>
                <div class="donut-chart {'passed' if failed_tests == 0 else 'mixed'}" 
                     onmousemove="handleDonutHover(event, {passed_tests}, {failed_tests})" 
                     onmouseout="hideTooltip()">
                    <div class="passed-tooltip" id="passed-tooltip-summary"></div>
                    <div class="failed-tooltip" id="failed-tooltip-summary"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{passed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{failed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{skipped_tests/total_tests*100:.1f}%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Scenarios</h3>
                <div class="donut-chart {'passed' if failed_tests == 0 else 'mixed'}" 
                     onmousemove="handleDonutHover(event, {passed_tests}, {failed_tests})" 
                     onmouseout="hideTooltip()">
                    <div class="passed-tooltip" id="passed-tooltip-summary-2"></div>
                    <div class="failed-tooltip" id="failed-tooltip-summary-2"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{passed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{failed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{skipped_tests/total_tests*100:.1f}%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Run Info</h3>
                <div style="text-align: left; margin-top: 20px;">
                    <div class="stat-item">
                        <span class="stat-label">🔗 Project</span>
                        <span class="stat-value">Rumah Pendidikan Automation Report</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">🕐 Generation Time</span>
                        <span class="stat-value">{datetime.now().isoformat()}Z</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">👤 OS User</span>
                        <span class="stat-value">{os.getenv('USER', 'unknown')}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">⏱️ Duration</span>
                        <span class="stat-value">{exec_summary['total_execution_time']:.0f} Seconds</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>Features Overview</h3>
            <table>
                <thead>
                    <tr>
                        <th>Feature Name</th>
                        <th>Status</th>
                        <th>Device</th>
                        <th>OS</th>
                        <th>Browser</th>
                        <th>Total</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Undefined</th>
                    </tr>
                </thead>
                <tbody>
                    {feature_overview_html}
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team.</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_feature_detail_page(self, summary: Dict[str, Any], timestamp: str) -> str:
        """Generate the detailed feature page with individual test cases and steps"""
        exec_summary = summary["execution_summary"]
        detailed_results = summary["detailed_results"]
        
        # Calculate statistics
        total_tests = len(detailed_results)
        passed_tests = sum(1 for r in detailed_results if r['status'] == 'passed')
        failed_tests = sum(1 for r in detailed_results if r['status'] == 'failed')
        skipped_tests = sum(1 for r in detailed_results if r['status'] == 'skipped')
        error_tests = sum(1 for r in detailed_results if r['status'] == 'error')
        timeout_tests = sum(1 for r in detailed_results if r['status'] == 'timeout')
        
        # Generate tags for navigation
        tags_html = ""
        for result in detailed_results:
            tc_id = result['test_case']
            tags_html += f'<a href="#{tc_id}" class="tag">{tc_id}</a>'
        
        # Generate scenarios with collapsible sections
        scenarios_html = ""
        for result in detailed_results:
            tc_id = result['test_case']
            status = result['status']
            execution_time = result['execution_time']
            steps_passed = result['steps_passed']
            steps_failed = result['steps_failed']
            steps_skipped = result['steps_skipped']
            total_steps = result['total_steps']
            error_message = result['error_message']
            
            status_icon = {
                'passed': '✅',
                'failed': '❌',
                'skipped': '⏭️',
                'error': '💥',
                'timeout': '⏰'
            }.get(status, '❓')
            
            # Generate steps HTML with actual Gherkin steps and execution results
            steps_html = ""
            gherkin_steps = self._get_gherkin_steps_for_tc(tc_id)
            step_results = result.get('detailed_steps', {}) if isinstance(result, dict) else (result.detailed_steps if result.detailed_steps else {})
            
            for i, step in enumerate(gherkin_steps):
                # Use actual step results if available, otherwise fall back to sequential logic
                step_num = i + 1
                if step_num in step_results:
                    if isinstance(step_results[step_num], dict):
                        step_status = step_results[step_num]['status']
                        step_result = step_results[step_num].get('result', '')
                        step_error = step_results[step_num].get('error', '')
                    else:
                        # Backward compatibility for old format
                        step_status = step_results[step_num]
                        step_result = ''
                        step_error = ''
                else:
                    # Fallback to sequential logic if no detailed results
                    step_status = 'passed' if i < steps_passed else ('failed' if i < steps_passed + steps_failed else 'skipped')
                    step_result = ''
                    step_error = ''
                
                step_icon = '✅' if step_status == 'passed' else ('❌' if step_status == 'failed' else '⏭️')
                
                # Generate error details HTML for failed steps
                error_details_html = ""
                if step_status == 'failed':
                    # Extract expected and actual from error message
                    expected = "N/A"
                    actual = "N/A"
                    
                    # Use the step-specific error message
                    error_msg = step_error if step_error else error_message
                    
                    if error_msg:
                        # Parse the error message to extract expected and actual values
                        import re
                        
                        # Try different patterns to extract expected and actual
                        # Pattern 1: "Expected: text, Actual: text" (from FAILED line)
                        expected_match = re.search(r"Expected:\s*([^,]+)", error_msg)
                        actual_match = re.search(r"Actual:\s*([^,\n]+)", error_msg)
                        
                        if expected_match:
                            expected = expected_match.group(1).strip()
                        if actual_match:
                            actual = actual_match.group(1).strip()
                        
                        # Pattern 2: Try to extract from the step text itself for assertions
                        if expected == "N/A" and "should see" in step:
                            # Extract the text from "Then I should see "text""
                            text_match = re.search(r'should see\s*["\']([^"\']*)["\']', step)
                            if text_match:
                                expected = text_match.group(1)
                        
                        # Pattern 3: If still N/A, try to extract from the full error message
                        if expected == "N/A":
                            # Look for the expected text in quotes
                            expected_match = re.search(r'Expected:\s*["\']([^"\']*)["\']', error_msg)
                            if expected_match:
                                expected = expected_match.group(1)
                        
                        if actual == "N/A":
                            # Look for the actual text in quotes
                            actual_match = re.search(r'Actual:\s*["\']([^"\']*)["\']', error_msg)
                            if actual_match:
                                actual = actual_match.group(1)
                    
                    error_details_html = f"""
                    <div class="error-details">
                        <div class="error-header">
                            <span class="error-icon">⚠️</span>
                            <span class="error-text">View Error Details & Solutions</span>
                            <button class="quick-show-btn" onclick="toggleErrorDetails('error-{tc_id}-{step_num}')">QUICK SHOW</button>
                        </div>
                        <div class="error-content" id="error-{tc_id}-{step_num}" style="display: none;">
                            <div class="error-comparison">
                                <div class="expected-actual">
                                    <div class="expected">
                                        <strong>Expected:</strong>
                                        <span class="value">"{expected}"</span>
                                    </div>
                                    <div class="actual">
                                        <strong>Actual:</strong>
                                        <span class="value">"{actual}"</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    """
                
                steps_html += f"""
                <div class="step-item {step_status}">
                    <div class="step-header">
                        <span class="step-icon">{step_icon}</span>
                        <span class="step-text">{step}</span>
                        <span class="step-time">({step_result if step_result else '0.01s'})</span>
                    </div>
                    <div class="step-execution">Step {step_num} execution</div>
                    {error_details_html}
                </div>
                """
            
            scenarios_html += f"""
            <div class="scenario-item" data-status="{status}">
                <div class="scenario-header" onclick="toggleScenario('{tc_id}')">
                    <div class="scenario-title">
                        <span class="status-icon">{status_icon}</span>
                        <span class="scenario-name">Test Scenario: [{tc_id}]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">({execution_time:.1f}s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-{tc_id}" style="display: none;">
                    <div class="steps-container">
                        {steps_html}
                    </div>
                </div>
            </div>
            """
        
        # Calculate percentages for the donut chart
        total_scenarios = len(detailed_results)
        passed_percentage = (passed_tests / total_scenarios * 100) if total_scenarios > 0 else 0
        failed_percentage = (failed_tests / total_scenarios * 100) if total_scenarios > 0 else 0
        
        # Generate the HTML with the new layout structure
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Rumah Pendidikan Automation Report</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }}
                .container {{
                    max-width: 1400px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    position: relative;
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 2.5em;
                    font-weight: 300;
                }}
                .header h2 {{
                    margin: 10px 0 0 0;
                    font-size: 1.2em;
                    font-weight: 300;
                    opacity: 0.9;
                }}
                .back-button {{
                    position: absolute;
                    left: 30px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    text-decoration: none;
                    font-size: 0.9em;
                    transition: background 0.3s;
                }}
                .back-button:hover {{
                    background: rgba(255,255,255,0.3);
                }}
                .content {{
                    padding: 30px;
                }}
                
                /* Feature Details Section */
                .feature-section {
                    background: #f8f9fa;
                    border-radius: 10px;
                    padding: 25px;
                    margin-bottom: 30px;
                    border-top: 4px solid #dc3545;
                }
                .feature-tags {
                    margin-bottom: 15px;
                    max-height: 60px;
                    overflow-y: auto;
                    overflow-x: hidden;
                    padding: 5px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    background: white;
                }
                .feature-tags::-webkit-scrollbar {
                    width: 6px;
                }
                .feature-tags::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                .feature-tags::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                }
                .feature-tags::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }
                .feature-tags .tag {
                    display: inline-block;
                    background: #e9ecef;
                    color: #495057;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 0.8em;
                    margin-right: 8px;
                    margin-bottom: 5px;
                    white-space: nowrap;
                }
                .feature-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 20px;
                }
                .feature-details {
                    flex: 1;
                }
                .feature-details h2 {
                    margin: 0 0 10px 0;
                    color: #333;
                    font-size: 1.8em;
                }
                .feature-description {
                    margin: 0 0 15px 0;
                    color: #666;
                }
                .feature-info div {
                    margin: 5px 0;
                    color: #666;
                }
                .feature-info strong {
                    color: #333;
                }
                
                /* Scenarios and Metadata Grid */
                .scenarios-overview {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: 30px;
                    margin-bottom: 30px;
                }
                .scenarios-chart {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .scenarios-chart h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .scenarios-chart-wrapper {
                    position: relative;
                    width: 200px;
                    height: 200px;
                    margin: 0 auto 20px;
                }
                .donut-chart {
                    width: 200px;
                    height: 200px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 2.5em;
                    font-weight: bold;
                    color: white;
                    position: relative;
                    background: conic-gradient(#28a745 0deg {passed_percentage * 3.6}deg, #dc3545 {passed_percentage * 3.6}deg 360deg);
                }
                .donut-chart::before {
                    content: '';
                    position: absolute;
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background: white;
                    z-index: 1;
                }
                .donut-chart::after {
                    content: '{total_scenarios}';
                    position: absolute;
                    z-index: 2;
                    color: #333;
                    font-size: 2em;
                    font-weight: bold;
                }
                .scenarios-chart-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                .chart-detail-section h4 {
                    font-size: 14px;
                    color: #6c757d;
                    margin-bottom: 8px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                .status-list, .progress-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }
                .status-list li, .progress-list li {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 5px;
                    font-size: 0.9em;
                }
                .status-list .passed, .progress-list .passed {
                    color: #28a745;
                }
                .status-list .failed, .progress-list .failed {
                    color: #dc3545;
                }
                
                /* Metadata Panel */
                .metadata-panel {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .metadata-panel h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .metadata-grid {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .metadata-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9ecef;
                }
                .metadata-item:last-child {
                    border-bottom: none;
                }
                .metadata-label {
                    font-weight: 500;
                    color: #666;
                }
                .metadata-value {
                    color: #333;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .metadata-value i {
                    font-size: 1.2em;
                }
                
                /* Filter Section */
                .filter-section {
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 30px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .filter-section h3 {
                    margin: 0 0 15px 0;
                    color: #333;
                }
                .filter-buttons {
                    display: flex;
                    gap: 10px;
                }
                .filter-button {
                    background: #e9ecef;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-size: 0.9em;
                    transition: all 0.3s;
                }
                .filter-button.active {
                    background: #667eea;
                    color: white;
                }
                .filter-button:hover {
                    background: #5a6fd8;
                    color: white;
                }
                
                /* Scenarios Section */
                .scenarios-section {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .scenarios-section h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .scenario-item {
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    margin-bottom: 15px;
                    overflow: hidden;
                }
                .scenario-header {
                    background: #f8f9fa;
                    padding: 15px 20px;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    transition: background 0.3s;
                }
                .scenario-header:hover {
                    background: #e9ecef;
                }
                .scenario-title {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    flex: 1;
                    min-width: 0;
                }
                .status-icon {
                    font-size: 1.2em;
                    flex-shrink: 0;
                }
                .scenario-name {
                    font-weight: 500;
                    color: #333;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .scenario-meta {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    flex-shrink: 0;
                    white-space: nowrap;
                }
                .execution-time {
                    color: #666;
                    font-size: 0.9em;
                }
                .arrow {
                    font-size: 0.8em;
                    color: #666;
                    transition: transform 0.3s;
                    flex-shrink: 0;
                    white-space: nowrap;
                }
                .scenario-content {
                    padding: 25px;
                    background: white;
                    border-radius: 0 0 8px 8px;
                }
                .steps-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    max-width: 100%;
                    overflow-x: hidden;
                }
                .step-item {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 25px;
                    border-left: 4px solid #28a745;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                }
                .step-item.failed {
                    border-left-color: #dc3545;
                }
                .step-item.skipped {
                    border-left-color: #ffc107;
                }
                .step-header {
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    margin-bottom: 15px;
                    flex-wrap: wrap;
                }
                .step-icon {
                    font-size: 1.2em;
                    flex-shrink: 0;
                    margin-top: 3px;
                    width: 20px;
                    text-align: center;
                }
                .step-text {
                    flex: 1;
                    font-weight: 500;
                    color: #333;
                    line-height: 1.6;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    hyphens: auto;
                    font-size: 0.95em;
                    margin: 0;
                    padding: 0;
                    min-width: 0;
                    max-width: 100%;
                    text-align: justify;
                    text-justify: inter-word;
                    white-space: normal;
                    word-break: normal;
                    overflow-wrap: anywhere;
                }
                .step-time {
                    color: #666;
                    font-size: 0.85em;
                    flex-shrink: 0;
                    white-space: normal;
                    background: #e9ecef;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-weight: 500;
                    margin-left: 10px;
                    max-width: 200px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                .step-execution {
                    background: #e3f2fd;
                    color: #1976d2;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 0.8em;
                    font-weight: 500;
                    display: inline-block;
                    border: 1px solid #bbdefb;
                    margin-top: 8px;
                    margin-left: 32px;
                }
                .error-details {
                    margin-top: 10px;
                    background: #fff5f5;
                    border: 1px solid #fed7d7;
                    border-radius: 6px;
                    overflow: hidden;
                }
                .error-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 15px;
                    background: #fed7d7;
                    cursor: pointer;
                }
                .error-icon {
                    margin-right: 8px;
                }
                .error-text {
                    flex: 1;
                    font-weight: 500;
                    color: #c53030;
                }
                .quick-show-btn {
                    background: #c53030;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 0.7em;
                    cursor: pointer;
                }
                .quick-show-btn:hover {
                    background: #a02323;
                }
                .error-content {
                    padding: 15px;
                }
                .error-comparison {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .expected-actual {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                .expected, .actual {
                    padding: 10px;
                    border-radius: 4px;
                }
                .expected {
                    background: #f0fff4;
                    border: 1px solid #9ae6b4;
                }
                .actual {
                    background: #fff5f5;
                    border: 1px solid #feb2b2;
                }
                .expected strong, .actual strong {
                    display: block;
                    margin-bottom: 5px;
                    font-size: 0.9em;
                }
                .expected .value, .actual .value {
                    font-family: monospace;
                    background: rgba(0,0,0,0.05);
                    padding: 5px;
                    border-radius: 3px;
                    word-break: break-all;
                }
                .footer {
                    background: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    color: #666;
                    border-top: 1px solid #e9ecef;
                }
                
                /* Tooltip Styles */
                .passed-tooltip, .failed-tooltip {
                    position: fixed;
                    background: rgba(0,0,0,0.9);
                    color: white;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 0.7em;
                    font-weight: 500;
                    white-space: nowrap;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.2s ease;
                    z-index: 1000;
                    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
                    backdrop-filter: blur(4px);
                }
                .passed-tooltip {
                    border-left: 3px solid #28a745;
                }
                .failed-tooltip {
                    border-left: 3px solid #dc3545;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="parallel_comprehensive_{timestamp}.html" class="back-button">
                        <i class="fas fa-arrow-left"></i> Summary
                    </a>
                    <h1>Rumah Pendidikan Automation Report</h1>
                    <h2>Feature: Test Execution</h2>
                </div>
                
                <div class="content">
                    <!-- Feature Details Section -->
                    <div class="feature-section">
                        <div class="feature-tags">
                            {tags_html}
                        </div>
                        <div class="feature-info">
                            <div class="feature-details">
                                <h2>Feature: Test Execution</h2>
                                <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                                <div><strong>File name:</strong> rumdik_regression_test.feature</div>
                            </div>
                            <div class="metadata">
                                <div><strong>Relative path:</strong> test_framework/features/rumdik_regression_test.feature</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scenarios and Metadata Grid -->
                    <div class="scenarios-overview">
                        <div class="scenarios-chart">
                            <h3>Scenarios</h3>
                            <div class="scenarios-chart-wrapper">
                                <div class="donut-chart" onmouseover="handleDonutHover(event, {passed_tests}, {failed_tests})" onmouseout="hideTooltip()"></div>
                            </div>
                            <div class="scenarios-chart-details">
                                <div class="chart-detail-section">
                                    <h4>Status</h4>
                                    <ul class="status-list">
                                        <li class="passed">✅ Passed</li>
                                        <li class="failed">❌ Failed</li>
                                    </ul>
                                </div>
                                <div class="chart-detail-section">
                                    <h4>Progress</h4>
                                    <ul class="progress-list">
                                        <li class="passed">✅ {passed_percentage:.1f}%</li>
                                        <li class="failed">❌ {failed_percentage:.1f}%</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metadata-panel">
                            <h3>Metadata</h3>
                            <div class="metadata-grid">
                                <div class="metadata-item">
                                    <div class="metadata-label">Device</div>
                                    <div class="metadata-value">Runner Machine</div>
                                </div>
                                <div class="metadata-item">
                                    <div class="metadata-label">OS</div>
                                    <div class="metadata-value">
                                        <i class="fab fa-apple"></i>
                                        macOS
                                    </div>
                                </div>
                                <div class="metadata-item">
                                    <div class="metadata-label">Browser</div>
                                    <div class="metadata-value">
                                        <i class="fab fa-chrome"></i>
                                        Chrome 103
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tooltip Elements -->
                    <div id="passed-tooltip" class="passed-tooltip"></div>
                    <div id="failed-tooltip" class="failed-tooltip"></div>
                    
                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3>Filter Scenarios</h3>
                        <div class="filter-buttons">
                            <button class="filter-button active" onclick="filterScenarios('all')">All ({total_scenarios})</button>
                            <button class="filter-button" onclick="filterScenarios('passed')">Passed ({passed_tests})</button>
                            <button class="filter-button" onclick="filterScenarios('failed')">Failed ({failed_tests})</button>
                        </div>
                    </div>
                    
                    <!-- Scenarios Section -->
                    <div class="scenarios-section">
                        <h3>Scenarios</h3>
                        {scenarios_html}
                    </div>
                </div>
                
                <div class="footer">
                    Maintained by Test Automation Team.
                </div>
            </div>
            
            <script>
                function toggleScenario(tcId) {{
                    const content = document.getElementById(`scenario-${{tcId}}`);
                    const arrow = content.previousElementSibling.querySelector('.arrow');
                    
                    if (content.style.display === 'none' || content.style.display === '') {{
                        content.style.display = 'block';
                        arrow.textContent = '▲';
                    }} else {{
                        content.style.display = 'none';
                        arrow.textContent = '▼';
                    }}
                }}
                
                function filterScenarios(status) {{
                    const scenarios = document.querySelectorAll('.scenario-item');
                    const buttons = document.querySelectorAll('.filter-button');
                    
                    // Update active button
                    buttons.forEach(btn => btn.classList.remove('active'));
                    event.target.classList.add('active');
                    
                    scenarios.forEach(scenario => {{
                        const scenarioStatus = scenario.getAttribute('data-status');
                        if (status === 'all' || scenarioStatus === status) {{
                            scenario.style.display = 'block';
                        }} else {{
                            scenario.style.display = 'none';
                        }}
                    }});
                }}
                
                function toggleErrorDetails(errorId) {{
                    const content = document.getElementById(errorId);
                    if (content.style.display === 'none' || content.style.display === '') {{
                        content.style.display = 'block';
                    }} else {{
                        content.style.display = 'none';
                    }}
                }}
                
                function showPassedTooltip(event, passedText) {{
                    const tooltip = document.getElementById('passed-tooltip');
                    tooltip.innerHTML = passedText;
                    tooltip.style.left = (event.clientX + 10) + 'px';
                    tooltip.style.top = (event.clientY - 30) + 'px';
                    tooltip.style.opacity = '1';
                }}
                
                function showFailedTooltip(event, failedText) {{
                    const tooltip = document.getElementById('failed-tooltip');
                    tooltip.innerHTML = failedText;
                    tooltip.style.left = (event.clientX + 10) + 'px';
                    tooltip.style.top = (event.clientY - 30) + 'px';
                    tooltip.style.opacity = '1';
                }}
                
                function hideTooltip() {{
                    document.getElementById('passed-tooltip').style.opacity = '0';
                    document.getElementById('failed-tooltip').style.opacity = '0';
                }}
                
                function handleDonutHover(event, passedTests, failedTests) {{
                    const rect = event.target.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    const mouseX = event.clientX;
                    const mouseY = event.clientY;
                    
                    // Calculate angle from center to mouse position
                    const deltaX = mouseX - centerX;
                    const deltaY = centerY - mouseY; // Invert Y axis
                    const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
                    const normalizedAngle = (angle + 360) % 360;
                    
                    // Calculate passed section angle
                    const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
                    
                    // Determine which section is being hovered
                    if (normalizedAngle <= passedAngle) {{
                        // Hovering over passed section (green)
                        showPassedTooltip(event, `Passed: ${{passedTests}}`);
                        document.getElementById('failed-tooltip').style.opacity = '0';
                    }} else {{
                        // Hovering over failed section (red)
                        showFailedTooltip(event, `Failed: ${{failedTests}}`);
                        document.getElementById('passed-tooltip').style.opacity = '0';
                    }}
                }}
            </script>
        </body>
        </html>
        """
        
        return html_content
    
    def _generate_comprehensive_html_report(self, summary: Dict[str, Any]) -> str:
        """Generate comprehensive HTML report content"""
        exec_summary = summary["execution_summary"]
        step_summary = summary["step_summary"]
        perf_metrics = summary["performance_metrics"]
        detailed_results = summary["detailed_results"]
        
        # Calculate statistics
        total_tests = len(detailed_results)
        passed_tests = sum(1 for r in detailed_results if r['status'] == 'passed')
        failed_tests = sum(1 for r in detailed_results if r['status'] == 'failed')
        skipped_tests = sum(1 for r in detailed_results if r['status'] == 'skipped')
        error_tests = sum(1 for r in detailed_results if r['status'] == 'error')
        timeout_tests = sum(1 for r in detailed_results if r['status'] == 'timeout')
        
        # Generate test results table
        test_results_html = ""
        for result in detailed_results:
            status_icon = {
                'passed': '✅',
                'failed': '❌',
                'skipped': '⏭️',
                'error': '💥',
                'timeout': '⏰'
            }.get(result['status'], '❓')
            
            test_results_html += f"""
            <tr>
                <td>{result['test_case']}</td>
                <td>{status_icon} {result['status'].upper()}</td>
                <td>{result['execution_time']:.1f}s</td>
                <td>{result['steps_passed']}</td>
                <td>{result['steps_failed']}</td>
                <td>{result['steps_skipped']}</td>
                <td>{result['total_steps']}</td>
                <td>{result['error_message'] if result['error_message'] else '-'}</td>
            </tr>
            """
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rumah Pendidikan Automation Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header h2 {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }}
        .summary-cards {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .card {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }}
        .card h3 {{
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }}
        .donut-chart {{
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg {passed_tests/total_tests*360}deg, #dc3545 {passed_tests/total_tests*360}deg 360deg);
        }}
        .donut-chart::before {{
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }}
        .donut-chart::after {{
            content: '{total_tests}';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 1.5em;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }}
        .donut-chart.passed {{
            background: conic-gradient(#28a745 0deg 360deg);
        }}
        .donut-chart.failed {{
            background: conic-gradient(#dc3545 0deg 360deg);
        }}
        .donut-chart.mixed {{
            background: conic-gradient(#28a745 0deg {passed_tests/total_tests*360}deg, #dc3545 {passed_tests/total_tests*360}deg 360deg);
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }}
        .stat-item {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }}
        .stat-label {{
            font-weight: 500;
            color: #666;
        }}
        .stat-value {{
            font-weight: bold;
            color: #333;
        }}
        .test-results {{
            padding: 30px;
        }}
        .test-results h3 {{
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }}
        th, td {{
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }}
        th {{
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }}
        tr:hover {{
            background: #f8f9fa;
        }}
        .status-passed {{ color: #28a745; font-weight: bold; }}
        .status-failed {{ color: #dc3545; font-weight: bold; }}
        .status-skipped {{ color: #ffc107; font-weight: bold; }}
        .status-error {{ color: #dc3545; font-weight: bold; }}
        .status-timeout {{ color: #fd7e14; font-weight: bold; }}
        .footer {{
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }}
        .social-icons {{
            margin-top: 10px;
        }}
        .social-icons a {{
            margin: 0 10px;
            color: #666;
            text-decoration: none;
            font-size: 1.2em;
        }}
        
        /* Error Details Styles */
        .error-details {{
            margin-top: 10px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            overflow: hidden;
        }}
        .error-header {{
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            background: #fed7d7;
            border-left: 4px solid #e53e3e;
        }}
        .error-icon {{
            font-size: 1.1em;
        }}
        .error-text {{
            flex: 1;
            font-weight: 600;
            color: #c53030;
        }}
        .quick-show-btn {{
            background: #38a169;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }}
        .quick-show-btn:hover {{
            background: #2f855a;
        }}
        .error-content {{
            padding: 15px;
            background: white;
        }}
        .error-comparison {{
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }}
        .expected-actual {{
            display: flex;
            flex-direction: column;
            gap: 10px;
        }}
        .expected, .actual {{
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .expected {{
            background: #f0fff4;
            border-bottom: 1px solid #e2e8f0;
        }}
        .actual {{
            background: #fff5f5;
        }}
        .expected strong, .actual strong {{
            min-width: 80px;
            font-size: 0.9em;
        }}
        .expected .value {{
            color: #38a169;
            font-family: monospace;
            background: #f0fff4;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #c6f6d5;
        }}
        .actual .value {{
            color: #e53e3e;
            font-family: monospace;
            background: #fed7d7;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #feb2b2;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <h3>Features</h3>
                <div class="donut-chart {'donut-passed' if failed_tests == 0 else 'donut-mixed'}">
                    {total_tests}
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{passed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{failed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{skipped_tests/total_tests*100:.1f}%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Scenarios</h3>
                <div class="donut-chart {'donut-passed' if failed_tests == 0 else 'donut-mixed'}">
                    {total_tests}
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{passed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{failed_tests/total_tests*100:.1f}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">{skipped_tests/total_tests*100:.1f}%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Run Info</h3>
                <div style="text-align: left; margin-top: 20px;">
                    <div class="stat-item">
                        <span class="stat-label">🔗 Project</span>
                        <span class="stat-value">Rumah Pendidikan Automation Report</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">🕐 Generation Time</span>
                        <span class="stat-value">{datetime.now().isoformat()}Z</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">👤 OS User</span>
                        <span class="stat-value">{os.getenv('USER', 'unknown')}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">⏱️ Duration</span>
                        <span class="stat-value">{exec_summary['total_execution_time']:.0f} Seconds</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>Features Overview</h3>
            <table>
                <thead>
                    <tr>
                        <th>Feature Name</th>
                        <th>Status</th>
                        <th>Device</th>
                        <th>OS</th>
                        <th>Browser</th>
                        <th>Total</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Undefined</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="color: #007bff; font-weight: 500;">Parallel Test Execution</td>
                        <td>✅</td>
                        <td>🖥️ Runner Machine</td>
                        <td>🐧 linux</td>
                        <td>🌐 103</td>
                        <td>{total_tests}</td>
                        <td>{passed_tests}</td>
                        <td>{failed_tests}</td>
                        <td>{skipped_tests + error_tests + timeout_tests}</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 style="margin-top: 30px;">Detailed Test Results</h3>
            <table>
                <thead>
                    <tr>
                        <th>Test Case</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Steps Passed</th>
                        <th>Steps Failed</th>
                        <th>Steps Skipped</th>
                        <th>Total Steps</th>
                        <th>Error Message</th>
                    </tr>
                </thead>
                <tbody>
                    {test_results_html}
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team.</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html

    def _get_actual_steps_from_report(self, tc_id: str, result: TestResult) -> List[tuple]:
        """Get actual steps and their status from the individual test report"""
        import re
        import glob
        import os
        import time
        
        try:
            # Try to find the report file for this test case
            if hasattr(result, 'report_file') and result.report_file:
                report_file = result.report_file
            else:
                # Look for reports that contain this specific test case
                report_pattern = f"test_framework/reports/test_report_*.html"
                reports = glob.glob(report_pattern)
                if not reports:
                    return []
                
                # Sort reports by creation time (newest first)
                reports.sort(key=os.path.getctime, reverse=True)
                
                # Find the report that contains this specific test case
                report_file = None
                for report in reports[:20]:  # Check the 20 most recent reports
                    try:
                        with open(report, 'r', encoding='utf-8') as f:
                            report_content = f.read()
                        
                        # Check if this report contains the test case
                        test_case_pattern = rf'<div class="scenario-title">Test Scenario: \[{re.escape(tc_id)}\].*?</div>'
                        if re.search(test_case_pattern, report_content, re.DOTALL | re.IGNORECASE):
                            report_file = report
                            print(f"  ✅ Found report for {tc_id}: {os.path.basename(report)}")
                            break
                    except Exception as e:
                        continue
                
                if not report_file:
                    print(f"  ⚠️  Report file not found for {tc_id} after checking {min(20, len(reports))} reports")
                    return []
            
            # Check if the report file exists
            if not os.path.exists(report_file):
                print(f"  ⚠️  Report file not found: {report_file}")
                return []
            
            # Read the report content
            with open(report_file, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            # Find the test case section using the correct HTML structure
            # The pattern is: <div class="scenario-title">Test Scenario: [TC1] Pencarian</div>
            test_case_pattern = rf'<div class="scenario-title">Test Scenario: \[{re.escape(tc_id)}\].*?</div>'
            test_case_match = re.search(test_case_pattern, report_content, re.DOTALL | re.IGNORECASE)
            
            if not test_case_match:
                print(f"  ⚠️  Test case {tc_id} not found in report")
                return []
            
            # Find the scenario content section that follows this test case
            test_case_start = test_case_match.start()
            remaining_content = report_content[test_case_start:]
            
            # Look for the scenario-content div
            scenario_content_pattern = r'<div class="scenario-content"[^>]*>'
            scenario_content_match = re.search(scenario_content_pattern, remaining_content)
            
            if not scenario_content_match:
                print(f"  ⚠️  Scenario content not found for {tc_id}")
                return []
            
            # Extract the scenario content section
            scenario_start = scenario_content_match.start()
            scenario_content = remaining_content[scenario_start:]
            
            # Find the closing div for scenario-content
            # Look for the next closing div that matches the opening
            depth = 0
            end_pos = 0
            for i, char in enumerate(scenario_content):
                if char == '<':
                    if scenario_content[i:i+2] == '</':
                        if scenario_content[i:i+len('</div>')] == '</div>':
                            if depth == 0:
                                end_pos = i
                                break
                            depth -= 1
                    elif scenario_content[i:i+len('<div')] == '<div':
                        depth += 1
            
            if end_pos > 0:
                scenario_content = scenario_content[:end_pos]
            
            # Extract step text and status from the section
            steps_and_results = []
            
            # Find all step items in this test case's section
            # Pattern: <div class="step-item (passed|failed|skipped)>...<div class="step-text">Step text</div>...
            step_pattern = r'<div class="step-item (passed|failed|skipped)">.*?<div class="step-text">(.*?)</div>'
            step_matches = re.findall(step_pattern, scenario_content, re.DOTALL)
            
            # Also extract error messages for failed steps
            error_pattern = r'<div class="step-item failed">.*?<div class="step-text">(.*?)</div>.*?<strong>Error:</strong>\s*(.*?)(?=<br>|<div|$)'
            error_matches = re.findall(error_pattern, scenario_content, re.DOTALL)
            
            # Create a mapping of step text to error message
            error_map = {}
            for step_text, error_msg in error_matches:
                # Clean up the step text
                step_text = re.sub(r'<[^>]+>', '', step_text).strip()
                step_text = step_text.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                # Clean up the error message
                error_msg = re.sub(r'<[^>]+>', '', error_msg).strip()
                error_msg = error_msg.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                error_map[step_text] = error_msg
            
            # Also try to extract error messages from the error details section
            error_details_pattern = r'<strong>Error:</strong>\s*(.*?)(?=<br>|<div|$)'
            error_details_matches = re.findall(error_details_pattern, scenario_content, re.DOTALL)
            
            # If we found error details but no step-specific errors, use the general error
            if error_details_matches and not error_map:
                general_error = error_details_matches[0]
                general_error = re.sub(r'<[^>]+>', '', general_error).strip()
                general_error = general_error.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                # Find the failed step and assign the error to it
                for step_status, step_text in step_matches:
                    if step_status == 'failed':
                        step_text_clean = re.sub(r'<[^>]+>', '', step_text).strip()
                        step_text_clean = step_text_clean.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                        error_map[step_text_clean] = general_error
                        break
            
            for step_status, step_text in step_matches:
                # Clean up the step text (remove HTML entities and extra whitespace)
                step_text = re.sub(r'<[^>]+>', '', step_text).strip()
                step_text = step_text.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                
                # Add error message if this step failed
                error_msg = error_map.get(step_text, '')
                steps_and_results.append((step_text, step_status, error_msg))
            
            print(f"  📊 Extracted {len(steps_and_results)} actual steps for {tc_id}")
            return steps_and_results
            
        except Exception as e:
            print(f"  ❌ Error extracting actual steps for {tc_id}: {str(e)}")
            return []
    
    def _get_gherkin_steps_for_tc(self, tc_id: str) -> List[str]:
        """Get the original Gherkin steps for a specific test case from the feature file"""
        import re
        import glob
        import os
        
        try:
            # Look for feature files
            feature_pattern = "test_framework/features/*.feature"
            feature_files = glob.glob(feature_pattern)
            
            if not feature_files:
                print(f"  ⚠️  No feature files found")
                return []
            
            # Use the first feature file found
            feature_file = feature_files[0]
            
            with open(feature_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find the scenario section for this test case
            # Look for @TC1, @TC2, etc. followed by Scenario or Scenario Outline
            scenario_pattern = rf'@{re.escape(tc_id)}.*?Scenario.*?:(.*?)(?=@TC|\Z)'
            scenario_match = re.search(scenario_pattern, content, re.DOTALL | re.IGNORECASE)
            
            if not scenario_match:
                print(f"  ⚠️  Scenario for {tc_id} not found in feature file")
                return []
            
            scenario_content = scenario_match.group(1)
            
            # Extract steps (Given, When, Then, And, But)
            step_pattern = r'^\s*(Given|When|Then|And|But)\s+(.+)$'
            steps = re.findall(step_pattern, scenario_content, re.MULTILINE | re.IGNORECASE)
            
            # Clean up steps
            cleaned_steps = []
            for step_type, step_text in steps:
                step_text = step_text.strip()
                if step_text:
                    cleaned_steps.append(f"{step_type} {step_text}")
            
            print(f"  📝 Found {len(cleaned_steps)} steps for {tc_id}: {cleaned_steps}")
            return cleaned_steps
            
        except Exception as e:
            print(f"  ❌ Error getting Gherkin steps for {tc_id}: {str(e)}")
        return []
    
    def _get_all_available_test_cases(self) -> List[str]:
        """Get all available test cases from the feature file"""
        import re
        import glob
        import os
        
        try:
            # Look for feature files
            feature_pattern = "test_framework/features/*.feature"
            feature_files = glob.glob(feature_pattern)
            
            if not feature_files:
                return []
            
            # Use the first feature file found
            feature_file = feature_files[0]
            
            with open(feature_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract all TC IDs from the feature file
            # Look for @TC1, @TC2, etc. format
            tc_pattern = r'@(TC\d+)'
            tc_matches = re.findall(tc_pattern, content)
            
            # Sort numerically
            tc_matches.sort(key=lambda x: int(x[2:]))  # Extract number from TC1, TC2, etc.
            
            return tc_matches
            
        except Exception as e:
            print(f"⚠️ Error getting all available test cases: {e}")
            return []
    
    def _generate_parallel_summary_report(self, summary: Dict[str, Any]):
        """Generate a custom parallel execution summary report"""
        try:
            report_file = f"test_framework/reports/parallel_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            
            html_content = self._generate_html_report(summary)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"📊 Parallel Summary Report Generated: {report_file}")
            
        except Exception as e:
            print(f"⚠️ Could not generate parallel summary report: {e}")
    
    def _generate_html_report(self, summary: Dict[str, Any]) -> str:
        """Generate HTML content for comprehensive report"""
        exec_summary = summary["execution_summary"]
        step_summary = summary["step_summary"]
        perf_metrics = summary["performance_metrics"]
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parallel Test Execution Report</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 2.5em; }}
        .header p {{ margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 30px; }}
        .metric-card {{ background: #f8f9fa; border-radius: 10px; padding: 20px; text-align: center; border-left: 5px solid #667eea; }}
        .metric-card h3 {{ margin: 0 0 10px 0; color: #333; }}
        .metric-card .value {{ font-size: 2em; font-weight: bold; color: #667eea; }}
        .metric-card .label {{ color: #666; font-size: 0.9em; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .danger {{ color: #dc3545; }}
        .results-table {{ margin: 30px; }}
        .results-table table {{ width: 100%; border-collapse: collapse; }}
        .results-table th, .results-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        .results-table th {{ background: #f8f9fa; font-weight: bold; }}
        .status-passed {{ color: #28a745; font-weight: bold; }}
        .status-failed {{ color: #dc3545; font-weight: bold; }}
        .status-skipped {{ color: #ffc107; font-weight: bold; }}
        .status-error {{ color: #dc3545; font-weight: bold; }}
        .error-message {{ 
            background: white; 
            padding: 15px; 
            border-radius: 6px; 
            margin-bottom: 15px; 
            font-family: 'Courier New', monospace; 
            white-space: pre-wrap; 
            border: 1px solid #e2e8f0; 
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); 
            font-size: 13px; 
            line-height: 1.5; 
            text-align: left; 
        }}
        .performance {{ background: #e3f2fd; padding: 20px; margin: 30px; border-radius: 10px; }}
        .performance h3 {{ color: #1976d2; margin-top: 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Parallel Test Execution Report</h1>
            <p>Massive Performance Improvement for Large Test Suites</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>Total Tests</h3>
                <div class="value">{exec_summary['total_tests']}</div>
                <div class="label">Test Cases Executed</div>
            </div>
            <div class="metric-card">
                <h3>Success Rate</h3>
                <div class="value success">{exec_summary['success_rate']:.1f}%</div>
                <div class="label">Tests Passed</div>
            </div>
            <div class="metric-card">
                <h3>Execution Time</h3>
                <div class="value">{exec_summary['total_execution_time']:.1f}s</div>
                <div class="label">Total Duration</div>
            </div>
            <div class="metric-card">
                <h3>Performance</h3>
                <div class="value">{exec_summary['tests_per_minute']:.1f}/min</div>
                <div class="label">Tests Per Minute</div>
            </div>
            <div class="metric-card">
                <h3>Efficiency Gain</h3>
                <div class="value success">{perf_metrics['efficiency_improvement']:.1f}%</div>
                <div class="label">Time Saved</div>
            </div>
            <div class="metric-card">
                <h3>Parallel Workers</h3>
                <div class="value">{perf_metrics['parallel_workers']}</div>
                <div class="label">Concurrent Executions</div>
            </div>
        </div>
        
        <div class="performance">
            <h3>🎯 Performance Analysis</h3>
            <p><strong>Sequential Execution Estimate:</strong> {exec_summary['total_tests'] * 34.3:.1f} seconds</p>
            <p><strong>Parallel Execution Time:</strong> {exec_summary['total_execution_time']:.1f} seconds</p>
            <p><strong>Time Saved:</strong> {(exec_summary['total_tests'] * 34.3) - exec_summary['total_execution_time']:.1f} seconds</p>
            <p><strong>Efficiency Improvement:</strong> {perf_metrics['efficiency_improvement']:.1f}%</p>
        </div>
        
        <div class="results-table">
            <h3>📊 Detailed Test Results</h3>
            <table>
                <thead>
                    <tr>
                        <th>Test Case</th>
                        <th>Status</th>
                        <th>Execution Time</th>
                        <th>Steps Passed</th>
                        <th>Steps Failed</th>
                        <th>Steps Skipped</th>
                        <th>Total Steps</th>
                    </tr>
                </thead>
                <tbody>
"""
        
        for result in summary["detailed_results"]:
            status_class = f"status-{result['status']}"
            html += f"""
                    <tr>
                        <td>{result['test_case']}</td>
                        <td class="{status_class}">{result['status'].upper()}</td>
                        <td>{result['execution_time']:.1f}s</td>
                        <td>{result['steps_passed']}</td>
                        <td>{result['steps_failed']}</td>
                        <td>{result['steps_skipped']}</td>
                        <td>{result['total_steps']}</td>
                    </tr>
"""
        
        html += """
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
"""
        
        return html

    def _get_step_results_for_tc(self, tc_id: str, result: TestResult) -> Dict[int, str]:
        """Get individual step results for a specific test case from the HTML report"""
        import re
        import glob
        import os
        
        try:
            # Try to find the report file for this test case
            if hasattr(result, 'report_file') and result.report_file:
                report_file = result.report_file
            else:
                # Look for the latest report
                report_pattern = f"test_framework/reports/test_report_*.html"
                reports = glob.glob(report_pattern)
                if not reports:
                    return {}
                report_file = max(reports, key=os.path.getctime)
            
            # Check if the report file exists
            if not os.path.exists(report_file):
                print(f"  ⚠️  Report file not found: {report_file}")
                return {}
            
            # Read the report content
            with open(report_file, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            # Find the test case section using the correct HTML structure
            # The pattern is: <div class="scenario-title">Test Scenario: [TC1] Pencarian</div>
            test_case_pattern = rf'<div class="scenario-title">Test Scenario: \[{re.escape(tc_id)}\].*?</div>'
            test_case_match = re.search(test_case_pattern, report_content, re.DOTALL | re.IGNORECASE)
            
            if not test_case_match:
                print(f"  ⚠️  Test case {tc_id} not found in report")
                return {}
            
            # Find the scenario content section that follows this test case
            test_case_start = test_case_match.start()
            remaining_content = report_content[test_case_start:]
            
            # Look for the scenario-content div
            scenario_content_pattern = r'<div class="scenario-content"[^>]*>'
            scenario_content_match = re.search(scenario_content_pattern, remaining_content)
            
            if not scenario_content_match:
                print(f"  ⚠️  Scenario content not found for {tc_id}")
                return {}
            
            # Extract the scenario content section
            scenario_start = scenario_content_match.start()
            scenario_content = remaining_content[scenario_start:]
            
            # Find the closing div for scenario-content
            # Look for the next closing div that matches the opening
            depth = 0
            end_pos = 0
            for i, char in enumerate(scenario_content):
                if char == '<':
                    if scenario_content[i:i+2] == '</':
                        if scenario_content[i:i+len('</div>')] == '</div>':
                            if depth == 0:
                                end_pos = i
                                break
                            depth -= 1
                    elif scenario_content[i:i+len('<div')] == '<div':
                        depth += 1
            
            if end_pos > 0:
                scenario_content = scenario_content[:end_pos]
            
            # Extract step results from the section
            step_results = {}
            
            # Find all step items in this test case's section
            # The pattern should match: <div class="step-item passed"> or <div class="step-item failed"> or <div class="step-item skipped">
            step_matches = re.findall(r'<div class="step-item (passed|failed|skipped)">', scenario_content)
            
            for i, step_status in enumerate(step_matches):
                step_results[i] = step_status
            
            print(f"  📊 Extracted {len(step_results)} step results for {tc_id}: {step_results}")
            return step_results
            
        except Exception as e:
            print(f"  ❌ Error extracting step results for {tc_id}: {str(e)}")
            return {}


async def main():
    """Main function for parallel test execution"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description="Parallel Test Executor")
    parser.add_argument("--test-cases", required=True, help="Comma-separated list of test cases (e.g., TC1,TC2,TC3)")
    parser.add_argument("--mode", default="gherkin", choices=["gherkin", "excel"], help="Test mode (default: gherkin)")
    parser.add_argument("--max-workers", type=int, help="Maximum number of parallel workers")
    parser.add_argument("--chunk-size", type=int, default=5, help="Chunk size for parallel execution (default: 5)")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Split test cases
    test_cases = [tc.strip() for tc in args.test_cases.split(',')]
    
    # Create executor
    executor = OptimizedParallelTestExecutor(max_workers=args.max_workers, chunk_size=args.chunk_size)
    
    # Run tests
    summary = await executor.run_all_tests(test_cases, mode=args.mode)
    
    # Print summary
    print("\n" + "="*60)
    print("🎯 PARALLEL EXECUTION SUMMARY")
    print("="*60)
    print(f"Total Tests: {summary['execution_summary']['total_tests']}")
    print(f"Success Rate: {summary['execution_summary']['success_rate']:.1f}%")
    print(f"Execution Time: {summary['execution_summary']['total_execution_time']:.1f} seconds")
    print(f"Tests Per Minute: {summary['execution_summary']['tests_per_minute']:.1f}")
    print(f"Efficiency Improvement: {summary['performance_metrics']['efficiency_improvement']:.1f}%")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main()) 