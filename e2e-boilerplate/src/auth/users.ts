import { resolve } from 'app-root-path'
import { config } from 'dotenv'

config({ path: resolve('.env') })

/**
 * This file contains the list of users that will be used for testing
 * Update this file with your own users data to test based on .env file
 */

export const USERS_TESTING = {
  GURU_SATU: {
    email: process.env.USER_EMAIL_GURU_SATU,
    password: process.env.USER_PASSWORD_GURU_SATU,
  },
  GURU_EMPAT: {
    email: process.env.USER_EMAIL_GURU_EMPAT,
    password: process.env.USER_PASSWORD_GURU_EMPAT,
  },
  IAP: {
    email: process.env.USER_EMAIL_IAP,
    password: process.env.USER_PASSWORD_IAP,
  },
}
