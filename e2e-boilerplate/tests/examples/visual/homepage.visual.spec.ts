import { scrollDownVisitAllContent } from '@/helpers/visual.helper'
import { expect, test } from '@playwright/test'

// This test case is example of how to test the whole page.
test('@visual @production @anonym should match the homepage snapshot', async ({ page }) => {
  // Go to the homepage
  await page.goto('/')

  // Scroll down to the bottom of the page
  // this is for pages that have scroll animations
  await scrollDownVisitAllContent(page)

  await expect(page).toHaveScreenshot({
    threshold: 0.1,
    maxDiffPixelRatio: 0.1,
    fullPage: true,
  })
})

// This test case is example of how to test a specific element
// on the page. In this case, we are testing the footer.
// This is useful when you want to test a specific element on the page
// and not the whole page.
test('@visual @staging @production @anonym footer should match the snapshot', async ({ page }) => {
  // Go to the homepage
  await page.goto('/')

  expect(
    await page.locator('footer').screenshot({
      // this property is used to mask the nav element @see https://playwright.dev/docs/api/class-locatorassertions#locator-assertions-to-have-screenshot-1-option-mask
      mask: [page.locator('nav')],
    })
  ).toMatchSnapshot({
    threshold: 0.1,
    maxDiffPixelRatio: 0.1,
  })
})

// This test case is example of how to test a specific element
// on the page. In this case, we are testing only static content on the page.
// This will ignore any dynamic content on the page.
test('@visual @production should match the snapshot akun', async ({ page }) => {
  // Go to the akun page
  await page.goto('/akun')

  await expect(page).toHaveScreenshot({
    mask: [
      page.locator('xpath=//*[@id="__next"]/div/div/div/div/div[1]/div/div[1]/span/img'),
      page.locator('xpath=//*[@id="__next"]/div/div/div/div/div[1]/div/div[2]/p[1]'),
      page.locator('xpath=//*[@id="__next"]/div/div/div/div/div[1]/div/div[2]/p[2]'),
      page.locator('xpath=//*[@id="__next"]/div/div/div/nav/ul/li[5]/a/div/div'),
    ],
    threshold: 0.1,
    maxDiffPixelRatio: 0.1,
    fullPage: true,
  })
})
