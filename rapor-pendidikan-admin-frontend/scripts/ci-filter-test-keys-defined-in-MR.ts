import axios from 'axios'
import fs from 'fs'

/**
 * This script is used to filter the test runs that are defined in the MR description.
 * Note: This script is used only in the CI pipeline.
 */

/**
 * Development:
    export CI_PROJECT_ID=45925326
    export CI_MERGE_REQUEST_IID=
    export GITLAB_TOKEN=glpat-yPihFdjzQz
 *
 * Run:
    npx tsx scripts/ci-filter-test-keys-defined-in-MR.ts
 */

export async function runFilterTestRunsOnMR() {
  // 0. Get current MR ID from env
  const MR_ID = process.env.CI_MERGE_REQUEST_IID

  const isDefaultBranch = process.env.CI_COMMIT_BRANCH === process.env.CI_DEFAULT_BRANCH
  if (isDefaultBranch) {
    console.info('In default branch, not filtering the test cases..')
    return []
  }

  if (!MR_ID) {
    console.error('Missing CI_MERGE_REQUEST_IID, not filtering the test cases..')
    return []
  }

  // 1. fetch API from gitlab MR description
  const url = `https://gitlab.com/api/v4/projects/${process.env.CI_PROJECT_ID}/merge_requests/${MR_ID}`

  // 2. parse the Jira keys
  try {
    const response = await axios.get(url, {
      headers: {
        'PRIVATE-TOKEN': process.env.GITLAB_TOKEN || process.env.COMMENTER_TOKEN,
      },
    })

    const { description } = response.data

    const jiraKeys = description.match(/([A-Z]+-\d+)/g)

    // 3. write the Jira keys to a file name: jira-keys-defined-in-MR-description.txt
    fs.writeFileSync('jira-keys-defined-in-MR-description.txt', jiraKeys.join('|'))
  } catch (error) {
    console.error(error)
    return []
  }
}

;(async function () {
  await runFilterTestRunsOnMR()
})()
