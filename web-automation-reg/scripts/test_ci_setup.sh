#!/bin/bash

# Test CI Setup Script for Web Automation Reg
# Simulates CI environment and tests the setup locally

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST-CI]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to simulate CI environment
simulate_ci_environment() {
    print_status "Simulating CI environment..."
    
    # Set CI environment variables
    export CI=true
    export GITLAB_CI=true
    export CI_PIPELINE_ID="test-pipeline-123"
    export CI_JOB_ID="test-job-456"
    export CI_COMMIT_SHA="abc123def456"
    export CI_COMMIT_REF_NAME="develop"
    
    # Set test environment variables
    export XRAY_ENABLED=true
    export XRAY_BASE_URL="https://wartek.atlassian.net"
    export XRAY_PROJECT_KEY="RP"
    export XRAY_ENVIRONMENT="staging"
    
    print_success "CI environment simulation completed"
}

# Function to test prerequisites
test_prerequisites() {
    print_status "Testing prerequisites..."
    
    local errors=0
    
    # Check Python
    if ! command -v python3 >/dev/null 2>&1; then
        print_error "Python 3 not found"
        errors=$((errors + 1))
    else
        print_success "Python 3 found: $(python3 --version)"
    fi
    
    # Check pip
    if ! command -v pip3 >/dev/null 2>&1; then
        print_error "pip3 not found"
        errors=$((errors + 1))
    else
        print_success "pip3 found: $(pip3 --version)"
    fi
    
    # Check required files
    local required_files=("run_scaled.sh" "requirements.txt" "test_framework/parallel_executor.py")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            errors=$((errors + 1))
        else
            print_success "Required file found: $file"
        fi
    done
    
    # Check required directories
    local required_dirs=("test_framework" "test_framework/features" "test_framework/modules")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            print_error "Required directory not found: $dir"
            errors=$((errors + 1))
        else
            print_success "Required directory found: $dir"
        fi
    done
    
    if [ $errors -eq 0 ]; then
        print_success "All prerequisites check passed"
        return 0
    else
        print_error "$errors prerequisite checks failed"
        return 1
    fi
}

# Function to test Python dependencies
test_python_dependencies() {
    print_status "Testing Python dependencies..."
    
    local errors=0
    
    # Test core dependencies
    local dependencies=("playwright" "requests" "yaml" "lxml")
    for dep in "${dependencies[@]}"; do
        if python3 -c "import $dep" 2>/dev/null; then
            print_success "Python dependency available: $dep"
        else
            print_error "Python dependency missing: $dep"
            errors=$((errors + 1))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        print_success "All Python dependencies available"
        return 0
    else
        print_warning "$errors Python dependencies missing (will be installed in CI)"
        return 0  # Non-blocking for local testing
    fi
}

# Function to test CI setup script
test_ci_setup_script() {
    print_status "Testing CI setup script..."
    
    if [ ! -f "scripts/ci_setup.sh" ]; then
        print_error "CI setup script not found: scripts/ci_setup.sh"
        return 1
    fi
    
    if [ ! -x "scripts/ci_setup.sh" ]; then
        print_error "CI setup script is not executable"
        return 1
    fi
    
    # Run CI setup script
    if ./scripts/ci_setup.sh; then
        print_success "CI setup script executed successfully"
        
        # Check if environment file was created
        if [ -f "ci_environment.env" ]; then
            print_success "CI environment file created"
            print_status "Environment variables:"
            cat ci_environment.env | head -10
        else
            print_warning "CI environment file not created"
        fi
        
        return 0
    else
        print_error "CI setup script failed"
        return 1
    fi
}

# Function to test run_scaled.sh script
test_run_scaled_script() {
    print_status "Testing run_scaled.sh script..."
    
    if [ ! -f "run_scaled.sh" ]; then
        print_error "run_scaled.sh not found"
        return 1
    fi
    
    if [ ! -x "run_scaled.sh" ]; then
        print_warning "run_scaled.sh is not executable, fixing..."
        chmod +x run_scaled.sh
    fi
    
    # Test script help
    if ./run_scaled.sh --help >/dev/null 2>&1; then
        print_success "run_scaled.sh help command works"
    else
        print_error "run_scaled.sh help command failed"
        return 1
    fi
    
    # Test script with dry-run (if available)
    print_status "Testing run_scaled.sh basic functionality..."
    
    # Check if there are any feature files
    local feature_count=$(find test_framework/features -name "*.feature" 2>/dev/null | wc -l)
    if [ "$feature_count" -gt 0 ]; then
        print_success "Found $feature_count feature files"
    else
        print_warning "No feature files found in test_framework/features"
    fi
    
    return 0
}

# Function to test Xray integration modules
test_xray_integration() {
    print_status "Testing Xray integration modules..."
    
    local errors=0
    
    # Check Xray integration module
    if [ -f "test_framework/modules/xray_integration.py" ]; then
        print_success "Xray integration module found"
        
        # Test import
        if python3 -c "import sys; sys.path.insert(0, 'test_framework'); from modules.xray_integration import XrayIntegration" 2>/dev/null; then
            print_success "Xray integration module imports successfully"
        else
            print_warning "Xray integration module import failed (dependencies may be missing)"
        fi
    else
        print_error "Xray integration module not found"
        errors=$((errors + 1))
    fi
    
    # Check Jira labeling script
    if [ -f "scripts/update_jira_labels.py" ]; then
        print_success "Jira labeling script found"
    else
        print_error "Jira labeling script not found"
        errors=$((errors + 1))
    fi
    
    # Check configuration files
    if [ -f "config/xray_config.yaml" ]; then
        print_success "Xray configuration file found"
    else
        print_warning "Xray configuration file not found (optional)"
    fi
    
    if [ $errors -eq 0 ]; then
        print_success "Xray integration modules check passed"
        return 0
    else
        print_error "$errors Xray integration checks failed"
        return 1
    fi
}

# Function to generate test summary
generate_test_summary() {
    print_status "Test Summary:"
    echo "=============="
    
    local total_tests=6
    local passed_tests=0
    
    # Count passed tests (this is a simplified approach)
    if test_prerequisites >/dev/null 2>&1; then
        passed_tests=$((passed_tests + 1))
        echo "✅ Prerequisites: PASSED"
    else
        echo "❌ Prerequisites: FAILED"
    fi
    
    if test_python_dependencies >/dev/null 2>&1; then
        passed_tests=$((passed_tests + 1))
        echo "✅ Python Dependencies: PASSED"
    else
        echo "⚠️ Python Dependencies: WARNING"
        passed_tests=$((passed_tests + 1))  # Count as passed since it's non-blocking
    fi
    
    if test_ci_setup_script >/dev/null 2>&1; then
        passed_tests=$((passed_tests + 1))
        echo "✅ CI Setup Script: PASSED"
    else
        echo "❌ CI Setup Script: FAILED"
    fi
    
    if test_run_scaled_script >/dev/null 2>&1; then
        passed_tests=$((passed_tests + 1))
        echo "✅ Run Scaled Script: PASSED"
    else
        echo "❌ Run Scaled Script: FAILED"
    fi
    
    if test_xray_integration >/dev/null 2>&1; then
        passed_tests=$((passed_tests + 1))
        echo "✅ Xray Integration: PASSED"
    else
        echo "❌ Xray Integration: FAILED"
    fi
    
    # CI Configuration (always pass if we get here)
    passed_tests=$((passed_tests + 1))
    echo "✅ CI Configuration: PASSED"
    
    echo ""
    echo "Overall: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        print_success "🎉 All tests passed! CI setup is ready."
        return 0
    else
        print_warning "⚠️ Some tests failed. Please review and fix issues."
        return 1
    fi
}

# Main execution
main() {
    print_status "🧪 Web Automation Reg - CI Setup Test"
    print_status "======================================"
    
    # Simulate CI environment
    simulate_ci_environment
    
    echo ""
    
    # Run tests
    test_prerequisites
    echo ""
    
    test_python_dependencies
    echo ""
    
    test_ci_setup_script
    echo ""
    
    test_run_scaled_script
    echo ""
    
    test_xray_integration
    echo ""
    
    # Generate summary
    generate_test_summary
    
    # Cleanup
    if [ -f "ci_environment.env" ]; then
        rm -f ci_environment.env
        print_status "Cleaned up test files"
    fi
}

# Run main function
main "$@"
