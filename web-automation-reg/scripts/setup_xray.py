#!/usr/bin/env python3
"""
Xray Integration Setup Script for Web Automation Reg
Helps configure and test Xray integration
"""

import os
import sys
import yaml
import requests
from pathlib import Path

def load_config():
    """Load configuration from environment and config files"""
    config = {
        'xray_enabled': os.getenv('XRAY_ENABLED', 'true').lower() == 'true',
        'base_url': os.getenv('XRAY_BASE_URL', 'https://wartek.atlassian.net'),
        'username': os.getenv('XRAY_USERNAME', ''),
        'password': os.getenv('XRAY_PASSWORD', ''),
        'api_token': os.getenv('XRAY_API_TOKEN', ''),
        'project_key': os.getenv('XRAY_PROJECT_KEY', 'RP'),
    }
    return config

def test_jira_connection(config):
    """Test connection to Jira/Xray"""
    print("🔍 Testing Jira connection...")
    
    if not config['username']:
        print("❌ XRAY_USERNAME not configured")
        return False
    
    if not config['api_token'] and not config['password']:
        print("❌ Neither XRAY_API_TOKEN nor XRAY_PASSWORD configured")
        return False
    
    # Test authentication
    auth = (config['username'], config['api_token'] or config['password'])
    test_url = f"{config['base_url']}/rest/api/2/myself"
    
    try:
        response = requests.get(test_url, auth=auth, timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ Successfully connected to Jira as: {user_info.get('displayName', 'Unknown')}")
            return True
        else:
            print(f"❌ Jira authentication failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_xray_api(config):
    """Test Xray API access"""
    print("🔍 Testing Xray API access...")
    
    auth = (config['username'], config['api_token'] or config['password'])
    
    # Test Xray import endpoint
    import_url = f"{config['base_url']}/rest/raven/1.0/import/execution/junit"
    
    try:
        # Just test if the endpoint is accessible (without sending data)
        response = requests.options(import_url, auth=auth, timeout=10)
        if response.status_code in [200, 405]:  # 405 is expected for OPTIONS
            print("✅ Xray API endpoint is accessible")
            return True
        else:
            print(f"❌ Xray API endpoint not accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Xray API error: {e}")
        return False

def check_feature_files():
    """Check feature files for Xray ticket tags"""
    print("🔍 Checking feature files for Xray ticket tags...")
    
    features_dir = Path("test_framework/features")
    if not features_dir.exists():
        print("❌ Features directory not found")
        return False
    
    feature_files = list(features_dir.glob("*.feature"))
    if not feature_files:
        print("❌ No feature files found")
        return False
    
    total_scenarios = 0
    tagged_scenarios = 0
    
    for feature_file in feature_files:
        try:
            with open(feature_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count scenarios
            scenarios = content.count('Scenario:')
            total_scenarios += scenarios
            
            # Count tagged scenarios
            import re
            tagged = len(re.findall(r'@TEST_[A-Z]+-\d+', content))
            tagged_scenarios += tagged
            
        except Exception as e:
            print(f"⚠️ Error reading {feature_file}: {e}")
    
    print(f"📊 Found {total_scenarios} scenarios, {tagged_scenarios} with Xray tags")
    
    if tagged_scenarios == 0:
        print("⚠️ No scenarios have Xray ticket tags (@TEST_RP-XXX)")
        return False
    
    coverage = (tagged_scenarios / total_scenarios) * 100 if total_scenarios > 0 else 0
    print(f"📈 Xray coverage: {coverage:.1f}%")
    
    return True

def generate_sample_junit():
    """Generate a sample JUnit XML for testing"""
    print("🔧 Generating sample JUnit XML...")
    
    sample_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
    <testsuite name="Web Automation Reg Test Suite" tests="2" failures="0" errors="0" skipped="0" time="45.5">
        <testcase name="Aktifasi Layanan Layanan UKBI pada Ruang Bahasa" classname="TC 188" time="25.3">
            <properties>
                <property name="test_key" value="RP-932"/>
                <property name="environment" value="staging"/>
                <property name="test_type" value="regression_test"/>
                <property name="test_case_id" value="RP-932"/>
            </properties>
            <system-out>Test executed successfully</system-out>
        </testcase>
        <testcase name="Viewing the blue button entry point on homepage" classname="TC 188" time="20.2">
            <properties>
                <property name="test_key" value="RP-549"/>
                <property name="environment" value="staging"/>
                <property name="test_type" value="regression_test"/>
                <property name="test_case_id" value="RP-549"/>
            </properties>
            <system-out>Test executed successfully</system-out>
        </testcase>
        <system-out>Test suite executed successfully</system-out>
        <system-err>Suite completed successfully</system-err>
    </testsuite>
</testsuites>'''
    
    with open('sample_junit.xml', 'w', encoding='utf-8') as f:
        f.write(sample_xml)
    
    print("✅ Sample JUnit XML generated: sample_junit.xml")
    return True

def test_xray_import(config):
    """Test Xray import with sample data"""
    print("🔍 Testing Xray import with sample data...")
    
    if not os.path.exists('sample_junit.xml'):
        if not generate_sample_junit():
            return False
    
    auth = (config['username'], config['api_token'] or config['password'])
    import_url = f"{config['base_url']}/rest/raven/1.0/import/execution/junit"
    
    try:
        with open('sample_junit.xml', 'r', encoding='utf-8') as f:
            junit_content = f.read()
        
        headers = {
            'Content-Type': 'application/xml',
            'Accept': 'application/json'
        }
        
        params = {
            'testEnvironment': 'staging'
        }
        
        response = requests.post(
            import_url,
            data=junit_content.encode('utf-8'),
            headers=headers,
            params=params,
            auth=auth,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Xray import successful!")
            print(f"   Test Execution Key: {result.get('testExecIssue', {}).get('key', 'N/A')}")
            return True
        else:
            print(f"❌ Xray import failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Xray import error: {e}")
        return False

def main():
    """Main setup and test function"""
    print("🚀 Web Automation Reg - Xray Integration Setup")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    
    if not config['xray_enabled']:
        print("ℹ️ Xray integration is disabled (XRAY_ENABLED=false)")
        return
    
    print(f"🔧 Configuration:")
    print(f"   Base URL: {config['base_url']}")
    print(f"   Username: {config['username']}")
    print(f"   Project Key: {config['project_key']}")
    print(f"   Auth Method: {'API Token' if config['api_token'] else 'Password'}")
    print()
    
    # Run tests
    tests = [
        ("Jira Connection", lambda: test_jira_connection(config)),
        ("Xray API Access", lambda: test_xray_api(config)),
        ("Feature Files", check_feature_files),
        ("Xray Import", lambda: test_xray_import(config)),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Summary:")
    print("=" * 30)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Xray integration is ready to use.")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and try again.")
        print("\nTroubleshooting tips:")
        print("1. Verify your Jira credentials are correct")
        print("2. Check that you have Xray permissions in the project")
        print("3. Ensure feature files have @TEST_RP-XXX tags")
        print("4. Verify network connectivity to Jira instance")
    
    # Cleanup
    if os.path.exists('sample_junit.xml'):
        os.remove('sample_junit.xml')

if __name__ == '__main__':
    main()
