// delete all rows in the table except the first row (to keep consistency between each different user access)
export const deleteTableRowsButFirst = async (page, selector = 'table') => {
  await page.evaluate((tableSelector) => {
    const table = document.querySelector(tableSelector)
    if (table) {
      const rows = table.querySelectorAll('tr')
      for (let i = rows.length - 1; i > 0; i--) {
        if (i > 1) {
          rows[i].remove()
        }
      }
    }
  }, selector) // Pass the selector as an argument to page.evaluate()
}
