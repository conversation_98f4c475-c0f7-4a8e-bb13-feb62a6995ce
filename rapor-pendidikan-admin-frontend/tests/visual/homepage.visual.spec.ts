import { connectXray } from '@/helpers/global.helper'
import { STAGING_BASE_URL } from '@/utils/env.util'
import { expect, test } from '@playwright/test'

test('@RPNG-1783 @visual @staging should match the home page snapshot', async ({ page }, testInfo) => {
  connectXray(testInfo, { key: 'RPNP-1783' })
  await page.goto(STAGING_BASE_URL)
  await expect(page).toHaveScreenshot({
    maxDiffPixelRatio: 0.2,
    fullPage: true,
  })
})
