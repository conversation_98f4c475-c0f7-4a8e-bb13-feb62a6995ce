#!/usr/bin/env python3
"""
JUnit XML Reporter for Xray Integration
Generates Xray-compatible XML reports from test execution results
"""

import xml.etree.ElementTree as ET
from datetime import datetime
from typing import List, Dict, Any, Optional
import os
import re
import logging

# Import Xray integration
try:
    from .xray_integration import XrayIntegration, XrayTestResult
except ImportError:
    # Fallback if xray_integration is not available
    XrayIntegration = None
    XrayTestResult = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JUnitReporter:
    """Generate JUnit XML reports compatible with Xray for CI integration"""

    def __init__(self, output_dir: str = "./test_framework/reports"):
        self.output_dir = output_dir
        self.test_suites = []
        self.current_suite = None

        # Initialize Xray integration
        self.xray_integration = None
        if XrayIntegration:
            try:
                self.xray_integration = XrayIntegration()
                if self.xray_integration.is_enabled():
                    logger.info("Xray integration enabled")
                else:
                    logger.info("Xray integration disabled or not configured")
            except Exception as e:
                logger.warning(f"Failed to initialize Xray integration: {e}")

        # Store test results for Xray batch update
        self.xray_test_results = []

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
    
    def start_test_suite(self, name: str = None):
        """Start a new test suite"""
        self.current_suite = {
            'name': name or "Test Suite",
            'tests': [],
            'start_time': datetime.now(),
            'failures': 0,
            'errors': 0,
            'skipped': 0
        }
    
    def add_test_result(self, scenario_tags: List[str], scenario_name: str, 
                       status: str, duration: float, steps: List[Dict], 
                       error_message: str = "", test_case_id: str = None,
                       feature_name: str = None, classname: str = None):
        """Add a test result to the current suite"""
        if not self.current_suite:
            self.start_test_suite()
        
        # Extract Xray information from existing tags
        xray_info = self._extract_xray_info(scenario_tags)
        
        # Use test_case_id if provided, otherwise extract from tags
        if not test_case_id:
            test_case_id = xray_info.get('ticket', 'UNKNOWN')
        
        # Use provided classname or derive from feature name
        if not classname:
            classname = feature_name or 'Test Case'
        
        test_case = {
            'name': scenario_name,
            'classname': classname,
            'time': duration,
            'status': status,
            'xray_key': xray_info['ticket'],
            'environment': xray_info['environment'],
            'test_type': xray_info['test_type'],
            'tags': scenario_tags,
            'test_case_id': test_case_id,
            'error_message': error_message,
            'steps': steps,
            'feature_name': feature_name
        }
        
        self.current_suite['tests'].append(test_case)

        # Update counters
        if status == 'failed':
            self.current_suite['failures'] += 1
        elif status == 'error':
            self.current_suite['errors'] += 1
        elif status == 'skipped':
            self.current_suite['skipped'] += 1

        # Prepare Xray test result for batch update
        if self.xray_integration and XrayTestResult:
            test_key = xray_info['ticket']
            if test_key and test_key != 'UNKNOWN':
                try:
                    xray_result = XrayTestResult(
                        test_key=test_key,
                        status=self.xray_integration.convert_status_to_xray(status),
                        environment=xray_info['environment'],
                        execution_time=duration,
                        error_message=error_message,
                        steps=steps,
                        executed_on=datetime.now().isoformat()
                    )
                    self.xray_test_results.append(xray_result)
                    logger.debug(f"Added Xray test result for {test_key}: {status}")
                except Exception as e:
                    logger.warning(f"Failed to prepare Xray result for {test_key}: {e}")
    
    def _extract_xray_info(self, tags: List[str]) -> Dict[str, str]:
        """Extract Xray information from existing Gherkin tags"""
        xray_info = {
            'ticket': 'UNKNOWN',
            'environment': 'UNKNOWN',
            'test_type': 'UNKNOWN'
        }
        
        for tag in tags:
            # Extract ticket number (@TEST_RP-553 -> RP-553)
            if tag.startswith('@TEST_'):
                xray_info['ticket'] = tag[6:]  # Remove @TEST_ prefix
            
            # Extract environment
            elif tag in ['@staging', '@production']:
                xray_info['environment'] = tag[1:]  # Remove @ prefix
            
            # Extract test type
            elif tag in ['@regression_test', '@ui_test', '@API_test', '@smoke_test']:
                xray_info['test_type'] = tag[1:]  # Remove @ prefix
        
        return xray_info
    
    def end_test_suite(self):
        """End the current test suite and add it to the collection"""
        if self.current_suite:
            self.current_suite['end_time'] = datetime.now()
            duration = (self.current_suite['end_time'] - self.current_suite['start_time']).total_seconds()
            self.current_suite['duration'] = duration
            self.test_suites.append(self.current_suite)
            self.current_suite = None
    
    def generate_xml(self) -> str:
        """Generate JUnit XML compatible with Xray"""
        if not self.test_suites:
            # Create a default suite if none exists
            self.start_test_suite()
            self.end_test_suite()
        
        root = ET.Element('testsuites')
        
        for suite in self.test_suites:
            testsuite = ET.SubElement(root, 'testsuite', {
                'name': suite['name'],
                'tests': str(len(suite['tests'])),
                'failures': str(suite['failures']),
                'errors': str(suite['errors']),
                'skipped': str(suite['skipped']),
                'time': str(suite['duration'])
            })
            
            # Add system-out and system-err for Xray compatibility
            system_out = ET.SubElement(testsuite, 'system-out')
            system_out.text = f"Test suite executed at {suite['start_time']}"
            
            system_err = ET.SubElement(testsuite, 'system-err')
            if suite['failures'] > 0 or suite['errors'] > 0:
                system_err.text = f"Suite completed with {suite['failures']} failures and {suite['errors']} errors"
            else:
                system_err.text = "Suite completed successfully"
            
            for test_case in suite['tests']:
                test = ET.SubElement(testsuite, 'testcase', {
                    'name': test_case['name'],
                    'classname': test_case['classname'],
                    'time': str(test_case['time'])
                })
                
                # Add Xray-specific properties
                properties = ET.SubElement(test, 'properties')
                ET.SubElement(properties, 'property', {
                    'name': 'test_key',
                    'value': test_case['xray_key']
                })
                ET.SubElement(properties, 'property', {
                    'name': 'environment',
                    'value': test_case['environment']
                })
                ET.SubElement(properties, 'property', {
                    'name': 'test_type',
                    'value': test_case['test_type']
                })
                ET.SubElement(properties, 'property', {
                    'name': 'test_case_id',
                    'value': test_case['test_case_id']
                })
                
                # Add failure details if test failed
                if test_case['status'] == 'failed' and test_case['error_message']:
                    failure = ET.SubElement(test, 'failure', {
                        'message': f"Test failed for {test_case['xray_key']}",
                        'type': 'AssertionError'
                    })
                    failure.text = test_case['error_message']
                
                # Add skipped details if test was skipped
                elif test_case['status'] == 'skipped':
                    skipped = ET.SubElement(test, 'skipped', {
                        'message': f"Test skipped for {test_case['xray_key']}"
                    })
                
                # Add system-out for test details
                test_system_out = ET.SubElement(test, 'system-out')
                test_system_out.text = f"Test executed at {datetime.now().isoformat()}"
                
                # Add step details if available
                if test_case['steps']:
                    step_details = []
                    for i, step in enumerate(test_case['steps'], 1):
                        step_status = step.get('status', 'unknown')
                        step_text = step.get('text', f'Step {i}')
                        step_details.append(f"Step {i}: {step_status} - {step_text}")
                    
                    if step_details:
                        test_system_out.text += f"\nSteps:\n" + "\n".join(step_details)
        
        return ET.tostring(root, encoding='unicode')
    
    def save_xml(self, filename: str = "junit.xml") -> str:
        """Save the JUnit XML to a file"""
        xml_content = self.generate_xml()
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        return filepath
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all test results"""
        total_tests = sum(len(suite['tests']) for suite in self.test_suites)
        total_failures = sum(suite['failures'] for suite in self.test_suites)
        total_errors = sum(suite['errors'] for suite in self.test_suites)
        total_skipped = sum(suite['skipped'] for suite in self.test_suites)
        total_passed = total_tests - total_failures - total_errors - total_skipped
        
        return {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failures,
            'errors': total_errors,
            'skipped': total_skipped,
            'success_rate': (total_passed / total_tests * 100) if total_tests > 0 else 0
        }
