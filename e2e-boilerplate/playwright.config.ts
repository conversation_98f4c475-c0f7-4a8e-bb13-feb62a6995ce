import '@total-typescript/ts-reset'

import { CI, CI_FAILURE_RETRIES, DEV_CUSTOM_REPORTER, RUN_EXAMPLES } from '@/utils/env.util'
import { defineConfig } from '@playwright/test'
import { playwrightProjects } from 'playwright.projects'

/**
 * See https://playwright.dev/docs/test-configuration.
 */

export default defineConfig({
  testIgnore: RUN_EXAMPLES ? undefined : '**/examples/**',

  testDir: './tests',
  /* See https://playwright.dev/docs/api/class-testconfig#test-config-snapshot-path-template */
  snapshotPathTemplate: '{testDir}/{testFileDir}/__snapshots__/{testName}{-projectName}{ext}',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!CI,
  /* Retry on CI only */
  retries: CI ? CI_FAILURE_RETRIES : 0,
  workers: CI ? 2 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { open: 'never' }],
    ['list'],
    ['junit', { outputFile: './junit.xml' }],
    [CI || DEV_CUSTOM_REPORTER ? './custom-report/e2e-testcase-tracker' : 'null'],
  ],
  /* Timeout configuration */

  // global timeout 1 hour, useful in CI
  globalTimeout: 60 * 60 * 1_000,
  // **60 seconds** is the most reasonable timeout for local development and 3 minutes for CI
  timeout: CI ? 3 * 60 * 1000 : 60 * 1000,
  use: {
    /* Collect trace and video on CI for test evidence */
    trace: CI ? 'off' : 'retain-on-failure',
    video: CI ? 'off' : 'retain-on-failure',
    screenshot: 'on',
  },
  globalSetup: './tests/configs/global.setup.ts',

  /* Configure projects for major browsers */
  projects: playwrightProjects,
})
