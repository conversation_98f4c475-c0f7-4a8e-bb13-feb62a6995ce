import json
import os
from typing import Dict, Any, List, Optional
import time
from pathlib import Path

class LocatorRepository:
    """Centralized repository for storing and retrieving locators."""
    
    def __init__(self, base_dir="./locators"):
        """Initialize the locator repository.
        
        Args:
            base_dir: Base directory for storing locator files
        """
        self.base_dir = base_dir
        self._ensure_directory_exists()
        self.cache = {}
        
    def _ensure_directory_exists(self):
        """Ensure the locator directory exists."""
        os.makedirs(self.base_dir, exist_ok=True)
    
    def _get_domain_file_path(self, domain: str) -> str:
        """Get the file path for a domain's locators.
        
        Args:
            domain: Domain name (e.g., example.com)
            
        Returns:
            str: File path for the domain's locators
        """
        # Sanitize domain name for file path
        domain = domain.replace(':', '_').replace('/', '_')
        return os.path.join(self.base_dir, f"{domain}.json")
    
    def save_locators(self, domain: str, locators: Dict[str, Any]) -> bool:
        """Save locators for a domain.
        
        Args:
            domain: Domain name
            locators: Dictionary of locators
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            file_path = self._get_domain_file_path(domain)
            
            # Load existing locators if file exists
            existing_locators = {}
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_locators = json.load(f)
            
            # Merge new locators with existing ones
            merged_locators = {**existing_locators, **locators}
            
            # Add timestamp for tracking
            merged_locators['_last_updated'] = time.time()
            
            # Save merged locators
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(merged_locators, f, indent=2, ensure_ascii=False)
            
            # Update cache
            self.cache[domain] = merged_locators
            
            print(f"Saved {len(locators)} locators for domain {domain}")
            return True
        except Exception as e:
            print(f"Error saving locators for domain {domain}: {e}")
            return False
    
    def load_locators(self, domain: str) -> Dict[str, Any]:
        """Load locators for a domain.
        
        Args:
            domain: Domain name
            
        Returns:
            Dict[str, Any]: Dictionary of locators
        """
        # Check cache first
        if domain in self.cache:
            return self.cache[domain]
        
        file_path = self._get_domain_file_path(domain)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    locators = json.load(f)
                self.cache[domain] = locators
                print(f"Loaded {len(locators) - 1} locators for domain {domain}")  # -1 for _last_updated
                return locators
            except Exception as e:
                print(f"Error loading locators for domain {domain}: {e}")
        
        print(f"No locators found for domain {domain}")
        return {}
    
    def update_locator(self, domain: str, locator_id: str, locator_data: Dict[str, Any]) -> bool:
        """Update a specific locator for a domain.
        
        Args:
            domain: Domain name
            locator_id: Identifier for the locator
            locator_data: Updated locator data
            
        Returns:
            bool: True if successful, False otherwise
        """
        locators = self.load_locators(domain)
        locators[locator_id] = locator_data
        return self.save_locators(domain, locators)
    
    def get_locator(self, domain: str, locator_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific locator for a domain.
        
        Args:
            domain: Domain name
            locator_id: Identifier for the locator
            
        Returns:
            Optional[Dict[str, Any]]: Locator data or None if not found
        """
        locators = self.load_locators(domain)
        return locators.get(locator_id)
    
    def find_locator_by_text(self, domain: str, text: str) -> Optional[Dict[str, Any]]:
        """Find a locator by text content.
        
        Args:
            domain: Domain name
            text: Text to search for
            
        Returns:
            Optional[Dict[str, Any]]: Locator data or None if not found
        """
        locators = self.load_locators(domain)
        text_lower = text.lower()
        
        for locator_id, locator_data in locators.items():
            if locator_id == '_last_updated':
                continue
                
            # Check text property
            if 'text' in locator_data and text_lower in locator_data['text'].lower():
                return locator_data
            
            # Check value property
            if 'value' in locator_data and text_lower in locator_data['value'].lower():
                return locator_data
            
            # Check placeholder property
            if 'placeholder' in locator_data and text_lower in locator_data['placeholder'].lower():
                return locator_data
        
        return None
    
    def find_locator_by_type(self, domain: str, element_type: str, text: str = None) -> Optional[Dict[str, Any]]:
        """Find a locator by element type and optional text.
        
        Args:
            domain: Domain name
            element_type: Type of element (button, link, input, etc.)
            text: Optional text to search for
            
        Returns:
            Optional[Dict[str, Any]]: Locator data or None if not found
        """
        locators = self.load_locators(domain)
        
        for locator_id, locator_data in locators.items():
            if locator_id == '_last_updated':
                continue
                
            if locator_data.get('type') == element_type:
                if text is None:
                    return locator_data
                
                text_lower = text.lower()
                
                # Check text property
                if 'text' in locator_data and text_lower in locator_data['text'].lower():
                    return locator_data
                
                # Check value property
                if 'value' in locator_data and text_lower in locator_data['value'].lower():
                    return locator_data
        
        return None
    
    def record_success(self, domain: str, locator_id: str) -> None:
        """Record a successful use of a locator.
        
        Args:
            domain: Domain name
            locator_id: Identifier for the locator
        """
        locator = self.get_locator(domain, locator_id)
        if locator:
            success_count = locator.get('success_count', 0) + 1
            locator['success_count'] = success_count
            locator['last_success'] = time.time()
            self.update_locator(domain, locator_id, locator)
    
    def record_failure(self, domain: str, locator_id: str) -> None:
        """Record a failed use of a locator.
        
        Args:
            domain: Domain name
            locator_id: Identifier for the locator
        """
        locator = self.get_locator(domain, locator_id)
        if locator:
            failure_count = locator.get('failure_count', 0) + 1
            locator['failure_count'] = failure_count
            locator['last_failure'] = time.time()
            self.update_locator(domain, locator_id, locator)