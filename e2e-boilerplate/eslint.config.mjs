import pluginJs from "@eslint/js";
import playwright from 'eslint-plugin-playwright';
import globals from "globals";
import tseslint from "typescript-eslint";

export default [
  { ignores: ['playwright-report/**'] },
  { files: ["**/*.{js,mjs,cjs,ts}"] },
  {
    ...playwright.configs['flat/recommended'],
    files: ['tests/**'],
  },
  {
    files: ['tests/**'],
    rules: {
      /**
       * @info {no-networkidle} Intentionally disabled to allow for more reliable test execution in scenarios
       *        where network activity may be unpredictable or slow
       */
      'playwright/no-networkidle': 'off',
      /**
       * @info {no-wait-for-timeout} Intentionally disabled to allow for more flexible timing control in tests
       */
      'playwright/no-wait-for-timeout': 'off',
      /**
       * @info {no-wait-for-selector} Intentionally disabled to allow for more flexible waiting specifically for complex
       *       UI components that are not easily selectable using getByRole, getByText, etc.
       */
      'playwright/no-wait-for-selector': 'off',
    },
  },
  { languageOptions: { globals: globals.node } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
];
