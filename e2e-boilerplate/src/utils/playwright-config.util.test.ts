import { beforeEach, describe, expect, it, vi } from 'vitest'

vi.mock('yargs', () => ({
  default: {
    option: vi.fn().mockReturnValue({ argv: { grep: '' } }),
  },
}))

describe('generateGrep', () => {
  beforeEach(() => {
    vi.unstubAllEnvs()
    /** reset modules to avoid side effects from previous tests (env vars can't be updated if modules are cached) */
    vi.resetModules()
  })

  it('should generate correct grep regex for staging environment with filter', async () => {
    vi.stubEnv('CI', 'false')
    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['tag1', 'tag2'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*tag1)(?=.*tag2)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should generate correct grep regex for staging environment (with grep staging) with filter', async () => {
    vi.stubEnv('CI', 'false')
    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['tag1', 'tag2', '@staging'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*tag1)(?=.*tag2)(?=.*@staging)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should return regex matching staging environment when no filter is provided', async () => {
    vi.stubEnv('CI', 'false')
    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should generate correct grep regex for production environment with filter', async () => {
    vi.stubEnv('CI', 'false')
    const { generateGrep } = await import('./playwright-config.util')

    const env = 'production'
    const filter = { AND: ['tag3', 'tag4'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@production)(?=.*tag3)(?=.*tag4)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should return regex matching production environment when no filter is provided', async () => {
    vi.stubEnv('CI', 'false')

    const { generateGrep } = await import('./playwright-config.util')

    const env = 'production'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@production)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should return regex matching staging environment when no filter is provided', async () => {
    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should return regex matching staging environment on CI with staging environment', async () => {
    vi.stubEnv('CI', 'true')
    vi.stubEnv('ENVIRONMENT', 'staging')

    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should return regex matching production environment on CI with production environment', async () => {
    vi.stubEnv('CI', 'true')
    vi.stubEnv('ENVIRONMENT', 'production')

    const { generateGrep } = await import('./playwright-config.util')

    const env = 'production'
    const grepRegex = generateGrep(env)

    expect(grepRegex).toEqual(/(?=.*@production)/)
  })

  it('should generate correct grep regex for staging environment on CI with filter tags', async () => {
    vi.stubEnv('CI', 'true')
    vi.stubEnv('ENVIRONMENT', 'staging')

    const { generateGrep } = await import('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['SHIP-123', 'SHIP-889'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*SHIP-123)(?=.*SHIP-889)/

    expect(grepRegex).toEqual(expectedRegex)
  })

  it('should generate correct grep regex for production environment on CI with filter tags', async () => {
    vi.stubEnv('CI', 'true')
    vi.stubEnv('ENVIRONMENT', 'production')

    const { generateGrep } = await import('./playwright-config.util')

    const env = 'production'
    const filter = { AND: ['SHIP-456', 'SHIP-789'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@production)(?=.*SHIP-456)(?=.*SHIP-789)/

    expect(grepRegex).toEqual(expectedRegex)
  })
})
