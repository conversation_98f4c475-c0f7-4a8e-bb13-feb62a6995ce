#!/bin/bash

# Feature Converter Script
# Converts user-generated features into proper .feature format

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
INPUT_FILE=""
OUTPUT_DIR="test_framework/features"
BASE_URL="https://your-app-url.com"

# Function to show usage
show_usage() {
    echo -e "${BLUE}Feature Converter - Convert user-generated features to .feature format${NC}"
    echo ""
    echo "Usage: $0 <input_file> [options]"
    echo ""
    echo "Arguments:"
    echo "  input_file              Input file containing features (.txt, .md, .csv)"
    echo ""
    echo "Options:"
    echo "  -o, --output DIR        Output directory for feature files (default: test_framework/features)"
    echo "  -u, --base-url URL      Base URL for the application (default: https://your-app-url.com)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 my_features.txt"
    echo "  $0 features.md --output custom_features"
    echo "  $0 test_cases.csv --base-url https://example.com"
    echo ""
    echo "Supported input formats:"
echo "  - .txt: Plain text format"
echo "  - .md:  Markdown format"
echo "  - .csv: CSV format with columns: Feature, Scenario, Steps, Tags"
echo "  - .feature: Existing Gherkin feature files"
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        echo -e "${RED}❌ Error: Input file '$1' not found${NC}"
        exit 1
    fi
}

# Function to check file extension
check_extension() {
    local file="$1"
    local ext="${file##*.}"
    
    case "$ext" in
        txt|md|csv|feature)
            return 0
            ;;
        *)
            echo -e "${RED}❌ Error: Unsupported file extension '.$ext'${NC}"
            echo -e "${YELLOW}Supported formats: .txt, .md, .csv, .feature${NC}"
            exit 1
            ;;
    esac
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -u|--base-url)
            BASE_URL="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            echo -e "${RED}❌ Error: Unknown option '$1'${NC}"
            show_usage
            exit 1
            ;;
        *)
            if [ -z "$INPUT_FILE" ]; then
                INPUT_FILE="$1"
            else
                echo -e "${RED}❌ Error: Multiple input files specified${NC}"
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if input file is provided
if [ -z "$INPUT_FILE" ]; then
    echo -e "${RED}❌ Error: Input file is required${NC}"
    show_usage
    exit 1
fi

# Validate input file
check_file "$INPUT_FILE"
check_extension "$INPUT_FILE"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Change to project root
cd "$PROJECT_ROOT"

echo -e "${BLUE}🚀 Feature Converter${NC}"
echo -e "${BLUE}==================${NC}"
echo ""
echo -e "${GREEN}📁 Input file:${NC} $INPUT_FILE"
echo -e "${GREEN}📁 Output directory:${NC} $OUTPUT_DIR"
echo -e "${GREEN}🌐 Base URL:${NC} $BASE_URL"
echo ""

# Run the Python script
python3 "$SCRIPT_DIR/convert_features.py" "$INPUT_FILE" \
    --output "$OUTPUT_DIR" \
    --base-url "$BASE_URL"

echo ""
echo -e "${GREEN}✅ Feature conversion completed!${NC}" 