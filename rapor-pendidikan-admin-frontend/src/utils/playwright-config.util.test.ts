import assert from 'node:assert'
import { after, test } from 'node:test'

test('generateGrep', async (t) => {
  await t.test('should generate correct grep regex for staging environment with filter', () => {
    process.env.CI = 'false'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['tag1', 'tag2'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*tag1)(?=.*tag2)/

    assert.deepEqual(grepRegex, expectedRegex)
  })

  await t.test('should generate correct grep regex for staging environment (with grep staging) with filter', () => {
    process.env.CI = 'false'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['tag1', 'tag2', '@staging'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*tag1)(?=.*tag2)(?=.*@staging)/

    assert.deepEqual(grepRegex, expectedRegex)
  })

  const { generateGrep } = require('./playwright-config.util')
  await t.test('should return regex matching staging environment when no filter is provided', () => {
    process.env.CI = 'false'
    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/
    assert.deepEqual(grepRegex, expectedRegex)
  })

  await t.test('should generate correct grep regex for production environment with filter', () => {
    process.env.CI = 'false'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'production'
    const filter = { AND: ['tag3', 'tag4'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@production)(?=.*tag3)(?=.*tag4)/

    assert.deepEqual(grepRegex, expectedRegex)
  })

  await t.test('should return regex matching production environment when no filter is provided', () => {
    process.env.CI = 'false'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'production'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@production)/

    assert.deepEqual(grepRegex, expectedRegex)
  })

  await t.test('should return regex matching staging environment when no filter is provided', () => {
    process.env.CI = 'false'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/

    assert.deepEqual(grepRegex, expectedRegex)
  })

  await t.test('should return regex matching staging environment on CI with staging environment', () => {
    process.env.ENVIRONMENT = 'staging'
    const { generateGrep } = require('./playwright-config.util')
    process.env.CI = 'true'

    const env = 'staging'
    const grepRegex = generateGrep(env)
    const expectedRegex = /(?=.*@staging)/

    assert.deepStrictEqual(grepRegex, expectedRegex)
  })

  await t.test('should return regex matching production environment on CI with production environment', () => {
    process.env.ENVIRONMENT = 'production'
    const { generateGrep } = require('./playwright-config.util')
    process.env.CI = 'true'

    const env = 'production'
    const grepRegex = generateGrep(env)

    assert.deepStrictEqual(grepRegex, /(?=.*@production)/)
  })

  await t.test('should generate correct grep regex for staging environment on CI with filter tags', () => {
    process.env.ENVIRONMENT = 'staging'
    process.env.CI = 'true'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'staging'
    const filter = { AND: ['SHIP-123', 'SHIP-889'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@staging)(?=.*SHIP-123)(?=.*SHIP-889)/

    assert.deepStrictEqual(grepRegex, expectedRegex)
  })

  await t.test('should generate correct grep regex for production environment on CI with filter tags', () => {
    process.env.ENVIRONMENT = 'production'
    process.env.CI = 'true'
    const { generateGrep } = require('./playwright-config.util')

    const env = 'production'
    const filter = { AND: ['SHIP-456', 'SHIP-789'] }
    const grepRegex = generateGrep(env, filter)
    const expectedRegex = /(?=.*@production)(?=.*SHIP-456)(?=.*SHIP-789)/

    assert.deepStrictEqual(grepRegex, expectedRegex)
  })

  after(() => {
    delete process.env.ENVIRONMENT
    delete process.env.CI
  })
})
