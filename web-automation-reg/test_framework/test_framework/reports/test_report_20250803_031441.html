
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R<PERSON>h Pendidikan Automation Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .chart-container h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .chart-wrapper {
            position: relative;
            height: 200px;
            margin: 20px 0;
        }
        
        .chart-center-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2em;
            font-weight: bold;
            color: #495057;
        }
        
        .chart-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .chart-detail-section {
            text-align: left;
        }
        
        .chart-detail-section h4 {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-list {
            list-style: none;
            padding: 0;
        }
        
        .status-list li {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .status-list li i {
            margin-right: 8px;
            font-size: 12px;
        }
        
        .status-list li.passed i { color: #28a745; }
        .status-list li.failed i { color: #dc3545; }
        .status-list li.undefined i { color: #ffc107; }
        
        .progress-list {
            list-style: none;
            padding: 0;
        }
        
        .progress-list li {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .progress-list li i {
            margin-right: 8px;
            font-size: 12px;
        }
        
        .progress-list li.passed i { color: #28a745; }
        .progress-list li.failed i { color: #dc3545; }
        .progress-list li.undefined i { color: #ffc107; }
        
        .run-info {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .run-info h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .info-item i {
            margin-right: 10px;
            color: #6c757d;
            width: 20px;
        }
        
        .features-table {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .table-header h3 {
            color: #495057;
        }
        
        .table-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .entries-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .feature-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .feature-link:hover {
            text-decoration: underline;
        }
        
        .status-icon {
            font-size: 1.2em;
        }
        
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-undefined { color: #ffc107; }
        
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
        }
        
        .pagination-info {
            color: #6c757d;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
        }
        
        .pagination-controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .pagination-controls button:hover {
            background: #f8f9fa;
        }
        
        .pagination-controls button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            margin-top: 40px;
        }
        
        /* Feature Detail Page Styles */
        .feature-detail {
            display: none;
        }
        
        .back-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .feature-header {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tags {
            margin-bottom: 15px;
        }
        
        .tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 3px;
            margin-right: 5px;
            font-size: 12px;
        }
        
        .feature-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .feature-description {
            margin: 15px 0;
            color: #6c757d;
        }
        
        .scenarios-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .scenarios-chart {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .scenarios-chart h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .scenarios-chart-wrapper {
            position: relative;
            height: 150px;
            margin: 20px 0;
        }
        
        .scenarios-chart-center-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em;
            font-weight: bold;
            color: #495057;
        }
        
        .scenarios-chart-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .metadata-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .metadata-panel h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .metadata-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .metadata-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .metadata-value {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 14px;
        }
        
        .metadata-value i {
            margin-right: 8px;
            color: #6c757d;
            width: 16px;
        }
        
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-section h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-button:hover {
            background: #f8f9fa;
        }
        
        .filter-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .scenarios-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .scenarios-section h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .scenario-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .scenario-header {
            background: #f8f9fa;
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .scenario-header:hover {
            background: #e9ecef;
        }
        
        .scenario-title {
            font-weight: 500;
            color: #495057;
        }
        
        .scenario-status {
            font-size: 1.2em;
        }
        
        .scenario-content {
            padding: 15px;
            border-top: 1px solid #ddd;
            display: none;
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        
        .step-item.passed {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .step-item.failed {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .step-item.undefined {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .step-icon {
            margin-right: 10px;
            font-size: 1.2em;
            margin-top: 2px;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-text {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .step-details {
            font-size: 14px;
            color: #6c757d;
        }
        
        .error-section {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .error-section.failed {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border-left: 5px solid #dc3545;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
        }
        
        .error-section.skipped {
            background: linear-gradient(135deg, #fffbf0 0%, #fef3c7 100%);
            border-left: 5px solid #f59e0b;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
        }
        
        .error-toggle {
            color: #dc3545;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .error-toggle:hover {
            background: rgba(220, 53, 69, 0.2);
        }
        
        .error-toggle i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .error-content {
            display: none;
            margin-top: 15px;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .error-message {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            font-size: 13px;
            line-height: 1.5;
            text-align: left !important;
        }
        
        .error-message * {
            text-align: left !important;
        }
        
        .error-message strong {
            text-align: left !important;
        }
        
        .error-message p {
            text-align: left !important;
        }
        
        .error-message div {
            text-align: left !important;
        }
        
        .error-message span {
            text-align: left !important;
        }
        
        /* Override any parent container alignment */
        .scenarios-chart .error-message,
        .chart-container .error-message,
        .dashboard .error-message,
        .main-container .error-message,
        .feature-detail .error-message {
            text-align: left !important;
        }
        
        .scenarios-chart .error-message *,
        .chart-container .error-message *,
        .dashboard .error-message *,
        .main-container .error-message *,
        .feature-detail .error-message * {
            text-align: left !important;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #0ea5e9;
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
            margin-top: 15px;
        }
        
        .solution-title {
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .solution-title::before {
            content: "💡";
            margin-right: 8px;
            font-size: 18px;
        }
        
        .solution-content {
            color: #0c4a6e;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .solution-content strong {
            color: #0369a1;
        }
        
        .solution-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .solution-content li {
            margin-bottom: 8px;
            padding-left: 5px;
        }
        
        .quick-fix-badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Rumah Pendidikan Automation Report</h1>
        <div class="subtitle">Comprehensive Test Automation Report</div>
    </div>
    
    <div class="main-container">
        <!-- Main Dashboard -->
        <div id="main-dashboard">
            <div class="dashboard">
                <div class="chart-container">
                    <h3>Features</h3>
                    <div class="chart-wrapper">
                        <canvas id="featuresChart"></canvas>
                        <div class="chart-center-text">1</div>
                    </div>
                    <div class="chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> Not Defined</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> 0.0%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> 100.0%</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> 0.0%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>Scenarios</h3>
                    <div class="chart-wrapper">
                        <canvas id="scenariosChart"></canvas>
                        <div class="chart-center-text">1</div>
                    </div>
                    <div class="chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> Not Defined</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> 0.0%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> 100.0%</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> 0.0%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="run-info">
                    <h3>Run Info</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-project-diagram"></i>
                            <span><strong>Project:</strong> Rumah Pendidikan Automation Report</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span><strong>Generation Time:</strong> 2025-08-03T03:14:41.856047Z</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span><strong>OS User:</strong> jemz</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-stopwatch"></i>
                            <span><strong>Duration:</strong> 01 Minutes 46 Seconds</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="features-table">
                <div class="table-header">
                    <h3>Features Overview</h3>
                    <div class="table-controls">
                        <select class="entries-select" id="entriesPerPage">
                            <option value="10">10 entries per page</option>
                            <option value="25">25 entries per page</option>
                            <option value="50" selected>50 entries per page</option>
                            <option value="100">100 entries per page</option>
                        </select>
                        <input type="text" class="search-box" id="searchBox" placeholder="Search features...">
                    </div>
                </div>
                
                <table id="featuresTable">
                    <thead>
                        <tr>
                            <th>Feature Name</th>
                            <th>Status</th>
                            <th>Device</th>
                            <th>OS</th>
                            <th>Browser</th>
                            <th>Total</th>
                            <th>Passed</th>
                            <th>Failed</th>
                            <th>Undefined</th>
                        </tr>
                    </thead>
                    <tbody>
        
                        <tr>
                            <td><a href="#" class="feature-link" onclick="showFeatureDetail(0)">Test Execution</a></td>
                            <td><i class="fas fas fa-times-circle status-icon status-failed"></i></td>
                            <td><i class="fas fa-desktop"></i> Runner Machine</td>
                            <td><i class="fab fa-linux"></i> linux</td>
                            <td><i class="fab fa-chrome"></i> 103</td>
                            <td>1</td>
                            <td>0</td>
                            <td>1</td>
                            <td>0</td>
                        </tr>
            
                    </tbody>
                </table>
                
                <div class="pagination">
                    <div class="pagination-info">
                        Showing 1 to 1 of 1 entries
                    </div>
                    <div class="pagination-controls">
                        <button disabled>&lt;&lt;</button>
                        <button disabled>&lt;</button>
                        <button class="active">1</button>
                        <button disabled>&gt;</button>
                        <button disabled>&gt;&gt;</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Feature Detail Pages -->
        
        <div id="feature-detail-0" class="feature-detail">
            <button class="back-button" onclick="showMainDashboard()">
                <i class="fas fa-arrow-left"></i> Features Overview
            </button>
            
                                    <div class="feature-header">
                            <div class="tags">
                                <span class="tag">@TC8</span><span class="tag">@TC4</span><span class="tag">@TC11</span><span class="tag">@TC12</span><span class="tag">@TC14</span><span class="tag">@TC9</span><span class="tag">@TC2</span><span class="tag">@TC6</span><span class="tag">@TC5</span><span class="tag">@TC3</span><span class="tag">@TC7</span><span class="tag">@TC1</span><span class="tag">@TC13</span><span class="tag">@TC10</span>
                            </div>
                            <h2>Feature: Test Execution</h2>
                            <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                            <div class="feature-info">
                                <div><strong>File name:</strong> rumdik_regression_test.feature</div>
                                <div><strong>Relative path:</strong> features/rumdik_regression_test.feature</div>
                            </div>
                        </div>
            
            <div class="scenarios-overview">
                <div class="scenarios-chart">
                    <h3>Scenarios</h3>
                    <div class="scenarios-chart-wrapper">
                        <canvas id="scenarioChart-0"></canvas>
                        <div class="scenarios-chart-center-text">1</div>
                    </div>
                    <div class="scenarios-chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> 0.0%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> 100.0%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="metadata-panel">
                    <h3>Metadata</h3>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <div class="metadata-label">Device</div>
                            <div class="metadata-value">Local Machine</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">OS</div>
                            <div class="metadata-value">
                                <i class="fab fa-apple"></i>
                                macOS
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Browser</div>
                            <div class="metadata-value">
                                <i class="fab fa-chrome"></i>
                                Chrome 103
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="filter-section">
                <h3>Filter Scenarios</h3>
                <div class="filter-buttons">
                    <button class="filter-button active" onclick="filterScenarios('all', 0)">All (1)</button>
                    <button class="filter-button" onclick="filterScenarios('passed', 0)">Passed (0)</button>
                    <button class="filter-button" onclick="filterScenarios('failed', 0)">Failed (1)</button>
                </div>
            </div>
            
            <div class="scenarios-section">
                <h3>Scenarios</h3>
            
                <div class="scenario-item" data-status="failed" data-feature="0">
                    <div class="scenario-header" onclick="toggleScenario(0, 0)">
                        <div class="scenario-title">Test Scenario: [TC14] Footer</div>
                        <div class="scenario-status">
                            <i class="fas fa-times-circle status-failed"></i>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="scenario-content" id="scenario-content-0-0">
                
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I am on the main page</div>
                                <div class="step-details">Step 1 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I scroll to the "Ke Pusat Informasi" section</div>
                                <div class="step-details">Step 2 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I should see "Kementerian Pendidikan Dasar dan Menengah"</div>
                                <div class="step-details">Step 3 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I should see "Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270"</div>
                                <div class="step-details">Step 4 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I should see "Pusat Bantuan Rumah Pendidikan"</div>
                                <div class="step-details">Step 5 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I should see "Syarat & Ketentuan"</div>
                                <div class="step-details">Step 6 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item failed">
                            <div class="step-icon">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I should see "Kebijakan Privasii"</div>
                                <div class="step-details">Step 7 execution</div>
                    
                                <div class="error-section failed">
                                    <div class="error-toggle" onclick="toggleError(this)">
                                        <i class="fas fa-exclamation-triangle"></i> 
                                        <span>View Error Details & Solutions</span>
                                        <span class="quick-fix-badge">Quick Show</span>
                                    </div>
                                    <div class="error-content">
                                        <div class="error-message" style="text-align: left !important;">
                                            <strong>Error:</strong> Expected: N/A, Actual: Kebijakan Privasi
                                        </div>
                                        <div class="solution-section">
                                            <div class="solution-title">Recommended Solutions</div>
                                            <div class="solution-content">👁️ <strong>Text Visibility Issue:</strong><br>• The automated text finder could not locate the specified text<br>• Check if the text has changed on the website<br>• Verify the exact text matches what's displayed on the page<br>• The text might be in a different section or page<br>• Check if the page has loaded completely<br>• The text might be hidden or require scrolling to view<br>• Verify there are no typos in the test step text<br>• The website content may have been updated</div>
                                        </div>
                                    </div>
                                </div>
                        
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I click "Kebijakan Privasi"</div>
                                <div class="step-details">Step 8 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">move to the previous page</div>
                                <div class="step-details">Step 9 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I click "Syarat & Ketentuan"</div>
                                <div class="step-details">Step 10 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">move to the previous page</div>
                                <div class="step-details">Step 11 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">I click "Pusat Bantuan Rumah Pendidikan"</div>
                                <div class="step-details">Step 12 execution</div>
                    
                            </div>
                        </div>
                    
                        <div class="step-item passed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">move to the previous page</div>
                                <div class="step-details">Step 13 execution</div>
                    
                            </div>
                        </div>
                    
                    </div>
                </div>
                
                                    </div>
                    </div>
                
        <div class="footer">
            <p>Maintained by Test Automation Team. Find us on:</p>
            <div style="margin-top: 10px;">
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-github fa-lg"></i></a>
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-twitter fa-lg"></i></a>
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-linkedin fa-lg"></i></a>
            </div>
        </div>
    </div>
    
    <script>
        // Chart.js configuration
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        
        // Features Chart
        var featuresCtx = document.getElementById('featuresChart').getContext('2d');
        var featuresChart = new Chart(featuresCtx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Undefined'],
                datasets: [{
                    data: [0, 1, 0],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '60%'
            }
        });
        
        // Scenarios Chart
        var scenariosCtx = document.getElementById('scenariosChart').getContext('2d');
        var scenariosChart = new Chart(scenariosCtx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Undefined'],
                datasets: [{
                    data: [0, 1, 0],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '60%'
            }
        });
        
        // Feature detail charts
        
        var scenarioCtx0 = document.getElementById('scenarioChart-0').getContext('2d');
        var scenarioChart0 = new Chart(scenarioCtx0, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed'],
                datasets: [{
                    data: [0, 1],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '60%'
            }
        });
            
        
        // Navigation functions
        function showFeatureDetail(featureIndex) {
            document.getElementById('main-dashboard').style.display = 'none';
            document.getElementById('feature-detail-' + featureIndex).style.display = 'block';
        }
        
        function showMainDashboard() {
            document.getElementById('main-dashboard').style.display = 'block';
            document.querySelectorAll('.feature-detail').forEach(function(el) {
                el.style.display = 'none';
            });
        }
        
        function toggleScenario(featureIndex, scenarioIndex) {
            var content = document.getElementById('scenario-content-' + featureIndex + '-' + scenarioIndex);
            var header = content.previousElementSibling;
            var icon = header.querySelector('.fa-chevron-down');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        
        function toggleError(element) {
            var content = element.nextElementSibling;
            var span = element.querySelector('span');
            var badge = element.querySelector('.quick-fix-badge');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                span.textContent = 'Hide Error Details & Solutions';
                if (badge) {
                    badge.textContent = 'Hide';
                    badge.style.background = '#dc3545';
                }
                element.style.background = 'rgba(220, 53, 69, 0.2)';
            } else {
                content.style.display = 'none';
                span.textContent = 'View Error Details & Solutions';
                if (badge) {
                    badge.textContent = 'Quick Show';
                    badge.style.background = '#10b981';
                }
                element.style.background = 'rgba(220, 53, 69, 0.1)';
            }
        }
        
        function filterScenarios(status, featureIndex) {
            // Update filter buttons
            var filterButtons = document.querySelectorAll('#feature-detail-' + featureIndex + ' .filter-button');
            filterButtons.forEach(function(btn) {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter scenarios
            var scenarios = document.querySelectorAll('#feature-detail-' + featureIndex + ' .scenario-item');
            scenarios.forEach(function(scenario) {
                var scenarioStatus = scenario.getAttribute('data-status');
                if (status === 'all' || scenarioStatus === status) {
                    scenario.style.display = '';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
        

        
        // Search functionality
        document.getElementById('searchBox').addEventListener('input', function() {
            var searchTerm = this.value.toLowerCase();
            var rows = document.querySelectorAll('#featuresTable tbody tr');
            
            rows.forEach(function(row) {
                var featureName = row.cells[0].textContent.toLowerCase();
                if (featureName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Entries per page functionality
        document.getElementById('entriesPerPage').addEventListener('change', function() {
            // Implementation for pagination
            console.log('Entries per page changed to: ' + this.value);
        });
    </script>
</body>
</html>
        