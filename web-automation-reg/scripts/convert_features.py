#!/usr/bin/env python3
"""
Feature Converter
Converts user-generated features into proper .feature format for the test framework
"""

import os
import sys
import re
import argparse
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class FeatureConverter:
    def __init__(self, input_file: str, output_dir: str = "test_framework/features", base_url: str = "https://your-app-url.com"):
        self.input_file = input_file
        self.output_dir = output_dir
        self.features = {}
        self.base_url = base_url  # Can be overridden by command line or extracted from file
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Step conversion patterns
        self.step_patterns = {
            'navigation': {
                'patterns': [
                    r'(?:buka|navigate|go to|visit|klik|click)\s+(.+?)(?:\s+button)?$',
                    r'(?:open|access)\s+(.+?)$',
                    r'(?:masuk|enter)\s+(.+?)$'
                ],
                'gherkin': 'And I click "{target}"'
            },
            'input': {
                'patterns': [
                    r'(?:isi|ketik|type|input|masukkan)\s+(.+?)\s+(?:di|in|pada)\s+(.+?)$',
                    r'(?:enter|fill)\s+(.+?)\s+(?:in|to)\s+(.+?)$'
                ],
                'gherkin': 'When I type "{value}" in the {field} field'
            },
            'verification': {
                'patterns': [
                    r'(?:pastikan|verify|assert|check|should see)\s+(.+?)$',
                    r'(?:memastikan|memverifikasi)\s+(.+?)$',
                    r'(?:confirm|ensure)\s+(.+?)$'
                ],
                'gherkin': 'Then I should see "{target}"'
            },
            'scroll': {
                'patterns': [
                    r'(?:scroll|gulir)\s+(?:ke|to)\s+(.+?)$',
                    r'(?:scroll|gulir)\s+(.+?)\s+section$'
                ],
                'gherkin': 'When I scroll to the "{target}" section'
            },
            'navigation_page': {
                'patterns': [
                    r'(?:buka|open|navigate to)\s+(.+?)\s+page$',
                    r'(?:go to|visit)\s+(.+?)$'
                ],
                'gherkin': 'Given I am on the {page} page'
            },
            'search': {
                'patterns': [
                    r'(?:cari|search)\s+(.+?)$',
                    r'(?:pencarian|find)\s+(.+?)$'
                ],
                'gherkin': 'When I search for "{query}"'
            },
            'wait': {
                'patterns': [
                    r'(?:tunggu|wait)\s+(?:for\s+)?(.+?)$',
                    r'(?:wait|delay)\s+(.+?)$'
                ],
                'gherkin': 'And I wait for "{target}"'
            }
        }
    
    def read_input_file(self) -> Dict[str, Dict]:
        """Read features from input file (supports multiple formats)."""
        print(f"📖 Reading features from: {self.input_file}")
        
        if not os.path.exists(self.input_file):
            print(f"❌ Input file not found: {self.input_file}")
            return {}
        
        file_extension = os.path.splitext(self.input_file)[1].lower()
        
        if file_extension == '.txt':
            return self._read_txt_file()
        elif file_extension == '.md':
            return self._read_markdown_file()
        elif file_extension == '.csv':
            return self._read_csv_file()
        elif file_extension == '.feature':
            return self._read_feature_file()
        else:
            print(f"❌ Unsupported file format: {file_extension}")
            return {}
    
    def _read_txt_file(self) -> Dict[str, Dict]:
        """Read features from plain text file."""
        features = {}
        current_feature = None
        current_scenario = None

        with open(self.input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Extract base_url from comments if present
        extracted_base_url = None
        for line in lines:
            line = line.strip()
            if line.startswith('#') and 'base_url:' in line.lower():
                url_match = re.search(r'base_url:\s*(.+)', line, re.IGNORECASE)
                if url_match:
                    extracted_base_url = url_match.group(1).strip()
                    break

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Extract base_url from comments
            if line.startswith('#'):
                if 'base_url:' in line.lower():
                    url_match = re.search(r'base_url:\s*(.+)', line, re.IGNORECASE)
                    if url_match and current_feature:
                        features[current_feature]['base_url'] = url_match.group(1).strip()
                continue
            
            # Detect feature
            if line.lower().startswith('feature:') or line.lower().startswith('fitur:'):
                feature_name = line.split(':', 1)[1].strip()
                current_feature = feature_name
                # Priority: 1. Extracted from file, 2. Command line/constructor parameter
                feature_base_url = extracted_base_url if extracted_base_url else self.base_url
                features[current_feature] = {
                    'scenarios': {},
                    'base_url': feature_base_url
                }
                continue
            
            # Detect scenario
            if line.lower().startswith('scenario:') or line.lower().startswith('skenario:'):
                scenario_name = line.split(':', 1)[1].strip()
                current_scenario = scenario_name
                if current_feature:
                    features[current_feature]['scenarios'][current_scenario] = {
                        'steps': [],
                        'tags': []
                    }
                continue
            
            # Detect test case ID
            if line.startswith('@'):
                tag_match = re.match(r'@(\w+)', line)
                if tag_match and current_scenario and current_feature:
                    features[current_feature]['scenarios'][current_scenario]['tags'].append(line)
                continue
            
            # Detect steps
            if current_scenario and current_feature and line:
                features[current_feature]['scenarios'][current_scenario]['steps'].append(line)
        
        return features
    
    def _read_markdown_file(self) -> Dict[str, Dict]:
        """Read features from markdown file."""
        features = {}
        current_feature = None
        current_scenario = None
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Detect feature (markdown headers)
            if line.startswith('## ') and ('feature' in line.lower() or 'fitur' in line.lower()):
                feature_name = line.replace('## ', '').replace('Feature:', '').replace('Fitur:', '').strip()
                current_feature = feature_name
                features[current_feature] = {
                    'scenarios': {},
                    'base_url': self.base_url
                }
                continue
            
            # Detect scenario (markdown headers)
            if line.startswith('### ') and ('scenario' in line.lower() or 'skenario' in line.lower()):
                scenario_name = line.replace('### ', '').replace('Scenario:', '').replace('Skenario:', '').strip()
                current_scenario = scenario_name
                if current_feature:
                    features[current_feature]['scenarios'][current_scenario] = {
                        'steps': [],
                        'tags': []
                    }
                continue
            
            # Detect steps (list items)
            if line.startswith('- ') or line.startswith('* '):
                step = line[2:].strip()
                if current_scenario and current_feature and step:
                    features[current_feature]['scenarios'][current_scenario]['steps'].append(step)
        
        return features
    
    def _read_csv_file(self) -> Dict[str, Dict]:
        """Read features from CSV file."""
        import csv

        features = {}

        with open(self.input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)

            for row in reader:
                feature_name = row.get('Feature', row.get('Fitur', 'Default Feature'))
                scenario_name = row.get('Scenario', row.get('Skenario', 'Default Scenario'))
                steps_text = row.get('Steps', row.get('Steps', ''))
                tags = row.get('Tags', '').split(',') if row.get('Tags') else []

                # Check for base_url in CSV row
                csv_base_url = row.get('Base_URL', row.get('base_url', row.get('BaseURL', '')))
                feature_base_url = csv_base_url if csv_base_url else self.base_url

                if feature_name not in features:
                    features[feature_name] = {
                        'scenarios': {},
                        'base_url': feature_base_url
                    }
                
                if scenario_name not in features[feature_name]['scenarios']:
                    features[feature_name]['scenarios'][scenario_name] = {
                        'steps': [],
                        'tags': [tag.strip() for tag in tags if tag.strip()]
                    }
                
                # Parse steps
                if steps_text:
                    steps = [step.strip() for step in steps_text.split('\n') if step.strip()]
                    features[feature_name]['scenarios'][scenario_name]['steps'].extend(steps)
        
        return features
    
    def _read_feature_file(self) -> Dict[str, Dict]:
        """Read and preserve existing Gherkin feature files while ensuring proper structure."""
        features = {}
        current_feature = None
        current_scenario = None
        current_tags = []
        current_background = None
        current_scenario_outline = None
        current_examples = None
        feature_description = []
        in_feature_description = False

        with open(self.input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # First pass: extract base_url from comments if present
        extracted_base_url = None
        for line in lines:
            line = line.strip()
            if line.startswith('#') and 'base_url:' in line.lower():
                # Extract base_url from comment like "# base_url: https://example.com"
                url_match = re.search(r'base_url:\s*(.+)', line, re.IGNORECASE)
                if url_match:
                    extracted_base_url = url_match.group(1).strip()
                    break

        for line in lines:
            line = line.rstrip()  # Keep leading spaces for indentation
            original_line = line

            # Skip empty lines but preserve them in context
            if not line.strip():
                continue

            # Extract base_url from comments but don't skip them entirely
            if line.strip().startswith('#'):
                if 'base_url:' in line.lower():
                    # Extract and store base_url for current feature
                    url_match = re.search(r'base_url:\s*(.+)', line.strip(), re.IGNORECASE)
                    if url_match and current_feature:
                        features[current_feature]['base_url'] = url_match.group(1).strip()
                continue

            # Detect feature
            if line.strip().lower().startswith('feature:'):
                feature_name = line.split(':', 1)[1].strip()
                current_feature = feature_name
                # Priority: 1. Extracted from file, 2. Command line/constructor parameter
                feature_base_url = extracted_base_url if extracted_base_url else self.base_url
                features[current_feature] = {
                    'scenarios': {},
                    'base_url': feature_base_url,
                    'description': [],
                    'background': None,
                    'feature_tags': current_tags.copy()
                }
                current_tags = []
                in_feature_description = True
                continue

            # Detect tags (lines starting with @)
            if line.strip().startswith('@'):
                current_tags.append(line.strip())
                continue

            # Detect background
            if line.strip().lower().startswith('background:'):
                in_feature_description = False
                current_scenario = None  # Reset scenario context
                current_background = {
                    'steps': []
                }
                if current_feature:
                    features[current_feature]['background'] = current_background
                continue

            # Detect scenario outline
            if line.strip().lower().startswith('scenario outline:') or line.strip().lower().startswith('scenario template:'):
                in_feature_description = False
                current_background = None  # Reset background context
                scenario_name = line.split(':', 1)[1].strip()
                current_scenario_outline = scenario_name
                current_scenario = scenario_name  # Treat as regular scenario for now
                if current_feature:
                    features[current_feature]['scenarios'][current_scenario] = {
                        'steps': [],
                        'tags': current_tags.copy(),
                        'type': 'outline',
                        'examples': []
                    }
                    current_tags = []
                continue

            # Detect scenario
            if line.strip().lower().startswith('scenario:'):
                in_feature_description = False
                current_background = None  # Reset background context
                scenario_name = line.split(':', 1)[1].strip()
                current_scenario = scenario_name
                if current_feature:
                    features[current_feature]['scenarios'][current_scenario] = {
                        'steps': [],
                        'tags': current_tags.copy(),
                        'type': 'scenario'
                    }
                    current_tags = []
                continue

            # Detect examples section
            if line.strip().lower().startswith('examples:'):
                current_examples = True
                continue

            # Handle feature description
            if in_feature_description and current_feature and line.strip():
                features[current_feature]['description'].append(line.strip())
                continue

            # Handle examples table
            if current_examples and current_scenario and line.strip().startswith('|'):
                if current_feature and current_scenario in features[current_feature]['scenarios']:
                    if 'examples' not in features[current_feature]['scenarios'][current_scenario]:
                        features[current_feature]['scenarios'][current_scenario]['examples'] = []
                    features[current_feature]['scenarios'][current_scenario]['examples'].append(line.strip())
                continue

            # Detect steps (lines with Given, When, Then, And, But)
            if line.strip():
                step_keywords = ['given', 'when', 'then', 'and', 'but']
                line_lower = line.strip().lower()

                if any(line_lower.startswith(keyword) for keyword in step_keywords):
                    # Preserve the original step with minimal formatting
                    preserved_step = self._preserve_and_format_step(line.strip())

                    # Add to background if we're in background context
                    if current_background is not None and current_scenario is None:
                        current_background['steps'].append(preserved_step)
                    # Add to current scenario if we have one
                    elif current_scenario and current_feature:
                        features[current_feature]['scenarios'][current_scenario]['steps'].append(preserved_step)

                elif line.strip().startswith('|') and current_scenario and current_feature:
                    # This is a table row, preserve it exactly as is
                    features[current_feature]['scenarios'][current_scenario]['steps'].append(line.strip())
                elif line.strip().startswith('"""') or line.strip().startswith("'''"):
                    # This is a docstring, preserve it exactly as is
                    if current_scenario and current_feature:
                        features[current_feature]['scenarios'][current_scenario]['steps'].append(line.strip())

        return features

    def _preserve_and_format_step(self, step: str) -> str:
        """
        Preserve user content while ensuring proper Gherkin structure.
        Only makes minimal changes for framework compatibility.
        """
        step = step.strip()

        # Normalize step keywords to proper case
        step_lower = step.lower()

        # Fix common keyword variations while preserving content
        if step_lower.startswith('given '):
            return 'Given ' + step[6:]
        elif step_lower.startswith('when '):
            return 'When ' + step[5:]
        elif step_lower.startswith('then '):
            return 'Then ' + step[5:]
        elif step_lower.startswith('and '):
            return 'And ' + step[4:]
        elif step_lower.startswith('but '):
            return 'But ' + step[4:]
        elif step_lower.startswith('* '):
            # Convert asterisk to And for better compatibility
            return 'And ' + step[2:]
        else:
            # If no keyword, add 'And' prefix for framework compatibility
            return 'And ' + step

    def _convert_existing_step_to_framework_format(self, step: str) -> str:
        """
        Minimal conversion for existing Gherkin steps - preserve user content.
        Only ensures proper keyword formatting for framework compatibility.
        """
        # Use the preserve and format method for minimal changes
        return self._preserve_and_format_step(step)


    
    def convert_step_to_gherkin(self, step: str) -> str:
        """Convert a user step to Gherkin format."""
        step_lower = step.lower()
        
        # Check for special patterns first
        if 'main page' in step_lower or 'halaman utama' in step_lower:
            return 'Given I am on the main page'
        
        if 'move to the previous page' in step_lower or 'kembali ke halaman sebelumnya' in step_lower:
            return 'And move to the previous page'
        
        if 'user direct to page' in step_lower:
            # Extract URL from step
            url_match = re.search(r'"(https?://[^"]+)"', step)
            if url_match:
                return f'Then user direct to page "{url_match.group(1)}"'
            return 'Then user direct to page'
        
        # Check for button beside pattern
        if 'button beside' in step_lower:
            text_match = re.search(r'button beside "([^"]+)"', step)
            if text_match:
                return f'And I click button beside "{text_match.group(1)}"'
        
        # Apply pattern matching
        for category, pattern_info in self.step_patterns.items():
            for pattern in pattern_info['patterns']:
                match = re.search(pattern, step_lower)
                if match:
                    gherkin_template = pattern_info['gherkin']
                    
                    # Extract values based on pattern
                    if category == 'input':
                        # For input patterns, we need to extract value and field
                        if len(match.groups()) >= 2:
                            value = match.group(1).strip('"')
                            field = match.group(2).strip('"')
                            return gherkin_template.format(value=value, field=field)
                    elif category == 'search':
                        # For search patterns, extract the query
                        query = match.group(1).strip('"')
                        return gherkin_template.format(query=query)
                    else:
                        # For other patterns, extract the target
                        target = match.group(1).strip('"')
                        return gherkin_template.format(target=target)
        
        # If no pattern matches, try to convert common phrases
        converted = self._convert_common_phrases(step)
        if converted:
            return converted
        
        # Default: return as is with proper Gherkin prefix
        return f'And {step}'
    
    def _cleanup_gherkin_step(self, step: str) -> str:
        """Clean up duplicate keywords and formatting issues in Gherkin steps."""
        # Remove duplicate keywords (e.g., "Given Given" -> "Given")
        step = re.sub(r'\b(And|When|Then|Given)\s+\1\b', r'\1', step)
        
        # Fix common formatting issues
        step = re.sub(r'\s+', ' ', step)  # Remove extra spaces
        step = step.strip()
        
        # Fix "scroll to to" -> "scroll to"
        step = re.sub(r'scroll to to', 'scroll to', step, flags=re.IGNORECASE)
        
        # Fix "When When" -> "When"
        step = re.sub(r'When When', 'When', step)
        
        # Fix "Given Given" -> "Given"
        step = re.sub(r'Given Given', 'Given', step)
        
        # Fix "When And" -> "And"
        step = re.sub(r'When And', 'And', step)
        
        # Fix "And And" -> "And"
        step = re.sub(r'And And', 'And', step)
        
        # Fix "Then Then" -> "Then"
        step = re.sub(r'Then Then', 'Then', step)
        
        # Ensure proper keyword at the beginning
        keywords = ['Given', 'When', 'Then', 'And', 'But']
        step_lower = step.lower()
        
        # If step doesn't start with a keyword, add 'And'
        if not any(step_lower.startswith(keyword.lower()) for keyword in keywords):
            step = f'And {step}'
        
        return step
    
    def _convert_common_phrases(self, step: str) -> Optional[str]:
        """Convert common phrases to Gherkin format."""
        step_lower = step.lower()
        
        # Common conversions
        conversions = {
            'klik': 'click',
            'ketik': 'type',
            'isi': 'fill',
            'pastikan': 'should see',
            'verifikasi': 'verify',
            'scroll': 'scroll to',
            'gulir': 'scroll to',
            'tunggu': 'wait for',
            'cari': 'search for',
            'pencarian': 'search for'
        }
        
        converted_step = step
        for indo, eng in conversions.items():
            converted_step = converted_step.replace(indo, eng)
        
        # Add proper Gherkin prefix
        if any(word in converted_step.lower() for word in ['click', 'type', 'fill', 'scroll', 'search']):
            return f'When {converted_step}'
        elif any(word in converted_step.lower() for word in ['should see', 'verify', 'confirm']):
            return f'Then {converted_step}'
        elif any(word in converted_step.lower() for word in ['wait', 'tunggu']):
            return f'And {converted_step}'
        
        return None
    
    def generate_feature_file(self, feature_name: str, feature_data: Dict) -> str:
        """Generate feature file content with enhanced structure preservation."""
        # Start with feature header
        content = f"Feature: {feature_name}\n"

        # Add feature tags if present
        if 'feature_tags' in feature_data and feature_data['feature_tags']:
            for tag in feature_data['feature_tags']:
                content = f"{tag}\n" + content

        # Add feature description if present
        if 'description' in feature_data and feature_data['description']:
            for desc_line in feature_data['description']:
                content += f"  {desc_line}\n"
        else:
            # Default description
            content += f"  As a user\n"
            content += f"  I want to access {feature_name.lower()} functionality\n"
            content += f"  So that I can perform {feature_name.lower()} operations\n"

        content += f"\n  # base_url: {feature_data['base_url']}\n\n"

        # Add background if present
        if 'background' in feature_data and feature_data['background']:
            content += "  Background:\n"
            for step in feature_data['background']['steps']:
                content += f"    {step}\n"
            content += "\n"

        # Add scenarios
        for scenario_name, scenario_data in feature_data['scenarios'].items():
            # Add scenario tags
            if 'tags' in scenario_data and scenario_data['tags']:
                for tag in scenario_data['tags']:
                    content += f"  {tag}\n"

            # Add scenario header
            scenario_type = scenario_data.get('type', 'scenario')
            if scenario_type == 'outline':
                content += f"  Scenario Outline: {scenario_name}\n"
            else:
                content += f"  Scenario: {scenario_name}\n"

            # Add steps
            for i, step in enumerate(scenario_data['steps']):
                # For .feature files, preserve the original step with minimal formatting
                if self.input_file.endswith('.feature'):
                    formatted_step = self._preserve_and_format_step(step)
                else:
                    # For other formats, use conversion
                    formatted_step = self.convert_step_to_gherkin(step)

                # Clean up the step
                formatted_step = self._cleanup_gherkin_step(formatted_step)

                # Ensure proper Gherkin structure for first step
                if i == 0 and not formatted_step.startswith('Given'):
                    if formatted_step.startswith('When'):
                        formatted_step = formatted_step.replace('When', 'Given', 1)
                    elif formatted_step.startswith('Then'):
                        formatted_step = formatted_step.replace('Then', 'Given', 1)
                    elif formatted_step.startswith('And'):
                        formatted_step = formatted_step.replace('And', 'Given', 1)

                content += f"    {formatted_step}\n"

            # Add examples for scenario outlines
            if 'examples' in scenario_data and scenario_data['examples']:
                content += "\n    Examples:\n"
                for example_line in scenario_data['examples']:
                    content += f"    {example_line}\n"

            content += "\n"

        return content
    
    def convert_features(self):
        """Convert features from input file to .feature format."""
        print("🚀 Starting feature conversion...")
        
        # Read input file
        features = self.read_input_file()
        if not features:
            print("❌ No features found in input file")
            return
        
        print(f"📊 Found {len(features)} features")
        
        # Convert each feature
        for feature_name, feature_data in features.items():
            print(f"🔄 Converting feature: {feature_name}")
            
            # Generate feature content
            content = self.generate_feature_file(feature_name, feature_data)
            
            # Write to file
            filename = self._sanitize_filename(feature_name)
            filepath = os.path.join(self.output_dir, f"{filename}.feature")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Generated: {filepath}")
            print(f"   - Scenarios: {len(feature_data['scenarios'])}")
        
        print(f"\n🎉 Conversion Complete!")
        print(f"📊 Summary:")
        print(f"   - Features converted: {len(features)}")
        print(f"   - Output directory: {self.output_dir}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for file system."""
        # Remove special characters and replace spaces with underscores
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.lower()

def main():
    parser = argparse.ArgumentParser(description="Feature Converter - Convert user-generated features to .feature format")
    parser.add_argument("input_file", help="Input file containing features (supports .txt, .md, .csv)")
    parser.add_argument("--output", default="test_framework/features",
                       help="Output directory for feature files")
    parser.add_argument("--base-url", default="https://your-app-url.com",
                       help="Base URL for the application")
    
    args = parser.parse_args()
    
    # Create converter with base_url
    converter = FeatureConverter(args.input_file, args.output, args.base_url)
    
    # Convert features
    converter.convert_features()

if __name__ == "__main__":
    main() 