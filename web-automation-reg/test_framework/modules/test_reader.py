import pandas as pd

class TestReader:
    def __init__(self, excel_path):
        self.excel_path = excel_path
        print(f"Initializing TestReader with Excel file: {excel_path}")
        
    def get_test_cases(self):
        """Read test cases from Excel file."""
        try:
            print(f"Attempting to read Excel file: {self.excel_path}")
            df = pd.read_excel(self.excel_path)
            print(f"Excel file read successfully. Found {len(df)} rows.")
            print(f"Excel columns: {df.columns.tolist()}")
            print(f"First few rows:\n{df.head()}")
            
            test_cases = {}
            base_urls = {}  # Dictionary to store base URLs for each test case
            scenario_names = {}  # Dictionary to store scenario names for each test case
            
            # Check if the expected columns exist
            if 'Test Case ID' in df.columns and 'Test Steps' in df.columns:
                # Use the Test Case ID and Test Steps columns
                for idx, row in df.iterrows():
                    tc_id = row['Test Case ID']
                    steps_text = str(row['Test Steps'])
                    
                    # Extract scenario name from the Scenarios column if it exists
                    scenario_name = f"Test Case {tc_id}"
                    if 'Scenarios' in df.columns:
                        scenario_text = str(row['Scenarios'])
                        if scenario_text.lower() != "nan" and scenario_text.strip():
                            scenario_name = scenario_text.strip()
                    scenario_names[tc_id] = scenario_name
                    
                    # Extract base URL from the Base URL column if it exists
                    base_url = ""
                    if 'Base URL' in df.columns:
                        base_url = str(row['Base URL'])
                        if base_url.lower() == "nan":
                            base_url = ""
                        base_urls[tc_id] = base_url
                    
                    print(f"Processing test case {tc_id}: {steps_text[:50]}...")
                    
                    steps = self._process_steps(steps_text, base_url)
                    
                    test_cases[tc_id] = steps
            else:
                # Fallback to the original approach (assuming first column contains steps)
                for idx, row in df.iterrows():
                    tc_id = f"TC{idx+1}"
                    steps_text = str(row[0])  # First column
                    print(f"Processing test case {tc_id}: {steps_text[:50]}...")
                    
                    # Split by newlines or periods
                    steps = [step.strip() for step in steps_text.replace('\n', '.').split('.') if step.strip()]
                    print(f"  - Extracted {len(steps)} steps")
                    test_cases[tc_id] = steps
                    scenario_names[tc_id] = f"Test Case {tc_id}"
            
            print(f"Total test cases extracted: {len(test_cases)}")
            return test_cases, base_urls, scenario_names  # Return test cases, base URLs, and scenario names
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            import traceback
            traceback.print_exc()
            return {}, {}, {}  # Return empty dictionaries in case of error

    def _process_steps(self, steps_text, base_url=""):
        """Process the steps text into a list of steps."""
        import re
        steps = []
        
        # First, normalize the text by replacing newlines with spaces
        steps_text = steps_text.replace('\n', ' ').replace('\r', ' ')
        
        # Split by numbered steps (e.g., "1. Step one")
        step_pattern = re.compile(r'(\d+\.\s*)')
        step_parts = step_pattern.split(steps_text)
        
        # Filter out empty parts and numbers
        step_parts = [part.strip() for part in step_parts if part.strip() and not re.match(r'^\d+\.$', part.strip())]
        
        # Process each step
        for step in step_parts:
            # Skip empty steps
            if not step:
                continue
            
            # Clean up the step
            step = step.strip()
            
            # Handle cases where a step might contain multiple sentences or steps
            # Split by common step patterns if they appear in the same step
            if 'Pastikan teks' in step and step.count('Pastikan teks') > 1:
                # Split by "Pastikan teks" if it appears multiple times
                sub_steps = re.split(r'(?=Pastikan teks)', step)
                for sub_step in sub_steps:
                    sub_step = sub_step.strip()
                    if sub_step and len(sub_step) > 10:  # Minimum meaningful step length
                        steps.append(sub_step)
            else:
                # Add the step to the list
                steps.append(step)
        
        print(f"  - Extracted {len(steps)} steps: {steps}")
        return steps
