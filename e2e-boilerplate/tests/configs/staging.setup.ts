import { googleLogin, STORAGE_STATE_IAP } from '@/auth/google-login.helper'
import { USERS, USERS_SHOULD_LOGIN_APP } from '@/constants/users.constant'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'
import {
  AUTH_LOGIN_SEQUENTIAL,
  AUTH_SETUP_ENABLED,
  CI,
  ENVIRONMENT,
  IAP_ENABLED,
  PRODUCTION_ONLY,
} from '@/utils/env.util'
import { chromium } from 'playwright-extra'

import { log } from '@/utils/playwright-config.util'
import { test as setup } from '@playwright/test'

if (!(PRODUCTION_ONLY || (CI && ENVIRONMENT !== 'staging'))) {
  setup.describe.configure({ mode: CI || AUTH_LOGIN_SEQUENTIAL ? 'serial' : 'parallel' })

  for (const userKey of USERS_SHOULD_LOGIN_APP) {
    // eslint-disable-next-line no-empty-pattern
    setup(`@setup @staging Setting up staging authentication for ${userKey}`, async ({}, testInfo) => {
      // 4min per user to login
      testInfo.setTimeout(4 * 60 * 1000)

      if (!CI) {
        const isConfigValid = await checkConfig('staging', userKey)
        if (isConfigValid) {
          log(`🔑 Cached auth files detected for ${userKey}; skipping setup staging auth.`, 'staging')
          return
        }
      }

      log(`🔑 Logging in ${userKey} with user data stored in the 'src/auth/users.ts' file`, 'staging')

      const browser = await chromium.launch({ headless: false })

      // stop staging login process when don't need auth
      if (!AUTH_SETUP_ENABLED) {
        await browser.close()
        return
      }

      const context = await browser.newContext({
        storageState: IAP_ENABLED ? STORAGE_STATE_IAP : undefined,
      })

      const page = await context.newPage()

      const user = USERS[userKey]
      await googleLogin(page, user, 'staging')

      await context.close()

      if (!CI) generateConfigAuth('staging', userKey)

      await browser.close()
    })
  }
}
