import { USERS_TESTING } from '@/auth/users'
import { z } from 'zod'

export const UserSchema = z.object({
  email: z.string().email(),
  password: z.string().min(3),
})

export type UserBase = z.infer<typeof UserSchema>

export type User = UserBase & {
  pathFile: string
}

export type UserKey = keyof typeof USERS_TESTING
export type Users = Record<keyof typeof USERS_TESTING, User>

/**
 * Don't change this USERS constant
 * It will be generated from USERS_TESTING
 * This is the constant that will be used for testing
 */
export const USERS: Users = Object.keys(USERS_TESTING).reduce((acc, key) => {
  const user = USERS_TESTING[key]

  const pathFile = `.auth/${key.replace(/_/g, '-').toLowerCase()}.json`

  return {
    ...acc,
    [key]: {
      ...user,
      email: user.email || ' ',
      password: user.password || ' ',
      pathFile,
    },
  }
}, {} as Users)

export const USERS_SHOULD_LOGIN_APP = Object.keys(USERS).filter((key) => key !== 'IAP') as UserKey[]
export const DEFAULT_USER = (USERS[process.env.DEFAULT_USER] || Object.values(USERS)[0]) as User
export const USER_IAP = USERS['IAP'] || DEFAULT_USER
