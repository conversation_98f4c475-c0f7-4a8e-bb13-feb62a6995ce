image: asia-southeast2-docker.pkg.dev/belajar-id-artifacts/docker-utils/playwright:1.41.1-1

include:
  # https://gitlab.com/wartek-id/e2e-web/cicd-templates/-/blob/main/e2e-web.gitlab-ci.yml
  - project: wartek-id/e2e-web/cicd-templates
    file: e2e-web.gitlab-ci.yml
    ref: main
  - project: wartek-id/e2e-web/e2e-trigger
    file: config/groups/core-webex.yml # 💣 UPDATE THIS: change this to your own group config https://gitlab.com/wartek-id/e2e-web/e2e-trigger/-/tree/main/config/groups
    ref: main

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_PIPELINE_SOURCE == "trigger"'
    - if: '$CI_PIPELINE_SOURCE == "api"'
    - if: '$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS'
      when: never
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_COMMIT_TAG'

variables:
  NODE_CACHE_DIR: $CI_PROJECT_DIR/node_modules/
  GITLAB_AUTH_TOKEN: $FE_TOOLBOX_READ_REGISTRY_TOKEN
  GOOGLE_APPLICATION_CREDENTIALS: $AUTOMATION_TEST_KEY

stages:
  - before-ci
  - test
  - after-test
  - after-ci

.run-production:
  rules:
    - if: $STAGING_ONLY == "true"
      when: never
    - if: $PRODUCTION_ONLY == "true"

.run-staging:
  rules:
    - if: $PRODUCTION_ONLY == "true"
      when: never
    - if: $STAGING_ONLY == "true"

.manual-when-update-snapshot-visual-test:
  rules:
    - if: '$RUN_UPDATE_SNAPSHOT_VISUAL_TEST == "true"'
      when: manual

.notify-slack-update-baseline-visual-test:
  script:
    # Check .env.merge-request and load the environment variables (to get $MERGE_REQUEST_LINK)
    - |
      if [ -f .env.merge-request ]; then
        source .env.merge-request;
      else
        echo "No .env.merge-request file found";
        exit 1;  # Optionally add an exit statement if this step is critical
      fi
    - |
      ## Converting newlines to spaces for Slack channel IDs
      CHANNELS=$(echo "$SLACK_CHANNEL_IDS" | tr '\n' ' ')

      # Format mentions properly with <@ID>
      # First, ensure we remove any accidental whitespace that might lead to formatting issues
      SLACK_MENTIONS=$(echo "$SLACK_MENTIONS" | tr -s '[:space:]')

      # Then, format mentions properly by ensuring each is wrapped with <>
      MENTIONS=""
      for id in $SLACK_MENTIONS; do
        # Ensure there is no leading @ in the current format of id
        clean_id="${id#@}"  # Remove any @ if present
        MENTIONS+="<@$clean_id> "
      done

      # Trim the trailing space at the end of mentions string
      MENTIONS=$(echo "$MENTIONS" | sed 's/ $//')

      # Loop over each channel and send a message
      for CHANNEL in $CHANNELS
      do
        curl -X POST "https://slack.com/api/chat.postMessage" \
        -H "Authorization: Bearer $CORE_WEBEX_SLACK_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
          \"channel\": \"$CHANNEL\",
          \"text\": \"Hello team, I've just updated the visual test snapshot baseline for the *$ENVIRONMENT* environment. Please check out this merge request: $MERGE_REQUEST_LINK. cc $MENTIONS\",
          \"link_names\": 1
        }"
      done

prepare:
  stage: before-ci
  script:
    - npm ci
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - $NODE_CACHE_DIR
    policy: pull-push
  artifacts:
    paths:
      - node_modules/

typecheck-lint:
  stage: test
  script:
    - cp $ENVS .env
    - npm run test:utils
    - npm run typecheck
    - npm run lint

e2e-test-staging:
  extends:
    - .template_test
  rules:
    - !reference [.manual-when-update-snapshot-visual-test, rules]
    - !reference [.run-staging, rules]
  variables:
    ENVIRONMENT: staging
    STAGING_ONLY: 'true'
  script:
    - cp $ENVS .env
    - npm run write-kv
    - npx tsx scripts/ci-filter-test-keys-defined-in-MR.ts
    - !reference [.template_test, script]
    # - you can extend the script here

e2e-test-production:
  extends:
    - .template_test
  rules:
    - !reference [.manual-when-update-snapshot-visual-test, rules]
    - !reference [.run-production, rules]
  variables:
    ENVIRONMENT: production
    PRODUCTION_ONLY: 'true'
  script:
    - cp $ENVS .env
    - npm run write-kv
    - npx tsx scripts/ci-filter-test-keys-defined-in-MR.ts
    - !reference [.template_test, script]
    # - you can extend the script here

e2e-update-baseline-visual-production:
  stage: test
  rules:
    - if: '$RUN_UPDATE_SNAPSHOT_VISUAL_TEST != "true"'
      when: never
    - !reference [.run-production, rules]
  variables:
    ENVIRONMENT: production
    PRODUCTION_ONLY: 'true'
  script:
    - cp $ENVS .env
    - npm run write-kv
    - npx tsx scripts/update-baseline-visual-test.ts
    - !reference [.notify-slack-update-baseline-visual-test, script]
  artifacts:
    paths:
      - playwright-report/

e2e-update-baseline-visual-staging:
  stage: test
  rules:
    - if: '$RUN_UPDATE_SNAPSHOT_VISUAL_TEST != "true"'
      when: never
    - !reference [.run-staging, rules]
  variables:
    ENVIRONMENT: staging
    STAGING_ONLY: 'true'
  script:
    - cp $ENVS .env
    - npm run write-kv
    - npx tsx scripts/update-baseline-visual-test.ts
    - !reference [.notify-slack-update-baseline-visual-test, script]
  needs:
    - job: prepare
      artifacts: true
    - job: e2e-update-baseline-visual-production
      optional: true
  artifacts:
    paths:
      - playwright-report/

pages:
  stage: after-ci
  dependencies:
    - e2e-test-staging
    - e2e-test-production
  rules:
    - exists:
        - playwright-report/
  script:
    - rm -rf public
    - mkdir public
    - cp -r playwright-report/* public/
  artifacts:
    paths:
      - public
