{"version": "1.9.0", "name": "e2e-boilerplate", "description": "boilerplate for e2e tests", "scripts": {"dev:kv": "npx nodemon --quiet --exec \"tsx scripts/write-kv-constants.ts\" -e ts,json --exitcrash --ignore gsheet-kv.ts", "dev": "run-p dev:kv", "dev:playwright": "playwright test", "test": "playwright test", "test:staging": "STAGING_ONLY=true npm run test -- --grep @staging", "test:production": "PRODUCTION_ONLY=true npm run test -- --grep @production", "test:visual": "npm run test -- --grep @visual", "test:ui": "npm run test -- --grep @setup && npx npm run test -- --ui", "test:debug": "npm run test -- --debug", "test:headed": "npm run test -- --headed", "test:snapshot": "npm run test:visual -- --update-snapshots", "auth:reset": "find .auth .auth-dev -type f -name \"*.json\" -delete ;npm run test -- -g @setup", "auth:reset:production": "find .auth .auth-dev -type f -name \"*.production.json\" -delete ;npx playwright test -g '(?=.*@setup)(?=.*@production)'", "auth:reset:staging": "find .auth .auth-dev -type f -name \"*.staging.json\" -delete ;npx playwright test -g '(?=.*@setup)(?=.*@staging)'", "docker": "docker run --rm --network host -v $(pwd):/work/ -w /work/ -e GITLAB_AUTH_TOKEN=$GITLAB_AUTH_TOKEN -it mcr.microsoft.com/playwright:v1.41.1-jammy /bin/bash", "test:utils": "npm run write-kv && vitest", "write-kv": "tsx scripts/write-kv-constants.ts", "typecheck": "tsc --noEmit --project tsconfig.ci.json", "report": "playwright show-report", "lint": "eslint . --fix", "format": "prettier --write .", "postinstall": "playwright install chromium && simple-git-hooks", "check:node": "node -e \"if (parseInt(process.versions.node.split('.')[0]) < 20) { console.error('Node.js version 20 (LTS) or higher is required.'); process.exit(1); }\"", "preinstall": "npm run check:node", "pretest": "npm run check:node"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@playwright/test": "^1.41.1", "@wartek-id/e2e-jira-labelling": "^1.0.1", "@wartek-id/toolbox-event-tracker": "^1.2.1", "@wartek-id/toolbox-webex-helper": "^1.0.1", "eslint-plugin-playwright": "^1.6.2", "fs-extra": "^11.2.0", "google-auth-library": "^9.14.0", "lodash": "^4.17.21", "nanoid": "^3.3.7", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "tiny-invariant": "^1.3.3", "ts-node": "^10.9.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@total-typescript/ts-reset": "^0.5.1", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/node": "^20.16.1", "@types/shelljs": "^0.8.15", "@types/xml2js": "^0.4.14", "@types/yargs": "^17.0.33", "app-root-path": "^3.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "eslint": "^9.9.0", "globals": "^15.9.0", "lint-staged": "^15.2.9", "nock": "^13.5.5", "nodemon": "^3.1.4", "npm-run-all": "^4.1.5", "picocolors": "^1.0.1", "prettier": "^3.3.3", "shelljs": "^0.8.5", "simple-git-hooks": "^2.11.1", "tsx": "^4.17.0", "typescript": "^5.5.4", "typescript-eslint": "^7.18.0", "vitest": "^2.0.5", "xml2js": "^0.6.2", "yargs": "^17.7.2", "znv": "^0.4.0"}, "lint-staged": {"*.{js,ts}": ["eslint --fix"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "engines": {"node": ">=20.0.0"}, "volta": {"node": "20.11.1"}, "overrides": {"eslint": "^9.9.0"}}