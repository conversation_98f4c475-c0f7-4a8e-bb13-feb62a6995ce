import re
import os
from typing import Dict, List, Tuple

class GherkinReader:
    def __init__(self, feature_dir="features", keep_original_steps=False):
        self.feature_dir = feature_dir
        self.keep_original_steps = keep_original_steps
        print(f"Initializing GherkinReader with feature directory: {feature_dir}")
        if keep_original_steps:
            print("  - Keeping original Gherkin steps (no conversion to Indonesian)")
        else:
            print("  - Converting Gherkin steps to Indonesian")
        
    def get_test_cases(self, requested_tc_ids=None) -> Tuple[Dict[str, Dict], Dict[str, str]]:
        """Read test cases from .feature files."""
        test_cases = {}
        base_urls = {}
        
        if not os.path.exists(self.feature_dir):
            print(f"Feature directory not found: {self.feature_dir}")
            return test_cases, base_urls
            
        feature_files = [f for f in os.listdir(self.feature_dir) if f.endswith('.feature')]
        
        if not feature_files:
            print(f"No .feature files found in {self.feature_dir}")
            return test_cases, base_urls
            
        print(f"Found {len(feature_files)} feature files")
        
        for feature_file in feature_files:
            feature_path = os.path.join(self.feature_dir, feature_file)
            print(f"Processing feature file: {feature_file}")
            
            scenarios = self._parse_feature_file(feature_path, requested_tc_ids)
            
            for scenario_id, scenario_data in scenarios.items():
                test_cases[scenario_id] = {
                    'steps': scenario_data['steps'],
                    'scenario_name': scenario_data['title']
                }
                base_urls[scenario_id] = scenario_data.get('base_url', '')
                
        print(f"Total test cases extracted: {len(test_cases)}")
        return test_cases, base_urls
    
    def _parse_feature_file(self, feature_path: str, requested_tc_ids=None) -> Dict[str, Dict]:
        """Parse a single .feature file and extract scenarios."""
        scenarios = {}
        
        try:
            with open(feature_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            # Extract base URL from feature level (before scenarios)
            feature_base_url = self._extract_feature_base_url(content)
            print(f"  - Feature base URL: {feature_base_url}")
                
            # Parse scenarios more robustly by looking for @ tags followed by Scenario
            # This ensures we capture the correct tags for each scenario
            scenario_pattern = r'(@[^\n]*)\n\s*Scenario\s*:\s*([^\n]*)\n(.*?)(?=\n\s*@|\n\s*$|\Z)'
            scenario_matches = re.findall(scenario_pattern, content, re.DOTALL)
            
            for i, (tags_line, title, steps_content) in enumerate(scenario_matches, 1):
                # Extract tags from the tags line
                tags = re.findall(r'@([^\s]+)', tags_line)
                
                # Parse the steps from the steps content, including data tables
                steps = self._parse_steps_with_data_tables(steps_content.strip())
                
                if steps:
                    # Try to extract TC ID from scenario tags first, then title, fallback to TC{i}
                    scenario_id = self._extract_tc_id_from_tags(tags, title, i)
                    
                    # Only process requested test cases if filter is provided
                    if requested_tc_ids is None or scenario_id in requested_tc_ids:
                        scenarios[scenario_id] = {
                            'title': title,
                            'tags': tags,
                            'steps': steps,
                            'base_url': feature_base_url
                        }
                        print(f"  - Scenario {i}: {title}")
                        print(f"    - Extracted {len(steps)} steps: {steps}")
                    # Don't print anything for skipped scenarios to reduce output
                    
        except Exception as e:
            print(f"Error parsing feature file {feature_path}: {e}")
            
        return scenarios
    
    def _parse_scenario_block(self, block: str, scenario_num: int, feature_base_url: str = "") -> Dict:
        """Parse a single scenario block."""
        lines = block.strip().split('\n')
        
        # Extract scenario title and tags
        title = ""
        tags = []
        steps = []
        base_url = feature_base_url  # Use feature-level base URL as default
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Extract tags from lines starting with @
            if line.startswith('@'):
                # Extract all tags from the line
                tag_matches = re.findall(r'@([^\s]+)', line)
                tags.extend(tag_matches)
                continue
                
            # Extract title from first line
            if not title and not line.startswith(('Given', 'When', 'Then', 'And', 'But')):
                title = line.strip(':')
                continue
                
            # Extract base URL from comments (scenario-level override)
            if line.startswith('#') and 'base_url' in line.lower():
                # Try different patterns for base URL extraction
                url_patterns = [
                    r'base_url[:\s]+([^\s]+)',
                    r'base_url[:\s]*["\']([^"\']+)["\']',
                    r'base_url[:\s]*([^\s]+)'
                ]
                
                for pattern in url_patterns:
                    url_match = re.search(pattern, line, re.IGNORECASE)
                    if url_match:
                        base_url = url_match.group(1)
                        print(f"    - Found scenario base URL: {base_url}")
                        break
                continue
                
            # Extract steps
            if line.startswith(('Given', 'When', 'Then', 'And', 'But')):
                step = self._clean_step(line, self.keep_original_steps)
                if step:
                    steps.append(step)
                    
        if steps:
            print(f"  - Scenario {scenario_num}: {title}")
            print(f"    - Extracted {len(steps)} steps: {steps}")
            return {
                'title': title,
                'tags': tags,
                'steps': steps,
                'base_url': base_url
            }
            
        return None
    
    def _extract_feature_base_url(self, content: str) -> str:
        """Extract base URL from feature-level comments or uncommented lines."""
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            # Check both commented and uncommented base_url lines
            if ('base_url' in line.lower() and 
                (line.startswith('#') or not line.startswith('#'))):
                
                # Try different patterns for base URL extraction
                url_patterns = [
                    r'base_url[:\s]+([^\s]+)',
                    r'base_url[:\s]*["\']([^"\']+)["\']',
                    r'base_url[:\s]*([^\s]+)'
                ]
                
                for pattern in url_patterns:
                    url_match = re.search(pattern, line, re.IGNORECASE)
                    if url_match:
                        return url_match.group(1)
        
        return ""
    
    def _extract_tc_id_from_tags(self, tags: list, title: str, scenario_num: int) -> str:
        """Extract test case ID from scenario tags first, then title."""
        # Look for TEST_RP pattern in tags first
        for tag in tags:
            test_rp_match = re.search(r'TEST_RP-\d+', tag)
            if test_rp_match:
                return test_rp_match.group(0)
            
            # Look for TC pattern in tags
            tc_match = re.search(r'TC\d+', tag)
            if tc_match:
                return tc_match.group(0)
        
        # If not found in tags, look in title
        return self._extract_tc_id(title, scenario_num)
    
    def _extract_tc_id(self, title: str, scenario_num: int) -> str:
        """Extract test case ID from scenario title."""
        # Look for TEST_RP pattern in title (e.g., "TEST_RP-549")
        test_rp_match = re.search(r'TEST_RP-\d+', title)
        if test_rp_match:
            return test_rp_match.group(0)
        
        # Look for TC pattern in title (e.g., "Sample Test Case (TC99)" or "@TC99")
        tc_match = re.search(r'TC\d+', title)
        if tc_match:
            return tc_match.group(0)
        
        # Fallback to TC{scenario_num}
        return f"TC{scenario_num}"
    
    def _clean_step(self, step_line: str, keep_original=False) -> str:
        """Clean and convert Gherkin step to natural language."""
        # Remove Gherkin keywords
        step = re.sub(r'^(Given|When|Then|And|But)\s+', '', step_line.strip())
        
        # If keep_original is True, return the step as is
        if keep_original:
            return step
        
        # Convert common Gherkin patterns to natural language
        step = self._convert_gherkin_patterns(step)
        
        return step

    def _parse_steps_with_data_tables(self, steps_content: str) -> List[str]:
        """Parse steps and include data table content where applicable."""
        steps = []
        lines = steps_content.split('\n')
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines and comments
            if not line or line.startswith('#'):
                i += 1
                continue

            # Process Gherkin step lines
            if line.startswith(('Given', 'When', 'Then', 'And', 'But')):
                step = self._clean_step(line, self.keep_original_steps)

                # Special handling for "And |" format - treat as individual assertion steps
                if line.strip().startswith('And |'):
                    # This is an individual assertion step, not a data table
                    if step:
                        steps.append(step)
                    i += 1
                    continue

                # Check if the next lines contain a data table (but not "And |" steps)
                data_table_content = []
                j = i + 1
                while j < len(lines):
                    next_line = lines[j].strip()

                    # Stop if we hit another step or empty line
                    if (next_line.startswith(('Given', 'When', 'Then', 'And', 'But')) or
                        not next_line or next_line.startswith('#')):
                        break

                    # Collect data table lines (lines with | or indented content)
                    # But exclude "And |" lines as they are individual steps
                    if ('|' in next_line or next_line.startswith(' ')) and not next_line.startswith('And |'):
                        data_table_content.append(next_line)
                    else:
                        break

                    j += 1

                # If we found data table content, append it to the step
                if data_table_content:
                    # Clean and format the data table content
                    formatted_data = self._format_data_table(data_table_content)
                    if formatted_data:
                        step = f"{step}\n{formatted_data}"
                    i = j  # Skip the data table lines
                else:
                    i += 1

                if step:
                    steps.append(step)
            else:
                i += 1

        return steps

    def _format_data_table(self, data_lines: List[str]) -> str:
        """Format data table lines into a readable format."""
        formatted_items = []

        for line in data_lines:
            line = line.strip()
            if not line:
                continue

            # Handle pipe-separated tables
            if '|' in line:
                # Extract content between pipes
                items = [item.strip() for item in line.split('|') if item.strip()]
                formatted_items.extend(items)
            else:
                # Handle indented content
                formatted_items.append(line)

        # Remove duplicates while preserving order
        seen = set()
        unique_items = []
        for item in formatted_items:
            if item not in seen and len(item) > 3:  # Filter out very short items
                seen.add(item)
                unique_items.append(item)

        if unique_items:
            return '\n'.join(f"| {item} |" for item in unique_items)

        return ""

    def _convert_gherkin_patterns(self, step: str) -> str:
        """Convert Gherkin patterns to natural language steps."""
        step_lower = step.lower()
        
        # Navigation patterns
        if "i am on" in step_lower or "i visit" in step_lower:
            return f"Buka halaman {step.split('"')[1] if '"' in step else 'utama'}"
            
        # Click patterns
        if "i click" in step_lower or "i press" in step_lower:
            target = self._extract_quoted_text(step)
            if "button next to" in step_lower:
                return f"Klik tombol di sebelah '{target}'"
            elif target.lower() == "cari":
                return f"Klik tombol \"{target}\""
            return f"Klik {target}"
            
        # Type patterns
        if "i type" in step_lower or "i enter" in step_lower or "i fill" in step_lower:
            if "search" in step_lower:
                value = self._extract_quoted_text(step)
                return f"Isi kolom pencarian dengan kata \"{value}\""
            else:
                value = self._extract_quoted_text(step)
                field = self._extract_field_name(step)
                return f"Isi kolom {field} dengan kata \"{value}\""
                
        # Assertion patterns
        if "i should see" in step_lower or "i can see" in step_lower:
            text = self._extract_quoted_text(step)
            if "page" in step_lower:
                return f"Pastikan halaman berpindah ke {text}"
            return f"Pastikan teks \"{text}\" muncul di halaman"
            
        # Scroll patterns
        if "i scroll" in step_lower:
            target = self._extract_quoted_text(step)
            return f"Scroll ke bagian {target}"
            
        # Navigation back patterns
        if "navigate back" in step_lower or "go back" in step_lower:
            return "berpindah ke halaman sebelumnya"
            
        # Default: return as is
        return step
    
    def _extract_quoted_text(self, text: str) -> str:
        """Extract text between quotes."""
        match = re.search(r'"([^"]+)"', text)
        return match.group(1) if match else ""
    
    def _extract_field_name(self, text: str) -> str:
        """Extract field name from step."""
        # Look for common field patterns
        field_patterns = [
            r'in the (\w+) field',
            r'into the (\w+) field',
            r'the (\w+) field',
            r'(\w+) input'
        ]
        
        for pattern in field_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
                
        return "input" 