# Changelog

## Version 1.4.1 (2024-04-04)

### Changes

- Chore: add new function in `global.helpers`: `getTestEnv` and `execFunctionOnEnv`

## Version 1.4.0 (2024-04-03)

### Changes

- Feat: Able to run specific test cases that are defined in merge request description, so that we can run only the test cases that are related to the changes in the merge request.

## Version 1.3.3 (2024-04-03)

### Changes

- Fix: Prevent send tracking test case when @setup test is failed.

## Version 1.3.2 (2024-04-02)

### Changes

- Fix: regex on playwright test list command

## Version 1.3.1 (2024-04-02)

### Changes

- Fix: Add new env variable `USE_GREP_FILTER` to allow unfiltered test cases to run in CI so that we can list all test cases that written in the test suite.
- Fix: Modify the script to filter and transform `playwright test --list` so that the JIRA keys are not duplicated and do not include "@" symbols.

## Version 1.3.0 (2024-03-27)

### Changes

- Feat: Add a script to automatically update the Jira ticket labels and components by adding the "automated" label and "automated" component when the test case is executed.
- Fix: Longer timeout on login and default timeout on test cases in CI.
- Fix: Package.json script to reset auth cache.

## Version 1.2.1 (2024-02-26)

### Changes

- Chore: adjust default config of video + trace mode to `retain-on-failure` on CI.

## Version 1.2.0 (2024-01-22)

### Changes

- Chore: bump dependencies to latest minor & patch changes
- Fix: typing on constant default device

## Version 1.1.7 (2024-01-15)

- Fixed consent screen handling in the `googleLogin` function. Now it detects if the consent screen has more than one flow and handles it accordingly.

## Version 1.1.6 (2024-01-03)

### Changes

- Chore: increase timeout on login setup from hardcoded 3 minutes to 40 seconds per user.

## Version 1.1.5 (2023-12-14)

### Changes

- Fix: Replace the usage of cache on CI with artifacts for more reliable CI job especially when running triggered by other repository.

## Version 1.1.4 (2023-12-04)

### Changes

- Fix: change CI_PIPELINE_SOURCE condition from 'trigger' to 'pipeline' because it is multiproject downstream pipeline. Source: <https://docs.gitlab.com/ee/ci/pipelines/downstream_pipelines.html?tab=Multi-project+pipeline#use-rules-to-control-downstream-pipeline-jobs>.

## Version 1.1.3 (2023-11-30)

### Changes

- Fix: success and failed rate calculation.
- Fix: skip tracking event when triggering pipeline from other repository (via `trigger` source pipeline).
- Chore: don't need to run `prepare` job when cache is available.

## Version 1.1.2 (2023-11-27)

### Changes

- Fix: skip tracking event when running specific test cases.

## Version 1.1.1 (2023-11-24)

### Changes

- Fix: use `CI_PIPELINE_ID` instead of nanoid to accumulate the result of staging and production test cases.
- Fix: failed test cases because of `extraHTTPHeaders`.

## Version 1.1.0 (2023-11-23)

### New Features

- **E2E Test Case Tracker as Custom Reporter**
  - **Integration**: Script integrated into CI, exclusively for the main branch.
  - **Metrics Tracked**:
    - Success Rate: Percentage of passed tests.
    - Failed Rate: Percentage of failed tests.
    - Test Case Count: Number of test cases, categorized by tags and the overall total.
  - **Dashboard Access**: Detailed analytics available on [Looker Studio Dashboard](https://lookerstudio.google.com/u/0/reporting/d8580779-ff11-4ad3-9987-8a0d440d13b5/page/p_4cf1oorybd).
  - Note: When the custom reporter is already stable, there will be patch changes to move it to the FE-toolbox repository. This is done to minimize maintenance efforts and reduce the possibility of unintended changes.

## Version 1.0.9 (2023-10-19)

- Fix: IAP login waits for URL changes now it only waits for network idle (if already on the login page) otherwise it will wait for the URL to change.

## Version 1.0.8 (2023-10-11)

- Chore: Bump dependencies
  - Playwright from 1.37 to 1.39 (check this changedoc for more info: <https://playwright.dev/docs/release-notes#version-139>)
  - Fix Zod vulnerability
- Fix: IAP login waits for URL changes now it only waits for network idle
- Fix: Google login expects the first text to exist instead of looking for many text

## Version 1.0.7 (2023-09-14)

- Fix: Avoid running IAP authentication setup and refrain from using IAP storage state in production job testing.

## Version 1.0.6 (2023-09-13)

- Fix: IAP login issue to wait until the page is redirected to the staging page.

## Version 1.0.5 (2023-09-11)

- Fix: Execute `[env].setup` when `IAP_ENABLED` is set to `false` to ensure the correct usage of IAP.
- Fix: Display Report URL on CI based on the GitLab project name
- Chore: Optionally use Bun and Upgrade playwright version to 1.37.1
- Chore: Convert `test/playwright-config.util` from JavaScript to TypeScript.

## Version 1.0.4 (2023-09-07)

- Fix: command `test:ui` can't run properly on some windows machine.

## Version 1.0.3 (2023-09-06)

- Refactor: Improved maintainability of the Google login function.
- Fix: Utilized `STAGING_ONLY` and `PRODUCTION_ONLY` for staging and production GitLab CI jobs.
- Fix: Resolved intermittent IAP issue in the staging setup.
- Fix: Resolved issue with `storageState` when `IAP_ENABLED` is set to `false`.

## Version 1.0.2 (2023-09-05)

- Fixed: Add fallback check when logging in with Google and waiting for the popup to show up.

## Version 1.0.1 (2023-09-04)

- Fixed: Developer experience issue on auth cache check and no auth flow (bug in checking value on env file)
- Fixed: Env check on mode staging/production only
- Fixed: Run IAP only on `IAP_ENABLED` or not `PRODUCTION_ONLY`
- Chore: Split IAP user as separated user testing.
  - Due to changes in src/auth/users.ts adding `IAP` user, now every project recommended to have env for `USER_EMAIL_IAP` and `USER_PASSWORD_IAP` for IAP user.
