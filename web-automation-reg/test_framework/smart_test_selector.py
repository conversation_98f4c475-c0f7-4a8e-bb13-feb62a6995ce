#!/usr/bin/env python3
"""
Smart Test Selector for AI Test Framework
Intelligently selects which tests to run based on risk analysis, recent changes, and failure patterns
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Set
from dataclasses import dataclass
import yaml
from pathlib import Path


@dataclass
class TestMetadata:
    """Metadata for test case analysis"""
    test_case: str
    last_execution: datetime
    last_status: str  # 'passed', 'failed', 'skipped'
    execution_count: int
    avg_execution_time: float
    failure_rate: float
    complexity_score: int  # 1-10 based on steps, interactions, etc.
    risk_score: float  # 0-1 based on business impact
    dependencies: List[str]
    tags: List[str]


class SmartTestSelector:
    """Intelligently selects tests to run based on various criteria"""
    
    def __init__(self, metadata_file="test_framework/cache/test_metadata.json"):
        self.metadata_file = metadata_file
        self.test_metadata = self._load_metadata()
        
        # Configuration
        self.config = self._load_config()
        
        # Risk weights for different factors
        self.risk_weights = {
            'recent_failure': 0.3,
            'high_complexity': 0.2,
            'high_risk': 0.25,
            'long_execution_time': 0.15,
            'low_coverage': 0.1
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config file"""
        try:
            with open('config/config.yaml', 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"Warning: Could not load config.yaml: {e}")
            return {}
    
    def _load_metadata(self) -> Dict[str, TestMetadata]:
        """Load test metadata from file"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    data = json.load(f)
                    metadata = {}
                    for tc, meta in data.items():
                        metadata[tc] = TestMetadata(
                            test_case=tc,
                            last_execution=datetime.fromisoformat(meta['last_execution']),
                            last_status=meta['last_status'],
                            execution_count=meta['execution_count'],
                            avg_execution_time=meta['avg_execution_time'],
                            failure_rate=meta['failure_rate'],
                            complexity_score=meta['complexity_score'],
                            risk_score=meta['risk_score'],
                            dependencies=meta['dependencies'],
                            tags=meta['tags']
                        )
                    return metadata
        except Exception as e:
            print(f"Warning: Could not load test metadata: {e}")
        
        return {}
    
    def _save_metadata(self):
        """Save test metadata to file"""
        try:
            os.makedirs(os.path.dirname(self.metadata_file), exist_ok=True)
            data = {}
            for tc, meta in self.test_metadata.items():
                data[tc] = {
                    'last_execution': meta.last_execution.isoformat(),
                    'last_status': meta.last_status,
                    'execution_count': meta.execution_count,
                    'avg_execution_time': meta.avg_execution_time,
                    'failure_rate': meta.failure_rate,
                    'complexity_score': meta.complexity_score,
                    'risk_score': meta.risk_score,
                    'dependencies': meta.dependencies,
                    'tags': meta.tags
                }
            
            with open(self.metadata_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save test metadata: {e}")
    
    def select_tests(self, 
                    all_test_cases: List[str],
                    max_tests: int = 50,
                    strategy: str = "risk_based",
                    include_failed: bool = True,
                    include_recent: bool = True) -> List[str]:
        """
        Select tests to run based on strategy
        
        Args:
            all_test_cases: All available test cases
            max_tests: Maximum number of tests to select
            strategy: Selection strategy ('risk_based', 'time_based', 'coverage_based', 'hybrid')
            include_failed: Include recently failed tests
            include_recent: Include recently executed tests
        
        Returns:
            Selected test cases
        """
        print(f"🧠 Smart Test Selection - Strategy: {strategy}")
        print(f"📊 Available Tests: {len(all_test_cases)}")
        print(f"🎯 Target Selection: {max_tests}")
        
        # Initialize metadata for new tests
        self._initialize_metadata(all_test_cases)
        
        # Apply selection strategy
        if strategy == "risk_based":
            selected = self._risk_based_selection(all_test_cases, max_tests, include_failed, include_recent)
        elif strategy == "time_based":
            selected = self._time_based_selection(all_test_cases, max_tests)
        elif strategy == "coverage_based":
            selected = self._coverage_based_selection(all_test_cases, max_tests)
        elif strategy == "hybrid":
            selected = self._hybrid_selection(all_test_cases, max_tests, include_failed, include_recent)
        else:
            selected = self._risk_based_selection(all_test_cases, max_tests, include_failed, include_recent)
        
        print(f"✅ Selected {len(selected)} tests")
        return selected
    
    def _initialize_metadata(self, test_cases: List[str]):
        """Initialize metadata for tests that don't have it"""
        for tc in test_cases:
            if tc not in self.test_metadata:
                # Analyze test complexity
                complexity = self._analyze_test_complexity(tc)
                risk_score = self._calculate_risk_score(tc)
                
                self.test_metadata[tc] = TestMetadata(
                    test_case=tc,
                    last_execution=datetime.now() - timedelta(days=30),  # Assume old
                    last_status="unknown",
                    execution_count=0,
                    avg_execution_time=30.0,  # Default estimate
                    failure_rate=0.0,
                    complexity_score=complexity,
                    risk_score=risk_score,
                    dependencies=[],
                    tags=self._extract_tags(tc)
                )
    
    def _analyze_test_complexity(self, test_case: str) -> int:
        """Analyze test complexity based on steps and interactions"""
        try:
            # Load test steps
            from modules.gherkin_reader import GherkinReader
            reader = GherkinReader("test_framework/features")
            test_cases = reader.get_test_cases([test_case])
            
            if test_case in test_cases:
                steps = test_cases[test_case]
                
                # Calculate complexity based on:
                # - Number of steps
                # - Types of interactions
                # - Navigation complexity
                
                complexity = 1
                
                # Base complexity from step count
                if len(steps) <= 5:
                    complexity += 1
                elif len(steps) <= 10:
                    complexity += 2
                elif len(steps) <= 20:
                    complexity += 3
                else:
                    complexity += 4
                
                # Add complexity for different interaction types
                interaction_types = set()
                for step in steps:
                    step_lower = step.lower()
                    if "click" in step_lower:
                        interaction_types.add("click")
                    if "type" in step_lower:
                        interaction_types.add("type")
                    if "scroll" in step_lower:
                        interaction_types.add("scroll")
                    if "navigate" in step_lower or "goto" in step_lower:
                        interaction_types.add("navigation")
                    if "assert" in step_lower or "should see" in step_lower:
                        interaction_types.add("assertion")
                
                complexity += len(interaction_types)
                
                # Add complexity for navigation
                if "navigation" in interaction_types:
                    complexity += 2
                
                return min(complexity, 10)  # Cap at 10
                
        except Exception as e:
            print(f"Warning: Could not analyze complexity for {test_case}: {e}")
        
        return 5  # Default medium complexity
    
    def _calculate_risk_score(self, test_case: str) -> float:
        """Calculate business risk score for test case"""
        # High-risk areas based on business impact
        high_risk_keywords = [
            "login", "authentication", "payment", "checkout", "registration",
            "profile", "settings", "admin", "user management", "data",
            "search", "filter", "sort", "export", "import"
        ]
        
        # Medium-risk areas
        medium_risk_keywords = [
            "navigation", "menu", "header", "footer", "sidebar",
            "form", "validation", "submit", "save", "delete",
            "list", "table", "grid", "pagination"
        ]
        
        test_lower = test_case.lower()
        
        # Check for high-risk keywords
        for keyword in high_risk_keywords:
            if keyword in test_lower:
                return 0.9
        
        # Check for medium-risk keywords
        for keyword in medium_risk_keywords:
            if keyword in test_lower:
                return 0.6
        
        # Default low risk
        return 0.3
    
    def _extract_tags(self, test_case: str) -> List[str]:
        """Extract tags from test case name"""
        tags = []
        test_lower = test_case.lower()
        
        # Extract functional areas
        if "login" in test_lower or "auth" in test_lower:
            tags.append("authentication")
        if "search" in test_lower:
            tags.append("search")
        if "navigation" in test_lower or "menu" in test_lower:
            tags.append("navigation")
        if "form" in test_lower:
            tags.append("forms")
        if "assert" in test_lower or "verify" in test_lower:
            tags.append("validation")
        
        # Extract test types
        if "smoke" in test_lower:
            tags.append("smoke")
        if "regression" in test_lower:
            tags.append("regression")
        if "critical" in test_lower:
            tags.append("critical")
        
        return tags
    
    def _risk_based_selection(self, 
                            test_cases: List[str], 
                            max_tests: int,
                            include_failed: bool,
                            include_recent: bool) -> List[str]:
        """Select tests based on risk analysis"""
        scored_tests = []
        
        for tc in test_cases:
            if tc not in self.test_metadata:
                continue
            
            meta = self.test_metadata[tc]
            score = 0.0
            
            # Recent failure weight
            if include_failed and meta.last_status == "failed":
                score += self.risk_weights['recent_failure']
            
            # High complexity weight
            if meta.complexity_score >= 7:
                score += self.risk_weights['high_complexity']
            
            # High risk weight
            if meta.risk_score >= 0.7:
                score += self.risk_weights['high_risk']
            
            # Long execution time weight
            if meta.avg_execution_time > 60:
                score += self.risk_weights['long_execution_time']
            
            # Low coverage weight (tests not run recently)
            if include_recent and (datetime.now() - meta.last_execution).days > 7:
                score += self.risk_weights['low_coverage']
            
            # Failure rate weight
            if meta.failure_rate > 0.3:
                score += 0.2
            
            scored_tests.append((tc, score))
        
        # Sort by score (highest first) and select top tests
        scored_tests.sort(key=lambda x: x[1], reverse=True)
        selected = [tc for tc, score in scored_tests[:max_tests]]
        
        print(f"🎯 Risk-based selection: {len(selected)} tests selected")
        return selected
    
    def _time_based_selection(self, test_cases: List[str], max_tests: int) -> List[str]:
        """Select tests based on execution time optimization"""
        # Sort by average execution time (fastest first)
        timed_tests = []
        
        for tc in test_cases:
            if tc in self.test_metadata:
                timed_tests.append((tc, self.test_metadata[tc].avg_execution_time))
            else:
                timed_tests.append((tc, 30.0))  # Default estimate
        
        timed_tests.sort(key=lambda x: x[1])
        selected = [tc for tc, time in timed_tests[:max_tests]]
        
        print(f"⏱️ Time-based selection: {len(selected)} tests selected")
        return selected
    
    def _coverage_based_selection(self, test_cases: List[str], max_tests: int) -> List[str]:
        """Select tests based on coverage gaps"""
        # Prioritize tests that haven't been run recently
        coverage_tests = []
        
        for tc in test_cases:
            if tc in self.test_metadata:
                days_since_execution = (datetime.now() - self.test_metadata[tc].last_execution).days
                coverage_tests.append((tc, days_since_execution))
            else:
                coverage_tests.append((tc, 999))  # Never run
        
        coverage_tests.sort(key=lambda x: x[1], reverse=True)
        selected = [tc for tc, days in coverage_tests[:max_tests]]
        
        print(f"📊 Coverage-based selection: {len(selected)} tests selected")
        return selected
    
    def _hybrid_selection(self, 
                         test_cases: List[str], 
                         max_tests: int,
                         include_failed: bool,
                         include_recent: bool) -> List[str]:
        """Hybrid selection combining multiple strategies"""
        # Get selections from different strategies
        risk_selected = set(self._risk_based_selection(test_cases, max_tests // 2, include_failed, include_recent))
        time_selected = set(self._time_based_selection(test_cases, max_tests // 4))
        coverage_selected = set(self._coverage_based_selection(test_cases, max_tests // 4))
        
        # Combine selections
        combined = risk_selected | time_selected | coverage_selected
        
        # If we have more than max_tests, prioritize by risk score
        if len(combined) > max_tests:
            scored_combined = []
            for tc in combined:
                if tc in self.test_metadata:
                    score = self.test_metadata[tc].risk_score
                else:
                    score = 0.5
                scored_combined.append((tc, score))
            
            scored_combined.sort(key=lambda x: x[1], reverse=True)
            selected = [tc for tc, score in scored_combined[:max_tests]]
        else:
            selected = list(combined)
        
        print(f"🔄 Hybrid selection: {len(selected)} tests selected")
        return selected
    
    def update_metadata(self, test_case: str, execution_result: Dict[str, Any]):
        """Update metadata after test execution"""
        if test_case not in self.test_metadata:
            self._initialize_metadata([test_case])
        
        meta = self.test_metadata[test_case]
        
        # Update execution count
        meta.execution_count += 1
        
        # Update last execution time
        meta.last_execution = datetime.now()
        
        # Update last status
        if 'status' in execution_result:
            meta.last_status = execution_result['status']
        
        # Update average execution time
        if 'execution_time' in execution_result:
            current_time = execution_result['execution_time']
            # Weighted average (more weight to recent executions)
            meta.avg_execution_time = (meta.avg_execution_time * 0.7) + (current_time * 0.3)
        
        # Update failure rate
        if 'status' in execution_result:
            if execution_result['status'] == 'failed':
                # Simple moving average for failure rate
                meta.failure_rate = (meta.failure_rate * 0.8) + 0.2
            else:
                meta.failure_rate = meta.failure_rate * 0.8
        
        # Save updated metadata
        self._save_metadata()
    
    def get_test_insights(self, test_case: str) -> Dict[str, Any]:
        """Get insights about a specific test case"""
        if test_case not in self.test_metadata:
            return {"error": "Test case not found in metadata"}
        
        meta = self.test_metadata[test_case]
        
        return {
            "test_case": test_case,
            "last_execution": meta.last_execution.isoformat(),
            "last_status": meta.last_status,
            "execution_count": meta.execution_count,
            "avg_execution_time": meta.avg_execution_time,
            "failure_rate": meta.failure_rate,
            "complexity_score": meta.complexity_score,
            "risk_score": meta.risk_score,
            "tags": meta.tags,
            "recommendations": self._generate_recommendations(meta)
        }
    
    def _generate_recommendations(self, meta: TestMetadata) -> List[str]:
        """Generate recommendations for test case"""
        recommendations = []
        
        if meta.failure_rate > 0.5:
            recommendations.append("High failure rate - consider investigation")
        
        if meta.avg_execution_time > 120:
            recommendations.append("Long execution time - consider optimization")
        
        if meta.complexity_score > 8:
            recommendations.append("High complexity - consider breaking into smaller tests")
        
        if (datetime.now() - meta.last_execution).days > 30:
            recommendations.append("Not executed recently - consider running")
        
        if meta.risk_score > 0.8:
            recommendations.append("High business risk - prioritize in test runs")
        
        return recommendations


def main():
    """Main function for smart test selection"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python smart_test_selector.py <all_test_cases> [max_tests] [strategy]")
        print("Example: python smart_test_selector.py TC1,TC2,TC3 20 risk_based")
        return
    
    # Parse arguments
    all_test_cases = sys.argv[1].split(',')
    max_tests = int(sys.argv[2]) if len(sys.argv) > 2 else 20
    strategy = sys.argv[3] if len(sys.argv) > 3 else "risk_based"
    
    # Create selector
    selector = SmartTestSelector()
    
    # Select tests
    selected_tests = selector.select_tests(
        all_test_cases=all_test_cases,
        max_tests=max_tests,
        strategy=strategy
    )
    
    # Print results
    print(f"\n🎯 Selected Tests ({len(selected_tests)}):")
    for i, test in enumerate(selected_tests, 1):
        print(f"  {i}. {test}")
    
    # Show insights for first few tests
    print(f"\n📊 Test Insights:")
    for test in selected_tests[:3]:
        insights = selector.get_test_insights(test)
        print(f"\n  {test}:")
        print(f"    Risk Score: {insights['risk_score']:.2f}")
        print(f"    Complexity: {insights['complexity_score']}/10")
        print(f"    Failure Rate: {insights['failure_rate']:.2f}")
        print(f"    Avg Time: {insights['avg_execution_time']:.1f}s")


if __name__ == "__main__":
    main() 