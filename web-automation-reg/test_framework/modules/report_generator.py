import os
import json
import datetime
import platform
import getpass
from typing import Dict, List, Any, Optional
from pathlib import Path
import re

class HTMLReportGenerator:
    """Generate comprehensive HTML reports for test automation results."""
    
    def __init__(self, output_dir: str = "./test_framework/reports"):
        self.output_dir = output_dir
        self.project_name = "Rumah Pendidikan Automation Report"
        self.report_data = {
            'features': [],
            'scenarios': [],
            'start_time': None,
            'end_time': None,
            'total_duration': 0,
            'os_user': None,
            'generation_time': None
        }
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
    def start_test_run(self):
        """Record the start of a test run."""
        self.report_data['start_time'] = datetime.datetime.now()
        self.report_data['os_user'] = self._get_os_user()
        
    def end_test_run(self):
        """Record the end of a test run and calculate duration."""
        self.report_data['end_time'] = datetime.datetime.now()
        if self.report_data['start_time']:
            duration = self.report_data['end_time'] - self.report_data['start_time']
            self.report_data['total_duration'] = duration.total_seconds()
        self.report_data['generation_time'] = datetime.datetime.now()
        
    def _get_os_user(self) -> str:
        """Get the current OS user or CI runner information."""
        # Try to get CI environment variables first (comprehensive list)
        ci_user_vars = [
            'GITHUB_ACTOR',           # GitHub Actions
            'CI_USER',                # Generic CI
            'RUNNER_NAME',            # GitHub Actions runner
            'GITHUB_RUN_ID',          # GitHub Actions run ID
            'GITHUB_JOB',             # GitHub Actions job name
            'GITLAB_USER_LOGIN',      # GitLab CI
            'GITLAB_USER_NAME',       # GitLab CI
            'JENKINS_USER',           # Jenkins
            'BUILD_USER',             # Jenkins
            'TRAVIS_USER',            # Travis CI
            'CIRCLE_USERNAME',        # CircleCI
            'AZURE_PIPELINES_AGENT_NAME',  # Azure DevOps
            'AWS_EXECUTION_ENV',      # AWS CodeBuild
            'GCP_PROJECT',            # Google Cloud Build
            'USER',                   # System user
            'USERNAME',               # Windows system user
            'LOGNAME'                 # Unix system user
        ]
        
        for var in ci_user_vars:
            value = os.environ.get(var)
            if value:
                # For GitHub Actions, create a more descriptive runner name
                if var == 'GITHUB_RUN_ID' and os.environ.get('GITHUB_ACTOR'):
                    return f"runner-{os.environ.get('GITHUB_ACTOR')}-{value}"
                elif var == 'RUNNER_NAME':
                    return f"runner-{value}"
                elif var in ['GITHUB_ACTOR', 'GITLAB_USER_LOGIN', 'GITLAB_USER_NAME']:
                    return f"runner-{value}"
                elif var in ['JENKINS_USER', 'BUILD_USER']:
                    return f"jenkins-{value}"
                elif var in ['TRAVIS_USER', 'CIRCLE_USERNAME']:
                    return f"ci-{value}"
                elif var in ['AZURE_PIPELINES_AGENT_NAME']:
                    return f"azure-{value}"
                elif var in ['AWS_EXECUTION_ENV']:
                    return f"aws-{value}"
                elif var in ['GCP_PROJECT']:
                    return f"gcp-{value}"
                else:
                    return value
        
        # Try to construct a runner name from available CI environment variables
        ci_indicators = {
            'GITHUB_ACTIONS': 'github',
            'GITLAB_CI': 'gitlab', 
            'JENKINS_URL': 'jenkins',
            'TRAVIS': 'travis',
            'CIRCLECI': 'circleci',
            'AZURE_PIPELINES_AGENT_NAME': 'azure',
            'AWS_EXECUTION_ENV': 'aws',
            'GCP_PROJECT': 'gcp'
        }
        
        for env_var, ci_name in ci_indicators.items():
            if os.environ.get(env_var):
                # Try to get a unique identifier
                run_id = (os.environ.get('GITHUB_RUN_ID') or 
                         os.environ.get('GITLAB_PIPELINE_ID') or
                         os.environ.get('BUILD_NUMBER') or
                         os.environ.get('TRAVIS_BUILD_NUMBER') or
                         os.environ.get('CIRCLE_BUILD_NUM') or
                         'unknown')
                return f"runner-{ci_name}-{run_id}"
        
        # Fallback to system user
        try:
            return getpass.getuser()
        except:
            return "Unknown User"
    
    def _get_device_info(self) -> str:
        """Detect if running locally or in CI environment."""
        # Check for CI environment variables
        ci_vars = ['CI', 'GITHUB_ACTIONS', 'GITLAB_CI', 'JENKINS_URL', 'TRAVIS', 'CIRCLECI']
        for var in ci_vars:
            if os.environ.get(var):
                return "Runner Machine"
        
        # Check for cloud environment indicators
        cloud_indicators = ['AWS_EXECUTION_ENV', 'AZURE_PIPELINES_AGENT_NAME', 'GCP_PROJECT']
        for var in cloud_indicators:
            if os.environ.get(var):
                return "Runner Machine"
        
        # Default to local machine
        return "Local Machine"
    
    def _get_os_info(self) -> str:
        """Detect the operating system."""
        import platform
        
        system = platform.system().lower()
        
        if system == "darwin":
            return "macOS"
        elif system == "linux":
            return "linux"
        elif system == "windows":
            return "Windows"
        else:
            return system.capitalize()
    
    def _get_os_icon(self) -> str:
        """Get the appropriate icon for the operating system."""
        import platform
        
        system = platform.system().lower()
        
        if system == "darwin":
            return "apple"
        elif system == "linux":
            return "linux"
        elif system == "windows":
            return "windows"
        else:
            return "desktop"
            
    def add_feature_result(self, feature_name: str, feature_file: str, relative_path: str, 
                          scenarios: List[Dict], tags: List[str] = None):
        """Add a feature result to the report."""
        feature_data = {
            'name': feature_name,
            'file': feature_file,
            'relative_path': relative_path,
            'tags': tags or [],
            'scenarios': scenarios,
            'total_scenarios': len(scenarios),
            'passed_scenarios': sum(1 for s in scenarios if s.get('status') == 'passed'),
            'failed_scenarios': sum(1 for s in scenarios if s.get('status') == 'failed'),
            'undefined_scenarios': sum(1 for s in scenarios if s.get('status') == 'undefined')
        }
        
        self.report_data['features'].append(feature_data)
        self.report_data['scenarios'].extend(scenarios)
        
    def _generate_pie_chart_js(self, data: Dict, chart_id: str) -> str:
        """Generate JavaScript for pie charts."""
        colors = {
            'passed': '#28a745',
            'failed': '#dc3545', 
            'undefined': '#ffc107',
            'skipped': '#6c757d'
        }
        
        js_code = f"""
        var ctx = document.getElementById('{chart_id}').getContext('2d');
        var {chart_id}_chart = new Chart(ctx, {{
            type: 'doughnut',
            data: {{
                labels: {list(data.keys())},
                datasets: [{{
                    data: {list(data.values())},
                    backgroundColor: [{', '.join([f"'{colors.get(k, '#6c757d')}'" for k in data.keys()])}],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    legend: {{
                        position: 'bottom',
                        labels: {{
                            padding: 20,
                            usePointStyle: true
                        }}
                    }}
                }},
                cutout: '60%'
            }}
        }});
        """
        return js_code
        
    def _format_duration(self, seconds: float) -> str:
        """Format duration in human readable format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d} Hours {minutes:02d} Minutes {secs:02d} Seconds"
        elif minutes > 0:
            return f"{minutes:02d} Minutes {secs:02d} Seconds"
        else:
            return f"{secs:02d} Seconds"
            
    def _generate_smart_solution(self, error_message: str, step_type: str) -> str:
        """Generate smart solutions for common test failures."""
        error_lower = error_message.lower()
        step_lower = step_type.lower() if step_type else ""
        
        # 🔍 Check for assertion errors with actual text information
        if ("expected:" in error_lower or "Expected:" in error_message) and ("actual:" in error_lower or "Actual:" in error_message):
            # Extract expected and actual text from error message
            expected_match = re.search(r"[Ee]xpected:\s*['\"]([^'\"]+)['\"]", error_message, re.IGNORECASE)
            actual_match = re.search(r"[Aa]ctual:\s*['\"]([^'\"]+)['\"]", error_message, re.IGNORECASE)
            
            if expected_match and actual_match:
                expected_text = expected_match.group(1)
                actual_text = actual_match.group(1)
                
                return f"""
                🔍 <strong>Text Assertion Mismatch:</strong>
                <br><br>
                <strong>Expected:</strong> <code>"{expected_text}"</code>
                <br><strong>Actual:</strong> <code>"{actual_text}"</code>
                <br><br>
                <strong>Possible Solutions:</strong>
                <ul>
                    <li>📝 <strong>Update Test Case:</strong> Change the expected text to match the actual text on the website</li>
                    <li>🔍 <strong>Check for Typos:</strong> Verify if there's a typo in the expected text (e.g., "Berjas" vs "Barjas")</li>
                    <li>🔄 <strong>Content Changed:</strong> The website content may have been updated - verify the current text</li>
                    <li>⏰ <strong>Dynamic Content:</strong> The text might be loading dynamically - add wait conditions</li>
                    <li>🌐 <strong>Language/Locale:</strong> Check if the text is in a different language or format</li>
                    <li>📱 <strong>Responsive Design:</strong> The text might be different on different screen sizes</li>
                </ul>
                <br>
                <strong>Quick Fix:</strong> Update the test case to use: <code>I should see "{actual_text}"</code>
                """
        
        # Comprehensive solution mapping
        solutions = {
            'timeout': [
                "⏱️ <strong>Timeout Issue:</strong>",
                "• Increase the timeout value in the test configuration",
                "• Check if the element is actually present on the page",
                "• Verify the page has fully loaded before interacting",
                "• Add explicit wait for element to be visible",
                "• Check network connectivity and page load speed"
            ],
            'element not found': [
                "🔍 <strong>Element Not Found:</strong>",
                "• Verify the element selector is correct and unique",
                "• Check if the element is visible and not hidden by CSS",
                "• Ensure the page has loaded completely before searching",
                "• Try using a different selector strategy (ID, class, XPath)",
                "• Check if the element is in an iframe or shadow DOM",
                "• Verify the element hasn't been removed from the DOM"
            ],
            'click': [
                "🖱️ <strong>Click Issue:</strong>",
                "• Ensure the element is clickable and not disabled",
                "• Try scrolling to the element before clicking",
                "• Check if there are any overlays, modals, or popups blocking the element",
                "• Verify the element is in the viewport and not off-screen",
                "• Try using JavaScript click as fallback",
                "• Check if element has proper z-index and is not covered"
            ],
            'type': [
                "⌨️ <strong>Input Issue:</strong>",
                "• Clear the field before typing using .clear() method",
                "• Ensure the field is not read-only or disabled",
                "• Check if the field is focused before typing",
                "• Verify the field accepts the input type you're providing",
                "• Try clicking on the field first to ensure focus",
                "• Check for any input validation that might block typing"
            ],
            'assert': [
                "✅ <strong>Assertion Issue:</strong>",
                "• Verify the expected text is actually present on the page",
                "• Check for case sensitivity in text matching",
                "• Ensure the page has loaded the expected content completely",
                "• Try waiting for the element to be visible before assertion",
                "• Check if text is in a different element or container",
                "• Verify the text hasn't changed due to dynamic content"
            ],
            'navigation': [
                "🧭 <strong>Navigation Issue:</strong>",
                "• Verify the URL is correct and accessible",
                "• Check if the page requires authentication",
                "• Ensure the base URL is properly configured",
                "• Try adding a wait for page load completion",
                "• Check for any redirects that might affect navigation",
                "• Verify the page doesn't have any blocking overlays"
            ],
            'scroll': [
                "📜 <strong>Scroll Issue:</strong>",
                "• Ensure the element exists before scrolling to it",
                "• Try scrolling to a parent container first",
                "• Check if the page has scrollable content",
                "• Verify the element is not in a fixed position container",
                "• Try using JavaScript scroll as alternative"
            ],
            'should see': [
                "👁️ <strong>Visibility Issue:</strong>",
                "• Check if the text/element is actually present on the page",
                "• Verify the element is not hidden by CSS (display: none, visibility: hidden)",
                "• Ensure the element is in the current viewport",
                "• Check if the text is in a different language or format",
                "• Try waiting for the element to be visible",
                "• Verify the element hasn't been removed or replaced"
            ],
            'should not see': [
                "🚫 <strong>Negative Assertion Issue:</strong>",
                "• Verify the element/text is actually not present",
                "• Check if the element is hidden but still in DOM",
                "• Ensure the page has loaded completely",
                "• Try using a more specific selector",
                "• Check if the element appears after a delay"
            ],
            'locator': [
                "🎯 <strong>Automated Locator Issue:</strong>",
                "• The element may have changed on the website since the test was written",
                "• Check if the website structure or content has been updated",
                "• Verify the element is still present on the current page",
                "• The element might be in a different section or have different text",
                "• Consider if the page has loaded completely or has dynamic content",
                "• Check if the element is visible or hidden by CSS/styling"
            ],
            'skipped': [
                "⏭️ <strong>Step Skipped Issue:</strong>",
                "• The automated locator finder could not locate the element",
                "• The website content or structure may have changed",
                "• The element might be in a different location or have different text",
                "• Check if the page has loaded completely",
                "• Verify the element is visible and not hidden",
                "• The element might require authentication or special access"
            ]
        }
        
        # Step-specific solutions based on step content
        step_specific_solutions = {
            'i should see': [
                "👁️ <strong>Text Visibility Issue:</strong>",
                "• The automated text finder could not locate the specified text",
                "• Check if the text has changed on the website",
                "• Verify the exact text matches what's displayed on the page",
                "• The text might be in a different section or page",
                "• Check if the page has loaded completely",
                "• The text might be hidden or require scrolling to view",
                "• Verify there are no typos in the test step text",
                "• The website content may have been updated"
            ],
            'i click': [
                "🖱️ <strong>Click Action Issue:</strong>",
                "• Verify the element is clickable and enabled",
                "• Check if element is covered by other elements",
                "• Try scrolling to the element first",
                "• Ensure the element is in the current viewport",
                "• Check if there are any JavaScript errors preventing clicks"
            ],
            'i type': [
                "⌨️ <strong>Input Action Issue:</strong>",
                "• Clear the input field before typing",
                "• Ensure the field is not read-only",
                "• Check if the field accepts the input type",
                "• Try clicking on the field first to focus it",
                "• Verify there are no input restrictions or validations"
            ],
            'i am on': [
                "🏠 <strong>Page Navigation Issue:</strong>",
                "• Verify the page URL is correct",
                "• Check if the page requires authentication",
                "• Ensure the page has loaded completely",
                "• Check for any redirects or error pages",
                "• Verify the page title matches expectations"
            ]
        }
        
        # Check if this is a skipped step
        if 'skipped' in error_lower or 'element not found' in error_lower or 'could not find' in error_lower:
            return "<br>".join(solutions.get('skipped', [
                "⏭️ <strong>Step Skipped Issue:</strong>",
                "• The automated locator finder could not locate the element",
                "• The website content or structure may have changed",
                "• The element might be in a different location or have different text",
                "• Check if the page has loaded completely",
                "• Verify the test step text matches what's actually on the website",
                "• Consider if the element is hidden or requires scrolling to view",
                "• The website may have been updated since the test was created"
            ]))
        
        # Find matching solution category
        for category, solution_list in solutions.items():
            if category in error_lower:
                return "<br>".join(solution_list)
        
        # Check for step-specific solutions
        for step_pattern, solution_list in step_specific_solutions.items():
            if step_pattern in step_lower:
                return "<br>".join(solution_list)
                
        # Default comprehensive solution
        return """🔧 <strong>General Troubleshooting:</strong>
• The automated locator finder could not locate the element
• Check if the website content or structure has changed
• Verify the test step text matches what's actually on the website
• Ensure the page has loaded completely before the test step
• Check if the element is visible or requires scrolling
• Verify there are no typos in the test step description
• The website may have been updated since the test was created
• Consider if the element is in a different section or page"""
        
    def generate_html_report(self) -> str:
        """Generate the complete HTML report."""
        
        # Calculate statistics
        total_features = len(self.report_data['features'])
        total_scenarios = len(self.report_data['scenarios'])
        passed_scenarios = sum(1 for s in self.report_data['scenarios'] if s.get('status') == 'passed')
        failed_scenarios = sum(1 for s in self.report_data['scenarios'] if s.get('status') == 'failed')
        undefined_scenarios = sum(1 for s in self.report_data['scenarios'] if s.get('status') == 'undefined')
        
        # Feature statistics
        feature_stats = {
            'passed': sum(1 for f in self.report_data['features'] if f['failed_scenarios'] == 0),
            'failed': sum(1 for f in self.report_data['features'] if f['failed_scenarios'] > 0),
            'undefined': sum(1 for f in self.report_data['features'] if f['undefined_scenarios'] > 0)
        }
        
        # Scenario statistics
        scenario_stats = {
            'passed': passed_scenarios,
            'failed': failed_scenarios,
            'undefined': undefined_scenarios
        }
        
        # Generate HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.project_name}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        
        .header .subtitle {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .main-container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .dashboard {{
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        
        .chart-container h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .chart-wrapper {{
            position: relative;
            height: 200px;
            margin: 20px 0;
        }}
        
        .chart-center-text {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2em;
            font-weight: bold;
            color: #495057;
        }}
        
        .chart-details {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }}
        
        .chart-detail-section {{
            text-align: left;
        }}
        
        .chart-detail-section h4 {{
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        
        .status-list {{
            list-style: none;
            padding: 0;
        }}
        
        .status-list li {{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 14px;
        }}
        
        .status-list li i {{
            margin-right: 8px;
            font-size: 12px;
        }}
        
        .status-list li.passed i {{ color: #28a745; }}
        .status-list li.failed i {{ color: #dc3545; }}
        .status-list li.undefined i {{ color: #ffc107; }}
        
        .progress-list {{
            list-style: none;
            padding: 0;
        }}
        
        .progress-list li {{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 14px;
        }}
        
        .progress-list li i {{
            margin-right: 8px;
            font-size: 12px;
        }}
        
        .progress-list li.passed i {{ color: #28a745; }}
        .progress-list li.failed i {{ color: #dc3545; }}
        .progress-list li.undefined i {{ color: #ffc107; }}
        
        .run-info {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .run-info h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .info-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }}
        
        .info-item {{
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }}
        
        .info-item i {{
            margin-right: 10px;
            color: #6c757d;
            width: 20px;
        }}
        
        .features-table {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .table-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }}
        
        .table-header h3 {{
            color: #495057;
        }}
        
        .table-controls {{
            display: flex;
            gap: 10px;
            align-items: center;
        }}
        
        .search-box {{
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }}
        
        .entries-select {{
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}
        
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        
        th {{
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }}
        
        .feature-link {{
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }}
        
        .feature-link:hover {{
            text-decoration: underline;
        }}
        
        .status-icon {{
            font-size: 1.2em;
        }}
        
        .status-passed {{ color: #28a745; }}
        .status-failed {{ color: #dc3545; }}
        .status-undefined {{ color: #ffc107; }}
        
        .pagination {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
        }}
        
        .pagination-info {{
            color: #6c757d;
        }}
        
        .pagination-controls {{
            display: flex;
            gap: 5px;
        }}
        
        .pagination-controls button {{
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }}
        
        .pagination-controls button:hover {{
            background: #f8f9fa;
        }}
        
        .pagination-controls button.active {{
            background: #007bff;
            color: white;
            border-color: #007bff;
        }}
        
        .footer {{
            text-align: center;
            padding: 20px;
            color: #6c757d;
            margin-top: 40px;
        }}
        
        /* Feature Detail Page Styles */
        .feature-detail {{
            display: none;
        }}
        
        .back-button {{
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }}
        
        .feature-header {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .tags {{
            margin-bottom: 15px;
        }}
        
        .tag {{
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 3px;
            margin-right: 5px;
            font-size: 12px;
        }}
        
        .feature-info {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }}
        
        .feature-description {{
            margin: 15px 0;
            color: #6c757d;
        }}
        
        .scenarios-overview {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }}
        
        .scenarios-chart {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        
        .scenarios-chart h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .scenarios-chart-wrapper {{
            position: relative;
            height: 150px;
            margin: 20px 0;
        }}
        
        .scenarios-chart-center-text {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em;
            font-weight: bold;
            color: #495057;
        }}
        
        .scenarios-chart-details {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }}
        
        .metadata-panel {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .metadata-panel h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .metadata-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        
        .metadata-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }}
        
        .metadata-label {{
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }}
        
        .metadata-value {{
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 14px;
        }}
        
        .metadata-value i {{
            margin-right: 8px;
            color: #6c757d;
            width: 16px;
        }}
        
        .filter-section {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .filter-section h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .filter-buttons {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }}
        
        .filter-button {{
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }}
        
        .filter-button:hover {{
            background: #f8f9fa;
        }}
        
        .filter-button.active {{
            background: #007bff;
            color: white;
            border-color: #007bff;
        }}
        
        .scenarios-section {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .scenarios-section h3 {{
            margin-bottom: 15px;
            color: #495057;
        }}
        
        .scenario-item {{
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            overflow: hidden;
        }}
        
        .scenario-header {{
            background: #f8f9fa;
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .scenario-header:hover {{
            background: #e9ecef;
        }}
        
        .scenario-title {{
            font-weight: 500;
            color: #495057;
        }}
        
        .scenario-status {{
            font-size: 1.2em;
        }}
        
        .scenario-content {{
            padding: 15px;
            border-top: 1px solid #ddd;
            display: none;
        }}
        
        .step-item {{
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }}
        
        .step-item.passed {{
            background: #d4edda;
            border-left: 4px solid #28a745;
        }}
        
        .step-item.failed {{
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }}
        
        .step-item.undefined {{
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }}
        
        .step-icon {{
            margin-right: 10px;
            font-size: 1.2em;
            margin-top: 2px;
        }}
        
        .step-content {{
            flex: 1;
        }}
        
        .step-text {{
            font-weight: 500;
            margin-bottom: 5px;
        }}
        
        .step-details {{
            font-size: 14px;
            color: #6c757d;
        }}
        
        .error-section {{
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}
        
        .error-section.failed {{
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border-left: 5px solid #dc3545;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
        }}
        
        .error-section.skipped {{
            background: linear-gradient(135deg, #fffbf0 0%, #fef3c7 100%);
            border-left: 5px solid #f59e0b;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
        }}
        
        .error-toggle {{
            color: #dc3545;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 5px;
            transition: all 0.3s ease;
        }}
        
        .error-toggle:hover {{
            background: rgba(220, 53, 69, 0.2);
        }}
        
        .error-toggle i {{
            margin-right: 8px;
            font-size: 16px;
        }}
        
        .error-content {{
            display: none;
            margin-top: 15px;
            animation: fadeIn 0.3s ease;
        }}
        
        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(-10px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        .error-message {{
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            font-size: 13px;
            line-height: 1.5;
            text-align: left !important;
        }}
        
        .error-message * {{
            text-align: left !important;
        }}
        
        .error-message strong {{
            text-align: left !important;
        }}
        
        .error-message p {{
            text-align: left !important;
        }}
        
        .error-message div {{
            text-align: left !important;
        }}
        
        .error-message span {{
            text-align: left !important;
        }}
        
        /* Override any parent container alignment */
        .scenarios-chart .error-message,
        .chart-container .error-message,
        .dashboard .error-message,
        .main-container .error-message,
        .feature-detail .error-message {{
            text-align: left !important;
        }}
        
        .scenarios-chart .error-message *,
        .chart-container .error-message *,
        .dashboard .error-message *,
        .main-container .error-message *,
        .feature-detail .error-message * {{
            text-align: left !important;
        }}
        
        .solution-section {{
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #0ea5e9;
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
            margin-top: 15px;
        }}
        
        .solution-title {{
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }}
        
        .solution-title::before {{
            content: "💡";
            margin-right: 8px;
            font-size: 18px;
        }}
        
        .solution-content {{
            color: #0c4a6e;
            line-height: 1.6;
            font-size: 14px;
        }}
        
        .solution-content strong {{
            color: #0369a1;
        }}
        
        .solution-content ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        
        .solution-content li {{
            margin-bottom: 8px;
            padding-left: 5px;
        }}
        
        .quick-fix-badge {{
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{self.project_name}</h1>
        <div class="subtitle">Comprehensive Test Automation Report</div>
    </div>
    
    <div class="main-container">
        <!-- Main Dashboard -->
        <div id="main-dashboard">
            <div class="dashboard">
                <div class="chart-container">
                    <h3>Features</h3>
                    <div class="chart-wrapper">
                        <canvas id="featuresChart"></canvas>
                        <div class="chart-center-text">{total_features}</div>
                    </div>
                    <div class="chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> Not Defined</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> {round((feature_stats.get('passed', 0) / total_features * 100) if total_features > 0 else 0, 2)}%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> {round((feature_stats.get('failed', 0) / total_features * 100) if total_features > 0 else 0, 2)}%</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> {round((feature_stats.get('undefined', 0) / total_features * 100) if total_features > 0 else 0, 2)}%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>Scenarios</h3>
                    <div class="chart-wrapper">
                        <canvas id="scenariosChart"></canvas>
                        <div class="chart-center-text">{total_scenarios}</div>
                    </div>
                    <div class="chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> Not Defined</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> {round((passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0, 2)}%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> {round((failed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0, 2)}%</li>
                                <li class="undefined"><i class="fas fa-question-circle"></i> {round((undefined_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0, 2)}%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="run-info">
                    <h3>Run Info</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-project-diagram"></i>
                            <span><strong>Project:</strong> {self.project_name}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span><strong>Generation Time:</strong> {self.report_data['generation_time'].strftime('%Y-%m-%dT%H:%M:%S.%fZ') if self.report_data['generation_time'] else 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span><strong>OS User:</strong> {self.report_data['os_user']}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-stopwatch"></i>
                            <span><strong>Duration:</strong> {self._format_duration(self.report_data['total_duration'])}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="features-table">
                <div class="table-header">
                    <h3>Features Overview</h3>
                    <div class="table-controls">
                        <select class="entries-select" id="entriesPerPage">
                            <option value="10">10 entries per page</option>
                            <option value="25">25 entries per page</option>
                            <option value="50" selected>50 entries per page</option>
                            <option value="100">100 entries per page</option>
                        </select>
                        <input type="text" class="search-box" id="searchBox" placeholder="Search features...">
                    </div>
                </div>
                
                <table id="featuresTable">
                    <thead>
                        <tr>
                            <th>Feature Name</th>
                            <th>Status</th>
                            <th>Device</th>
                            <th>OS</th>
                            <th>Browser</th>
                            <th>Total</th>
                            <th>Passed</th>
                            <th>Failed</th>
                            <th>Undefined</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add feature rows
        for i, feature in enumerate(self.report_data['features']):
            status_class = 'passed' if feature['failed_scenarios'] == 0 else 'failed'
            status_icon = 'fas fa-check-circle' if status_class == 'passed' else 'fas fa-times-circle'
            
            # Defensive: ensure feature name is present
            feature_name = feature.get('name', f'Feature_{i}')
            html_content += f"""
                        <tr>
                            <td><a href="#" class="feature-link" onclick="showFeatureDetail({i})">{feature_name}</a></td>
                            <td><i class="fas {status_icon} status-icon status-{status_class}"></i></td>
                            <td><i class="fas fa-desktop"></i> Runner Machine</td>
                            <td><i class="fab fa-linux"></i> linux</td>
                            <td><i class="fab fa-chrome"></i> 103</td>
                            <td>{feature['total_scenarios']}</td>
                            <td>{feature['passed_scenarios']}</td>
                            <td>{feature['failed_scenarios']}</td>
                            <td>{feature['undefined_scenarios']}</td>
                        </tr>
            """
        
        html_content += """
                    </tbody>
                </table>
                
                <div class="pagination">
                    <div class="pagination-info">
                        Showing 1 to """ + str(len(self.report_data['features'])) + """ of """ + str(len(self.report_data['features'])) + """ entries
                    </div>
                    <div class="pagination-controls">
                        <button disabled>&lt;&lt;</button>
                        <button disabled>&lt;</button>
                        <button class="active">1</button>
                        <button disabled>&gt;</button>
                        <button disabled>&gt;&gt;</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Feature Detail Pages -->
        """
        
        # Generate feature detail pages
        for i, feature in enumerate(self.report_data['features']):
            # Defensive: ensure feature name is present
            feature_name = feature.get('name', f'Feature_{i}')
            html_content += f"""
        <div id="feature-detail-{i}" class="feature-detail">
            <button class="back-button" onclick="showMainDashboard()">
                <i class="fas fa-arrow-left"></i> Features Overview
            </button>
            
                                    <div class="feature-header">
                            <div class="tags">
                                {''.join([f'<span class="tag">{tag}</span>' for tag in feature.get('tags', [])])}
                            </div>
                            <h2>Feature: {feature_name}</h2>
                            <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                            <div class="feature-info">
                                <div><strong>File name:</strong> {feature.get('file', 'N/A')}</div>
                                <div><strong>Relative path:</strong> {feature.get('relative_path', 'N/A')}</div>
                            </div>
                        </div>
            
            <div class="scenarios-overview">
                <div class="scenarios-chart">
                    <h3>Scenarios</h3>
                    <div class="scenarios-chart-wrapper">
                        <canvas id="scenarioChart-{i}"></canvas>
                        <div class="scenarios-chart-center-text">{feature['total_scenarios']}</div>
                    </div>
                    <div class="scenarios-chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <ul class="status-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> Passed</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> Failed</li>
                            </ul>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <ul class="progress-list">
                                <li class="passed"><i class="fas fa-check-circle"></i> {round((feature['passed_scenarios'] / feature['total_scenarios'] * 100) if feature['total_scenarios'] > 0 else 0, 2)}%</li>
                                <li class="failed"><i class="fas fa-times-circle"></i> {round((feature['failed_scenarios'] / feature['total_scenarios'] * 100) if feature['total_scenarios'] > 0 else 0, 2)}%</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="metadata-panel">
                    <h3>Metadata</h3>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <div class="metadata-label">Device</div>
                            <div class="metadata-value">{self._get_device_info()}</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">OS</div>
                            <div class="metadata-value">
                                <i class="fab fa-{self._get_os_icon()}"></i>
                                {self._get_os_info()}
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Browser</div>
                            <div class="metadata-value">
                                <i class="fab fa-chrome"></i>
                                Chrome 103
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="filter-section">
                <h3>Filter Scenarios</h3>
                <div class="filter-buttons">
                    <button class="filter-button active" onclick="filterScenarios('all', {i})">All ({feature['total_scenarios']})</button>
                    <button class="filter-button" onclick="filterScenarios('passed', {i})">Passed ({feature['passed_scenarios']})</button>
                    <button class="filter-button" onclick="filterScenarios('failed', {i})">Failed ({feature['failed_scenarios']})</button>
                </div>
            </div>
            
            <div class="scenarios-section">
                <h3>Scenarios</h3>
            """
            
            # Add scenarios
            for j, scenario in enumerate(feature['scenarios']):
                status_class = scenario.get('status', 'undefined')
                status_icon = {
                    'passed': 'fas fa-check-circle',
                    'failed': 'fas fa-times-circle',
                    'undefined': 'fas fa-question-circle'
                }.get(status_class, 'fas fa-question-circle')
                ticket_number = scenario.get('ticket_number', '')
                ticket_html = f'<span class="tag">{ticket_number}</span> ' if ticket_number else ''
                html_content += f"""
                <div class="scenario-item" data-status="{status_class}" data-feature="{i}">
                    <div class="scenario-header" onclick="toggleScenario({i}, {j})">
                        <div class="scenario-title">{ticket_html}{scenario.get('name', f'Scenario {j+1}')}</div>
                        <div class="scenario-status">
                            <i class="{status_icon} status-{status_class}"></i>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="scenario-content" id="scenario-content-{i}-{j}">
                """
                
                # Add steps
                for step in scenario.get('steps', []):
                    step_status = step.get('status', 'undefined')
                    step_icon = {
                        'passed': 'fas fa-check',
                        'failed': 'fas fa-times',
                        'undefined': 'fas fa-question'
                    }.get(step_status, 'fas fa-question')
                    
                    html_content += f"""
                        <div class="step-item {step_status}">
                            <div class="step-icon">
                                <i class="{step_icon}"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-text">{step.get('text', 'Step')}</div>
                                <div class="step-details">{step.get('details', '')}</div>
                    """
                    
                    # Add error section if step failed or skipped
                    if step_status in ['failed', 'skipped']:
                        if step_status == 'failed':
                            error_message = step.get('error', 'Step execution failed')
                            status_text = 'Error'
                        else:  # skipped
                            error_message = step.get('error', 'Step was skipped - element not found or action could not be completed')
                            status_text = 'Skipped'
                        
                        step_text = step.get('text', 'Unknown step')
                        solution = self._generate_smart_solution(error_message, step_text)
                        
                        html_content += f"""
                                <div class="error-section {step_status}">
                                    <div class="error-toggle" onclick="toggleError(this)">
                                        <i class="fas fa-exclamation-triangle"></i> 
                                        <span>View {status_text} Details & Solutions</span>
                                        <span class="quick-fix-badge">Quick Show</span>
                                    </div>
                                    <div class="error-content">
                                        <div class="error-message" style="text-align: left !important;">
                                            <strong>{status_text}:</strong> {error_message}
                                        </div>
                                        <div class="solution-section">
                                            <div class="solution-title">Recommended Solutions</div>
                                            <div class="solution-content">{solution}</div>
                                        </div>
                                    </div>
                                </div>
                        """
                    
                    html_content += """
                            </div>
                        </div>
                    """
                
                html_content += """
                    </div>
                </div>
                """
            
            html_content += """
                                    </div>
                    </div>
                """
        
        html_content += """
        <div class="footer">
            <p>Maintained by Test Automation Team. Find us on:</p>
            <div style="margin-top: 10px;">
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-github fa-lg"></i></a>
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-twitter fa-lg"></i></a>
                <a href="#" style="margin: 0 10px; color: #6c757d;"><i class="fab fa-linkedin fa-lg"></i></a>
            </div>
        </div>
    </div>
    
    <script>
        // Chart.js configuration
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        
        // Features Chart
        var featuresCtx = document.getElementById('featuresChart').getContext('2d');
        var featuresChart = new Chart(featuresCtx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Undefined'],
                datasets: [{
                    data: [""" + str(feature_stats.get('passed', 0)) + """, """ + str(feature_stats.get('failed', 0)) + """, """ + str(feature_stats.get('undefined', 0)) + """],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '60%'
            }
        });
        
        // Scenarios Chart
        var scenariosCtx = document.getElementById('scenariosChart').getContext('2d');
        var scenariosChart = new Chart(scenariosCtx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Undefined'],
                datasets: [{
                    data: [""" + str(passed_scenarios) + """, """ + str(failed_scenarios) + """, """ + str(undefined_scenarios) + """],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '60%'
            }
        });
        
        // Feature detail charts
        """
        
        # Add JavaScript for feature detail charts
        for i, feature in enumerate(self.report_data['features']):
            passed = feature['passed_scenarios']
            failed = feature['failed_scenarios']
            total = feature['total_scenarios']
            
            html_content += f"""
        var scenarioCtx{i} = document.getElementById('scenarioChart-{i}').getContext('2d');
        var scenarioChart{i} = new Chart(scenarioCtx{i}, {{
            type: 'doughnut',
            data: {{
                labels: ['Passed', 'Failed'],
                datasets: [{{
                    data: [{passed}, {failed}],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    legend: {{
                        display: false
                    }}
                }},
                cutout: '60%'
            }}
        }});
            """
        
        html_content += """
        
        // Navigation functions
        function showFeatureDetail(featureIndex) {
            document.getElementById('main-dashboard').style.display = 'none';
            document.getElementById('feature-detail-' + featureIndex).style.display = 'block';
        }
        
        function showMainDashboard() {
            document.getElementById('main-dashboard').style.display = 'block';
            document.querySelectorAll('.feature-detail').forEach(function(el) {
                el.style.display = 'none';
            });
        }
        
        function toggleScenario(featureIndex, scenarioIndex) {
            var content = document.getElementById('scenario-content-' + featureIndex + '-' + scenarioIndex);
            var header = content.previousElementSibling;
            var icon = header.querySelector('.fa-chevron-down');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        
        function toggleError(element) {
            var content = element.nextElementSibling;
            var span = element.querySelector('span');
            var badge = element.querySelector('.quick-fix-badge');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                span.textContent = 'Hide Error Details & Solutions';
                if (badge) {
                    badge.textContent = 'Hide';
                    badge.style.background = '#dc3545';
                }
                element.style.background = 'rgba(220, 53, 69, 0.2)';
            } else {
                content.style.display = 'none';
                span.textContent = 'View Error Details & Solutions';
                if (badge) {
                    badge.textContent = 'Quick Show';
                    badge.style.background = '#10b981';
                }
                element.style.background = 'rgba(220, 53, 69, 0.1)';
            }
        }
        
        function filterScenarios(status, featureIndex) {
            // Update filter buttons
            var filterButtons = document.querySelectorAll('#feature-detail-' + featureIndex + ' .filter-button');
            filterButtons.forEach(function(btn) {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter scenarios
            var scenarios = document.querySelectorAll('#feature-detail-' + featureIndex + ' .scenario-item');
            scenarios.forEach(function(scenario) {
                var scenarioStatus = scenario.getAttribute('data-status');
                if (status === 'all' || scenarioStatus === status) {
                    scenario.style.display = '';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
        

        
        // Search functionality
        document.getElementById('searchBox').addEventListener('input', function() {
            var searchTerm = this.value.toLowerCase();
            var rows = document.querySelectorAll('#featuresTable tbody tr');
            
            rows.forEach(function(row) {
                var featureName = row.cells[0].textContent.toLowerCase();
                if (featureName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Entries per page functionality
        document.getElementById('entriesPerPage').addEventListener('change', function() {
            // Implementation for pagination
            console.log('Entries per page changed to: ' + this.value);
        });
    </script>
</body>
</html>
        """
        
        return html_content
        
    def save_report(self, filename: str = None) -> str:
        """Save the HTML report to a file."""
        if filename is None:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"test_report_{timestamp}.html"
            
        filepath = os.path.join(self.output_dir, filename)
        
        html_content = self.generate_html_report()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return filepath 