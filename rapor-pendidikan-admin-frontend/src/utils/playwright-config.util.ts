import { STORAGE_STATE_IAP } from '@/auth/google-login.helper'
import { User, Users } from '@/constants/users.constant'
import {
  AUTH_DEBUG,
  AUTH_SETUP_ENABLED,
  CI,
  ENVIRONMENT,
  IAP_ENABLED,
  PRODUCTION_ONLY,
  STAGING_ONLY,
  USE_GREP_FILTER,
} from '@/utils/env.util'
import { Project, devices } from '@playwright/test'
import yargs from 'yargs'

const { DEFAULT_USER }: { USERS: Users; DEFAULT_USER: User } = require('src/constants/users.constant')

const defaultAuthFile = DEFAULT_USER.pathFile
const devAuthFile = DEFAULT_USER.pathFile.replace('.auth', '.auth-dev')

export const authFile = CI ? defaultAuthFile : devAuthFile

export const log = (message: string, setupEnv: typeof ENVIRONMENT) => {
  AUTH_DEBUG && console.log(`     [${setupEnv}.setup] ${message}`)
}
/**
 * Note: Please consult to Core Webex at #webex-core-support, if you want to change this `generateGrep` function
 * because it will affect xray integrations and test filtering in CI.
 */
export const generateGrep = (env: typeof ENVIRONMENT, filter?: { AND: string[] }) => {
  const CI_ENVIRONMENT = CI ? ENVIRONMENT : null

  const filterStringAnd = filter?.AND?.map((grep) => `(?=.*${grep})`).join('')

  const argv = yargs.option('grep', {
    alias: 'g',
    describe: 'Specify the value for --grep or -g',
  }).argv

  // @ts-expect-error no need await Promise
  const argvGrepValue: string[] = (argv.grep || argv.g)?.split('|') || []

  let grepAndValue: string[] = []

  function noRunTest() {
    // to make sure that we run tests only when the CI environment is matched with the project environment
    return new RegExp(Math.random().toString(), 'i')
  }

  const usingGrepCli = argvGrepValue.length

  function runTestWithFilter() {
    if (usingGrepCli) {
      const isFilteringStaging = argvGrepValue.some((grep) => grep.includes('staging'))
      const isFilteringProduction = argvGrepValue.some((grep) => grep.includes('production'))

      const filterENV = CI ? ENVIRONMENT : env

      if ((isFilteringStaging && env !== 'staging') || (isFilteringProduction && env !== 'production')) {
        grepAndValue = []
        return
      }

      grepAndValue = argvGrepValue
        .map((grep) => `(?=.*${grep})(?=.*@${filterENV})`)
        .map((grep) => (filterStringAnd ? `${grep}${filterStringAnd}` : grep))
    } else {
      grepAndValue = [`(?=.*@${env})`].map((grep) => (filterStringAnd ? `${grep}${filterStringAnd}` : grep))
    }
  }

  /** This can be useful when we need to run all tests in CI, such as when running the e2e update Jira labels script. */
  if (!USE_GREP_FILTER) {
    const mergedGrepArgvWithEnv = grepAndValue.join('|')
    const grep = new RegExp(mergedGrepArgvWithEnv)

    return grep
  }

  if ((CI && env === CI_ENVIRONMENT) || !CI) {
    runTestWithFilter()
  } else if (CI) {
    return noRunTest()
  }

  if (!grepAndValue.length) return noRunTest()

  /**
   * Grep result will be like this:
   * example result: (?=.*@SQA-123)(?=.*@staging)|(?=.*@SQA-124)(?=.*@production)
   * example result: (?=.*@SQA-123)(?=.*@staging)(?=.*@anonym)|(?=.*@SQA-124)(?=.*@production)(?=.*@anonym)
   * playwright grep docs: https://playwright.dev/docs/test-annotations#tag-tests
   */
  const mergedGrepArgvWithEnv = grepAndValue.join('|')
  const grep = new RegExp(mergedGrepArgvWithEnv)

  return grep
}

const defaultProjectUse = {
  video: { mode: 'retain-on-failure' },
} as const

export const defaultMobileProjectUse = {
  ...devices['Pixel 3'],
  ...defaultProjectUse,
} as (typeof devices)[string]

export const defaultDesktopProjectUse = {
  ...devices['Desktop Chrome'],
  ...defaultProjectUse,
} as (typeof devices)[string]

const IAPSetup = {
  name: 'iap.setup',
  testMatch: /iap.setup\.ts/,
  use: defaultDesktopProjectUse,
} as Project

const productionSetup = {
  name: 'production.setup',
  testMatch: /production.setup\.ts/,
  use: defaultDesktopProjectUse,
} as Project

const stagingSetup = {
  name: 'staging.setup',
  testMatch: /staging.setup\.ts/,
  use: defaultDesktopProjectUse,
  dependencies: IAP_ENABLED ? ['iap.setup'] : [],
  storageState: IAP_ENABLED ? STORAGE_STATE_IAP : undefined,
} as Project

const SHOULD_RUN_STAGING_SETUP = !PRODUCTION_ONLY || (CI && ENVIRONMENT === 'staging')
const SHOULD_RUN_PRODUCTION_SETUP = !STAGING_ONLY || (CI && ENVIRONMENT === 'production')
const SHOULD_RUN_IAP_SETUP = IAP_ENABLED && SHOULD_RUN_STAGING_SETUP

export const environmentSetup = (
  AUTH_SETUP_ENABLED
    ? [
        SHOULD_RUN_IAP_SETUP && IAPSetup,
        SHOULD_RUN_STAGING_SETUP && stagingSetup,
        SHOULD_RUN_PRODUCTION_SETUP && productionSetup,
      ]
    : [SHOULD_RUN_IAP_SETUP && IAPSetup]
).filter((v) => v)
