import { STORAGE_STATE_IAP, googleLogin } from '@/auth/google-login.helper'
import { USERS, USERS_SHOULD_LOGIN_APP, UserKey } from '@/constants/users.constant'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'
import {
  AUTH_LOGIN_SEQUENTIAL,
  AUTH_SETUP_ENABLED,
  CI,
  ENVIRONMENT,
  IAP_ENABLED,
  PRODUCTION_ONLY,
} from '@/utils/env.util'
import { chromium } from 'playwright-extra'

import { log } from '@/utils/playwright-config.util'
import { test as setup } from '@playwright/test'

setup('@setup @staging Setting up staging authentication', async () => {
  if (PRODUCTION_ONLY || (CI && ENVIRONMENT !== 'staging')) {
    return
  }

  // 2min per user to login
  const numberOfUsers = Object.keys(USERS_SHOULD_LOGIN_APP).length
  const timeout = numberOfUsers * 2 * 60 * 1_000
  setup.setTimeout(timeout)

  if (!CI) {
    const isConfigValid = await checkConfig('staging')
    if (isConfigValid) {
      log('🔑 Cached auth files detected; skipping setup staging auth.', 'staging')
      return
    }
  }

  log('🔑 Logging you in with user data stored in the `src/auth/users.ts` file', 'staging')

  const browser = await chromium.launch({ headless: false })

  // stop staging login process when don't need auth
  if (!AUTH_SETUP_ENABLED) {
    await browser.close()
    return
  }

  const runCode = async () => {
    const processUser = async (userKey: UserKey) => {
      const context = await browser.newContext({
        storageState: IAP_ENABLED ? STORAGE_STATE_IAP : undefined,
      })

      const page = await context.newPage()

      const user = USERS[userKey]
      await googleLogin(page, user, 'staging')

      await context.close()
    }

    if (CI || AUTH_LOGIN_SEQUENTIAL) {
      for (const key of USERS_SHOULD_LOGIN_APP) {
        await processUser(key)
      }
    } else {
      const promises = USERS_SHOULD_LOGIN_APP.map(processUser)
      await Promise.all(promises)
    }
  }

  await runCode()

  if (!CI) generateConfigAuth('staging')

  await browser.close()
})
