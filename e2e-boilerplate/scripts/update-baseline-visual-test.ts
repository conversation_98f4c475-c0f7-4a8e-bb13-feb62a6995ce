import { applyChangesOnRemote, checkoutNewBranch } from '@wartek-id/toolbox-webex-helper'
import shelljs from 'shelljs'

main()

/**
 * Automates the process of updating visual tests within a CI pipeline.
 *
 * Detailed Workflow:
 * 1. Checks out a new branch specifically for the visual test updates.
 * 2. Executes visual tests based on the environment configuration.
 * 3. If changes are detected, it updates the tests in a new branch.
 * 4. Automatically pushes the changes to the remote repository.
 * 5. Creates a merge request on GitLab for the updates.
 *
 * @async
 * @function main Main function to handle visual test updates.
 */
async function main() {
  // Create a new branch for the visual test updates
  const branch = checkoutNewBranch('update-visual-test')

  let stderr

  // Run the visual tests, optionally filtering them based on environment variables
  if (process.env.TEST_RUNS) {
    stderr = shelljs.exec(`xvfb-run npm run test:snapshot -- --grep "${process.env.TEST_RUNS}"`).stderr
  } else {
    stderr = shelljs.exec('xvfb-run npm run test:snapshot').stderr
  }

  // Environment variable to capitalize the first letter
  const env = process.env.ENVIRONMENT.charAt(0).toUpperCase() + process.env.ENVIRONMENT.slice(1)

  // Check if no visual tests were found and log a message to skip the update
  if (stderr.includes('No tests found')) {
    console.info(`No visual test found in current environment ${env}. Skip updating visual test.`)
    return
  }

  // Apply changes to the remote repository and create a merge request
  await applyChangesOnRemote({
    branchName: branch,
    commitMessage: `[automated ${env}]: update visual test`,
    directory: 'tests',
    mergeRequest: {
      description:
        'This is an automated merge request to update snapshot baseline for visual tests, please review the changes and merge it if it looks good.',
      titleMessage: 'Update Baseline Snapshots For Visual Tests',
      titlePrefix: `[Automated][${env}]`,

      // Define the reviewers for the merge request
      // reviewerIds: [12345, 67890, ...],
    },
  })
}
