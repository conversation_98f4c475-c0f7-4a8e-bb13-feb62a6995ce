import { USERS, User<PERSON><PERSON> } from '@/constants/users.constant'
import { ENVIRONMENT, PRODUCTION_BASE_URL } from '@/utils/env.util'
import { Page, PageScreenshotOptions, TestInfo } from '@playwright/test'
import appRootPath from 'app-root-path'
import fs from 'fs-extra'
import { CONFIG_AUTH_CACHE } from './auth-cache.helper'

type ConnectXrayConfig = {
  /** Jira Test Key like: CUR-123, SQA-123, etc */
  key: string
}

export const connectXray = (testInfo: TestInfo, { key }: ConnectXrayConfig) =>
  testInfo.annotations.push({ type: 'test_key', description: key })

export const screenshot = async (
  page: Page,
  testInfo: TestInfo,
  screenshotOptions?: PageScreenshotOptions & { id?: string }
) => {
  const projectName = testInfo.project.name
  const title = testInfo.title
  const id = screenshotOptions?.id ? `${screenshotOptions.id}-` : ''

  const fullPath = `${appRootPath}/screenshots/${projectName}/${id}${title}.png`
  const buffer = await page.screenshot({ path: fullPath, ...screenshotOptions })

  await testInfo.attach('screenshot', { body: buffer, contentType: 'image/png' })

  testInfo.annotations.push({
    type: 'test_evidence',
    description: buffer.toString('base64'),
  })
}

/**
 * @description
 * Detecting the environment based on the URL address.
 * Matched with PRODUCTION_BASE_URL value from env
 *
 * @param {Object} page - Page instance from the test cases.
 * @returns {boolean} - `true` if it's production, otherwise `false`.
 *
 * @example
 * isProduction(page)
 * // return `true` or `false`
 *
 */
export const isProduction = (page: Page) => {
  return page.url().includes(PRODUCTION_BASE_URL)
}

/**
 * @description
 * Extending `isProduction` but returning the environment name.
 *
 * @param {Object} page - Page instance from the test cases.
 * @returns {string} - `production` if it's production, otherwise `staging`.
 *
 * @example
 * const env = getTestEnv(page)
 * // return 'production' or 'staging'
 *
 */
export const getTestEnv = (page: Page) => {
  return isProduction(page) ? 'production' : 'staging'
}

export const getAuthFile = (pathAuthFile: string, env?: typeof ENVIRONMENT) => {
  if (!env) {
    return pathAuthFile
  }

  return pathAuthFile.replace('.json', `.${env}.json`)
}

export const authStorageStateCloneFromDev = () => {
  const configFileName = CONFIG_AUTH_CACHE.split('/').pop()

  if (fs.existsSync('.auth-dev')) {
    fs.copy('.auth-dev', '.auth', {
      filter: (src) => !src.includes(configFileName),
      /** ignore errors caused by parallel runs */
    }).catch(() => {})
  }
}

export const switchUserAccount = async (page: Page, testInfo: TestInfo, userKey: UserKey) => {
  // To ensure we are using "valid" authentication files, allowing us to switch user accounts as many times as we want.
  authStorageStateCloneFromDev()

  await page.close()

  const isTestForProduction = testInfo.project.name.includes('production')
  const authFile = getAuthFile(USERS[userKey].pathFile, isTestForProduction ? 'production' : 'staging')

  const browser = await page.context().browser().newContext({ storageState: authFile })

  return await browser.newPage()
}

/**
 * @description
 * Running function conditionally based on the environment.
 * Can be used to remove the conditional logic/assertion from the test cases.
 *
 * @param {Object} p - Object with the page and the functions to be executed.
 * @param {string} p.page - Page instance from the test cases.
 * @param {string} p.staging - Async function to be executed on staging environment.
 * @param {string} p.production - Async function to be executed on production environment.
 * @returns {Promise<void>} - Promise with void.
 *
 * @example
 * // Assertion text based on the environment
 * await execFunctionOnEnv({
 *   page,
 *   production: async () => {
 *     expect(
 *       page.getByText(process.env.VALUE_IN_STAGING)
 *     ).toBeInViewport()
 *   },
 *   staging: async () => {
 *     expect(
 *       page.getByText(process.env.VALUE_IN_PROD)
 *     ).toBeInViewport()
 *   }
 * })
 */
export const execFunctionOnEnv = async ({
  page,
  staging,
  production,
}: {
  page: Page
  production: () => Promise<void>
  staging: () => Promise<void>
}) => {
  isProduction(page) ? await production() : await staging()
}
