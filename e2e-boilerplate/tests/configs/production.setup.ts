import { test as setup } from '@playwright/test'
import { chromium } from 'playwright-extra'

import { AUTH_LOGIN_SEQUENTIAL, AUTH_SETUP_ENABLED, CI, ENVIRONMENT, STAGING_ONLY } from '@/utils/env.util'
import { USERS, USERS_SHOULD_LOGIN_APP } from 'src/constants/users.constant'

import { googleLogin } from '@/auth/google-login.helper'
import { checkConfig, generateConfigAuth } from '@/helpers/auth-cache.helper'

import { log } from '@/utils/playwright-config.util'

if (!(STAGING_ONLY || (CI && ENVIRONMENT !== 'production'))) {
  setup.describe.configure({ mode: CI || AUTH_LOGIN_SEQUENTIAL ? 'serial' : 'parallel' })

  for (const userKey of USERS_SHOULD_LOGIN_APP) {
    // eslint-disable-next-line no-empty-pattern
    setup(`@setup @production Setting up production authentication for ${userKey}`, async ({}, testInfo) => {
      // 4min per user to login
      testInfo.setTimeout(4 * 60 * 1000)

      if (!CI) {
        const isConfigValid = await checkConfig('production', userKey)
        if (isConfigValid) {
          log(`🔑 Cached auth files detected for ${userKey}; skipping setup production auth.`, 'production')
          return
        }
      }

      log(`🔑 Logging in ${userKey} with user data stored in the 'src/auth/users.ts' file`, 'production')

      const browser = await chromium.launch({ headless: false })

      // stop production login process when don't need auth
      if (!AUTH_SETUP_ENABLED) {
        await browser.close()
        return
      }

      const context = await browser.newContext()
      const page = await context.newPage()

      const user = USERS[userKey]
      await googleLogin(page, user, 'production')

      await context.close()

      if (!CI) generateConfigAuth('production', userKey)

      await browser.close()
    })
  }
}
