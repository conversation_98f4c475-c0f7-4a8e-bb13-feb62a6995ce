import { USER_IAP } from '@/constants/users.constant'
import { validateUsersTesting } from '@/helpers/auth-check.helper'
import { authStorageStateCloneFromDev } from '@/helpers/global.helper'
import { AUTH_SETUP_ENABLED, CI, IAP_ENABLED, USE_DYNAMIC_CONSTANTS_GSHEET } from '@/utils/env.util'
import { exec } from 'shelljs'

let watcherStarted = false

export default async function globalSetup() {
  if (!watcherStarted && USE_DYNAMIC_CONSTANTS_GSHEET) {
    exec('./node_modules/.bin/tsx scripts/write-kv-constants.ts && ./node_modules/.bin/prettier gsheet-kv.ts --write')

    watcherStarted = true
  }

  authStorageStateCloneFromDev()

  const isEmail = (email: string) => email.includes('@')

  if (IAP_ENABLED && !isEmail(USER_IAP.email)) {
    throw new Error('Please provide a valid email for IAP user. Check src/auth/users.ts')
  }

  if (CI) {
    const url = `https://wartek-id.gitlab.io/-/e2e-web/${process.env.CI_PROJECT_NAME}/-/jobs/${process.env.CI_JOB_ID}/artifacts/playwright-report/index.html`
    console.log(`📝 Test report will be accessible after the test is done at: ${url}`)
  }

  if (AUTH_SETUP_ENABLED) validateUsersTesting()
}
